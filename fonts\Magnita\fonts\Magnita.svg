<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170924 at Mon Jan 13 08:32:01 2020
 By www-data
Copyright (c) 2019 by <PERSON><PERSON><PERSON>. All Rights Reserved
</metadata>
<defs>
<font id="Magnita" horiz-adv-x="0" >
  <font-face 
    font-family="Magnita"
    font-weight="400"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 0 5 0 0 0 0 0 0 0"
    ascent="1638"
    descent="-410"
    x-height="1286"
    cap-height="1919"
    bbox="-62 -633 2903 2657"
    underline-thickness="150"
    underline-position="-292"
    unicode-range="U+001D-E270"
  />
<missing-glyph horiz-adv-x="974" 
d="M0 0v1434h824v-1434h-824zM50 50q353 159 724 0v1334q-384 -118 -724 0v-1334zM319 334l57 58q11 10 20 10q10 0 22 -11l56 -60q9 -11 9 -20q0 -11 -10 -22l-56 -62q-14 -14 -21 -14q-11 0 -20 10l-61 71q-7 9 -7 18q0 10 11 22zM313 1191q53 30 112 30q92 0 148 -47.5
t56 -127.5q0 -49 -23 -98.5t-87 -129.5q-63 -80 -99 -132t-36 -99q0 -38 24 -133h-32q-35 97 -35 148q0 76 81.5 218t81.5 217q0 57 -33 90t-91 33q-8 0 -16 -1l32 -51q7 -12 5 -21q-2 -11 -14 -20l-66 -50q-17 -11 -24 -10q-10 2 -17 14l-47 81q-5 10 -3 19q1 10 28.5 32.5
t54.5 37.5z" />
    <glyph glyph-name=".notdef" horiz-adv-x="974" 
d="M0 0v1434h824v-1434h-824zM50 50q353 159 724 0v1334q-384 -118 -724 0v-1334zM319 334l57 58q11 10 20 10q10 0 22 -11l56 -60q9 -11 9 -20q0 -11 -10 -22l-56 -62q-14 -14 -21 -14q-11 0 -20 10l-61 71q-7 9 -7 18q0 10 11 22zM313 1191q53 30 112 30q92 0 148 -47.5
t56 -127.5q0 -49 -23 -98.5t-87 -129.5q-63 -80 -99 -132t-36 -99q0 -38 24 -133h-32q-35 97 -35 148q0 76 81.5 218t81.5 217q0 57 -33 90t-91 33q-8 0 -16 -1l32 -51q7 -12 5 -21q-2 -11 -14 -20l-66 -50q-17 -11 -24 -10q-10 2 -17 14l-47 81q-5 10 -3 19q1 10 28.5 32.5
t54.5 37.5z" />
    <glyph glyph-name="uni0000" 
 />
    <glyph glyph-name="uni0000" 
 />
    <glyph glyph-name="uni0009" horiz-adv-x="508" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="695" 
 />
    <glyph glyph-name="space" unicode="&#xa0;" horiz-adv-x="695" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="371" 
d="M0 221v0h221v-221h-221v221zM180 442v0h-138l-42 1477h221z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="643" 
d="M0 1749v0v170h188v-103q0 -33 -6 -64q-34 -153 -172 -240l-10 19q9 9 25.5 29t30.5 47.5t14 57.5t-19.5 53t-50.5 31zM305 1919v0h188v-103q0 -33 -6 -64q-33 -153 -171 -240l-11 19q10 9 26.5 29t30.5 47.5t14 57.5t-20 53t-51 31v170z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1678" 
d="M414 635v0h-414l37 138h414l74 276h-414l37 138h414l111 414h138l-111 -414h276l111 414h138l-111 -414h414l-37 -138h-414l-74 -276h414l-37 -138h-414l-111 -414h-138l111 414h-276l-111 -414h-138zM589 773v0h276l74 276h-276z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1203" 
d="M939 808v0l1 -1q51 -57 80 -129t29 -154q0 -62 -17 -119q-40 -133 -147.5 -216t-252.5 -87h-1h-63v-184h-83v184h-395l-90 230l158 -127v0v0l2 -1q80 -66 185 -66h140v814l-285 286q-90 94 -90 225q0 77 32 142v0q41 84 119.5 135t175.5 51q1 0 2 -1h2v1h44v210h83v-210
h395l90 -231l-157 127q-1 0 -1 1q-80 66 -187 66h-140v-575zM371 1376v0l114 -114v489q-79 -15 -130.5 -75t-51.5 -141q0 -94 68 -159zM568 140v0q123 14 204 102t81 210q0 83 -40 153v0v0q-6 11 -14 22q-5 8 -12 17t-14 17.5t-12 14l-5 5.5l-188 189v-730z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1589" 
d="M337 1025v0q-93 0 -169.5 57.5t-122 154.5t-45.5 214q0 118 45.5 214.5t122 154t169.5 57.5t169.5 -57.5t122 -154t45.5 -214.5q0 -117 -45.5 -214t-122 -154.5t-169.5 -57.5zM337 1836v0q-82 0 -140.5 -112.5t-58.5 -272.5q0 -159 58.5 -271.5t140.5 -112.5
q83 0 141 112.5t58 271.5q0 160 -58 272.5t-141 112.5zM1102 894v0q93 0 169.5 -57.5t122 -154.5t45.5 -214q0 -118 -45.5 -214.5t-122 -154t-169.5 -57.5t-169.5 57.5t-122 154t-45.5 214.5q0 117 45.5 214t122 154.5t169.5 57.5zM1102 83v0q82 0 140.5 112.5t58.5 272.5
q0 159 -58.5 271.5t-140.5 112.5q-83 0 -141 -112.5t-58 -271.5q0 -160 58 -272.5t141 -112.5zM1194 1919v0h159l-1108 -1919h-159z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1786" 
d="M1590 543v0q46 -75 46 -168q0 -58 -19 -110q-7 -21 -18 -41q-8 -16 -18 -31q-45 -65 -114 -99q-52 -25 -113 -25q-69 0 -127.5 33t-97.5 90l-33 46q-103 -133 -264 -196q-105 -42 -224 -42t-225 42q-89 34 -162 93q-103 82 -162 199t-59 254q0 189 112 339
q89 121 227 187q52 25 110 41l-18.5 25.5t-42 59t-42 59.5t-20.5 29q-67 94 -67 212q0 89 39 165q58 114 175 172q49 24 104 35q4 1 8 1l26 4q20 2 40 2q92 0 171 -38l7 -4q97 -48 155.5 -137.5t58.5 -199.5q0 -9 -1 -17v-7v-5q-7 -68 -40 -121.5t-82 -92.5q-6 -6 -13 -11
q-40 -30 -85 -53q-36 -18 -72 -31q-26 -10 -52 -18l682 -966v1q33 -47 86 -47q46 0 78 36q9 11 16 25q13 25 13 55q0 39 -21 69q-1 1 -1 2q-1 2 -2 3l-497 703h363v-41h-165zM446 1540v0l62 -87l118 -167l55 -78q1 0 3 1q1 0 3 1q69 29 110 76q14 16 25 34q61 102 53 223.5
t-53 209.5v0q-1 1 -1 2l-1 1q-5 10 -11 20q0 1 -1 2v0q-67 99 -182 99h-11l-24 -2v-1q-4 0 -8 -1q-75 -16 -124 -74.5t-49 -137.5q0 -67 37 -121h-1zM769 168v0q163 0 295 81q8 5 16 11l-616 872q-117 -67 -180 -171.5t-63 -269.5v0v-6q0 -143 74 -260t198.5 -187t275.5 -70
z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="338" 
d="M0 1919v0h188v-103q0 -33 -6 -64q-33 -153 -171 -240l-11 19q10 9 26.5 29t30.5 47.5t14 57.5t-20 53t-51 31v170z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="564" 
d="M221 960v0q0 -281 39 -546t113 -508q19 -64 41 -126h-41v0q-85 120 -152 253q-106 209 -163.5 442.5t-57.5 484.5q0 250 57.5 483.5t163.5 442.5q67 133 152 253v0h41q-22 -62 -41 -126q-74 -242 -113 -507.5t-39 -545.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="564" 
d="M193 960v0q0 -281 -39 -546t-113 -508q-19 -64 -41 -126h41v0q85 120 152 253q107 209 164 442.5t57 484.5q0 250 -57 483.5t-164 442.5q-67 133 -152 253v0h-41q22 -62 41 -126q74 -242 113 -507.5t39 -545.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="985" 
d="M835 1573v0l-340 -74l297 -180l-150 -162l-225 313l-224 -313l-150 162l297 180l-340 74l89 202l292 -206l-72 350h221l-70 -347l286 203z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1141" 
d="M991 752v0v-220h-385v-385h-221v385h-385v220h385v385h221v-385h385z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="371" 
d="M0 221v0h221v-134q0 -42 -7 -82q-40 -200 -202 -314l-12 25q11 12 30.5 38t36 62t16.5 75t-23.5 69t-59.5 40v221z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="1140" 
d="M990 706v0h-990v-220h990v220z" />
    <glyph glyph-name="hyphen" unicode="&#xad;" horiz-adv-x="1140" 
d="M990 706v0h-990v-220h990v220z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="370" 
d="M220 0v0v221h-220v-221h220z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="1574" 
d="M1203 2001v0l-1203 -2083h220l1204 2083h-221z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1667" 
d="M758 1919v0q158 0 295.5 -75t241.5 -206.5t163 -305.5t59 -373q0 -198 -59 -372t-163 -305.5t-241.5 -206.5t-295.5 -75q-157 0 -294.5 75t-241.5 206.5t-163 305.5t-59 372q0 199 59 373t163 305.5t241.5 206.5t294.5 75zM758 42v0q149 0 271 123.5t194 332t72 461.5
q0 254 -72 462.5t-194 332t-271 123.5q-148 0 -270 -123.5t-194.5 -332t-72.5 -462.5q0 -253 72.5 -461.5t194.5 -332t270 -123.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="674" 
d="M434 128v0l90 -128v0h-401v0l90 128v1551l-213 -136v98l434 278v-1791z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1274" 
d="M289 42v0h561l274 220l-102 -262h-28h-994l792 1231q15 20 28 44q55 103 55 246q0 147 -58.5 251.5t-140.5 104.5v0h-117q-63 0 -114.5 -55t-82 -147t-30.5 -205h-221q0 124 60.5 226t162 162.5t225.5 60.5v0h117v0q131 0 237 -67.5t163 -178.5q48 -93 48 -203
q0 -121 -58 -221l-27 -41z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1439" 
d="M1289 600v0q0 -166 -81 -302t-217 -217t-302 -81v0h-89q-166 0 -302 81t-217 217t-81 302h221q0 -154 51 -281t137 -202t191 -75h89q105 0 191 75t137 202t51 281t-51 281t-137 202t-191 75h-317l442 719h-429l-275 -220l102 262h887l-442 -719h32q166 0 302 -81
t217 -217t81 -302z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1723" 
d="M1573 545v0h-220v-417l90 -128v0h-401v0l90 128v417h-1132l1132 1374h221v-1153h220v-221zM1132 766v0v1088l-896 -1088v0h896z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1439" 
d="M1289 600v0q0 -166 -81 -302t-217 -217t-302 -81v0h-89q-166 0 -302 81t-217 217t-81 302h221q0 -154 51 -281t137 -202t191 -75h89q105 0 191 75t137 202t51 281t-51 281t-137 202t-191 75h-468v761h856l102 -262l-275 220h-463v-677h248q166 0 302 -81t217 -217
t81 -302z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1430" 
d="M680 1200v0q166 0 302 -81t217 -217t81 -302q0 -165 -81 -301.5t-217 -217.5t-302 -81h-80q-166 0 -302 81t-217 217.5t-81 301.5v44v22q0 346 107 629.5t286 451t401 172.5l-23 -45q-179 -21 -315 -232.5t-198 -548.5q73 51 160 79t182 28h80zM680 42v0q105 0 191 75
t137 202t51 281t-51 281t-137 202.5t-191 75.5h-80q-101 0 -185 -71t-135.5 -190t-57.5 -267v-11v-20h-1q0 -154 51 -281t137 -202t191 -75h80z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1361" 
d="M391 128v0v0l32 -128v0h-423v0l148 128l801 1749h-587l-274 -220l102 262h1021z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1408" 
d="M1258 523v0q0 -144 -70.5 -263t-189 -189.5t-263.5 -70.5v0h-212v0q-145 0 -263.5 70.5t-189 189.5t-70.5 263q0 125 53.5 231t145 179t209.5 100q-133 51 -215.5 166t-82.5 263q0 118 55 217.5t148 162t210 74.5v3h212v-2q118 -12 211 -75t148.5 -162t55.5 -218
q0 -148 -83 -263.5t-216 -165.5q118 -28 209.5 -100.5t144.5 -179t53 -230.5zM567 1877v0q-100 -4 -168 -123.5t-68 -291.5q0 -173 68 -292.5t168 -123.5h126q100 4 168 123.5t68 292.5q0 172 -68 291.5t-168 123.5h-126zM735 42v0q84 0 152.5 65t109 174t40.5 242
t-40.5 242t-109 174t-152.5 65h-212q-84 0 -152.5 -65t-109 -174t-40.5 -242t40.5 -242t109 -174t152.5 -65v0h212v0z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1430" 
d="M1280 1321v0v-46v-22q0 -260 -62 -487t-171.5 -399t-254 -269.5t-308.5 -97.5l22 44q180 20 317 232t199 550q-73 -51 -160 -79t-182 -28h-80q-166 0 -302 81t-217 217t-81 302t81 302t217 217t302 81h80q165 0 301 -80.5t216 -215.5t82 -302h1zM1058 1288v0v11v20h1
q0 154 -51.5 281t-137.5 202t-190 75h-80q-105 0 -191 -75t-137 -202t-51 -281t51 -281t137 -202t191 -75h80q101 0 185 70.5t135.5 189.5t57.5 267z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="371" 
d="M0 75v0v221h221v-221h-221zM0 892v0h221v-221h-221v221z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="371" 
d="M0 892v0h221v-221h-221v221zM0 75v0v221h221v-133q0 -43 -7 -83q-40 -200 -202 -313l-12 24q11 12 30.5 38t36 62t16.5 76q0 38 -23.5 68t-59.5 40z" />
    <glyph glyph-name="semicolon" unicode="&#x37e;" horiz-adv-x="371" 
d="M0 892v0h221v-221h-221v221zM0 75v0v221h221v-133q0 -43 -7 -83q-40 -200 -202 -313l-12 24q11 12 30.5 38t36 62t16.5 76q0 38 -23.5 68t-59.5 40z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="1415" 
d="M1265 1301v0v-155l-963 -495l963 -496v-155l-1265 651z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1141" 
d="M0 892v0h991v-138h-991v138zM0 243v0v138h991v-138h-991z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="1415" 
d="M0 1301v0v-155l963 -495l-963 -496v-155l1265 651z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="1101" 
d="M332 0v0v221h221v-221h-221zM730 1823v0q101 -75 161 -190t60 -253q0 -155 -77 -282q-50 -86 -126 -147l-18 -14q-1 0 -2 -1q-2 -1 -3 -2l-2 -2q-110 -88 -174 -213.5t-64 -271.5h-1v-5h-83v3v3q0 199 83 358q10 20 21 39q35 58 78 105l10 10l1 1l1 1v0q62 68 98.5 177.5
t36.5 240.5q0 137 -40 250t-107 180t-148 67v0h-1q-114 -1 -210 -54t-160 -144l-64 -10q86 141 235 208q78 35 167 42h66q89 -7 168 -42q50 -22 94 -54z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1998" 
d="M1360 1833v0q216 -109 345 -301t143 -435q0 -25 -1 -49q-13 -179 -83 -306q-54 -100 -124.5 -167.5t-142.5 -102.5q-86 -42 -161 -42q-9 0 -18 1q-41 7 -72 18q-30 10 -53 22q-69 37 -89 89l-36 -8l-136 -28h-2h-1q-32 -5 -66 1h-2q-1 1 -2 1q-3 1 -7 2q-2 0 -4 1
q-3 0 -5 1v0h-1q-13 4 -25 8q-2 1 -4 1q-2 1 -5 2q-4 2 -8 3q-4 2 -9 4q-17 7 -33 16v0q-119 67 -177.5 193t-33.5 276q39 183 176.5 284t311.5 74l16 -4q92 -20 168 -65l15 76h141l-135 -684v0q-18 -84 -12.5 -150t63.5 -87l0.5 -0.5l0.5 -0.5q81 -18 186 33
q58 28 115.5 79.5t106 125t78 169.5t29.5 214q-7 192 -120.5 373.5t-280.5 269.5q-195 93 -402 66t-386.5 -157.5t-289.5 -347.5q-105 -219 -99.5 -440.5t110.5 -402.5t297 -279q142 -68 293 -71v0h5q31 -1 61 1q1 1 2 1h5q157 7 299.5 65t268.5 186l10 -39
q-216 -234 -508 -301t-574 65q-225 115 -350 321.5t-136.5 457t106.5 495.5q123 242 328.5 385.5t445.5 170t468 -82.5zM1195 1198v0v0q-51 139 -165 165q-110 16 -204.5 -85t-129.5 -273q-24 -138 6 -250q21 -82 66 -136q15 -18 32 -31v-1h1q30 -27 71 -35v0v0q29 -5 55 0
q3 1 4 1q12 2 43 9q15 3 31 6l19 4q1 0 2 1q5 1 9 2l39 7l9 2l3 1l9 2q-9 39 -4 80v0l22 113q1 4 1 8v0z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="2161" 
d="M1866 128v0l145 -128v0h-451v0l56 79l-161 370h-1125l-139 -321l34 -128v0h-225v0l145 128l725 1664l-55 127h271zM366 532v0h1053l-526 1208z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1603" 
d="M973 959v0q133 0 241.5 -64.5t173.5 -173.5t65 -241q0 -133 -65 -241.5t-173.5 -173.5t-241.5 -65h-973v0l90 128v1663l-90 128v0h115.5h268.5h269.5h129.5q138 0 251 -67.5t180.5 -180.5t67.5 -251q0 -156 -85.5 -279t-223.5 -182v0zM338 1877v0v-897h482v0q80 4 132 60
q62 68 97 154t35 184q0 125 -56 230t-151 174q-129 95 -293 95h-246zM747 42v0q126 0 230.5 61.5t166.5 166t62 230.5q-9 149 -92 262t-215 165q-31 12 -65 12h-74h-170.5h-170h-81.5v-897h409z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1854" 
d="M1636 243v0l24 -34q-132 -99 -294 -154t-342 -55q-212 0 -398 75t-326.5 206.5t-220 305.5t-79.5 372q0 199 79.5 373t220 305.5t326.5 206.5t398 75q9 0 19 -1h7v0v0h10q8 1 16 1q176 0 335.5 -47.5t292.5 -133.5v-354q-65 147 -168 258t-235 173t-281 62
q-160 0 -300 -71.5t-245.5 -197t-166 -292.5t-60.5 -357t60.5 -356.5t166 -292t245.5 -197t300 -71.5h2h2v0q173 0 329 53t283 148v0z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1755" 
d="M582 1919v0q212 0 397.5 -75t326 -206.5t220 -305.5t79.5 -373q0 -198 -79.5 -372t-220 -305.5t-326 -206.5t-397.5 -75h-582v0l90 128v1663l-90 128v0h582zM585 42v0q160 0 300 71.5t246 197t166 292t60 356.5t-60 357t-166 292.5t-246 197t-300 71.5h-1h-2v0h-244
v-1835h244v0h1h2z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1427" 
d="M338 42v0h665l274 220l-102 -262h-1175v0l90 128v1663l-90 128v0h1175l102 -262l-274 220h-665v-918h691v-82h-691v-835z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1427" 
d="M339 1877v0v-918h690v-82h-690v-749l90 -128v0h-429v0l90 128v1663l-90 128v0h1175l102 -262l-274 220h-664z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1944" 
d="M1704 128v0l90 -128v0h-338v90q-201 -90 -433 -90q-212 0 -397.5 75t-326 206.5t-220 305.5t-79.5 372q0 199 79.5 373t220 305.5t326 206.5t397.5 75q10 0 20 -1h7v0h9q9 1 17 1q176 0 335.5 -47.5t292.5 -133.5v-354q-65 147 -168.5 258t-235 173t-280.5 62
q-160 0 -300 -71.5t-246 -197t-166 -292.5t-60 -357t60 -356.5t166 -292t246 -197t300 -71.5h2h1v0q233 0 433 94v741h-433v82h681v-831z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1855" 
d="M1615 1791v0v-1663l90 -128v0h-428v0l90 128v790h-1028v-790l90 -128v0h-429v0l90 128v1663l-90 128v0h429v0l-90 -128v-832h1028v832l-90 128v0h428v0z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="579" 
d="M339 128v0l90 -128v0h-429v0l88 124v1671l-88 124v0h426v0l-87 -124v-1667z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="1432" 
d="M1282 1914v0v0l-90 -127v-1175q0 -148 -64 -273.5t-174 -211t-251 -114.5q-60 -13 -123 -13v0h-0.5h-0.5q-12 0 -24 1h-3q-12 0 -24 1q-11 1 -23 3h-5q-11 2 -23 4t-25 4l-24 6h-1l-1 1q-10 2 -21 6h-1q-72 22 -136 59q-21 13 -41 27q-1 0 -2 1q-119 86 -188 216
q-22 41 -37 85l34 29q37 -117 116.5 -207.5t190 -142.5t238.5 -52v1h0.5h0.5v0q100 0 182.5 77t131.5 206.5t49 286.5v1175l-90 127v0h429z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1991" 
d="M1661 128v0l180 -128v0h-485v0l45 64l-607 860v0l-456 -644v-152l90 -128v0h-428v0l90 128v1663l-90 128v0h428v0l-90 -128v-1439l1051 1485l-33 82v0h231v0l-180 -128l-461 -652v-1z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1359" 
d="M934 42v0l275 220l-102 -262h-1107v0l90 128v1663l-90 128h428l-90 -128v-1749h596z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="2624" 
d="M2384 1791v0v-1663l90 -128v0h-429v0l90 128v1694l-871 -1822l-1133 1719v-1591l90 -128v0h-221v0l90 128v1654l-90 137v0h297l1066 -1617l772 1617h339v0z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1903" 
d="M1753 1919v0v0l-90 -128v-1791h-2h-305l-1225 1733v-1605l90 -128v0h-221v0l90 128v1663l-90 128v0h304l1318 -1863v1735l-90 128v0h221z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="2204" 
d="M1027 1919v0q213 0 399.5 -75t327 -206.5t220.5 -305.5t80 -373q0 -198 -80 -372t-220.5 -305.5t-327 -206.5t-399.5 -75q-212 0 -398.5 75t-327.5 206.5t-221 305.5t-80 372q0 199 80 373t221 305.5t327.5 206.5t398.5 75zM1027 40v0q162 0 303 72t248 198t167.5 292.5
t60.5 356.5q0 191 -60.5 357.5t-167.5 292.5t-248 197.5t-303 71.5q-161 0 -302 -71.5t-248 -197.5t-167.5 -292.5t-60.5 -357.5q0 -190 60.5 -356.5t167.5 -292.5t248 -198t302 -72z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1541" 
d="M770 1919v0q172 0 312.5 -84t224.5 -224.5t84 -312.5q0 -171 -84 -311.5t-224.5 -224.5t-312.5 -84h-432v-550l90 -128v0h-428v0l90 128v1663l-90 128v0h770zM770 720v0q103 0 187 78t134 209.5t50 290.5q0 160 -50 291.5t-134 209.5t-187 78h-432v-1157h432z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="2334" 
d="M917 6v0q229 -109 453.5 -131.5t573.5 -15.5q110 1 240 1v-222q-438 -5 -797 101t-856 380v0q-159 83 -278 210.5t-186 288t-67 341.5q0 199 80 373t221 305.5t327 206.5t399 75t399.5 -75t327 -206.5t220.5 -305.5t80 -373q0 -198 -80 -372t-220.5 -305.5t-327 -206.5
t-399.5 -75q-55 0 -110 6zM248 959v0q0 -190 61 -356.5t167.5 -292.5t248 -198t302.5 -72t302.5 72t248 198t167.5 292.5t61 356.5q0 191 -61 357.5t-167.5 292.5t-248 197.5t-302.5 71.5t-302.5 -71.5t-248 -197.5t-167.5 -292.5t-61 -357.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1600" 
d="M1270 128v0l180 -128v0h-484v0l45 64l-434 614h-238v-550l90 -128v0h-428h-1l91 128v1663l-91 128h1h746v-1q172 -4 310 -87t220.5 -223t82.5 -310q0 -148 -64 -275t-174 -213t-251 -118l399 -564v0zM339 720v0h401q102 0 186 78t134 209.5t50 290.5q0 160 -50 291.5
t-134 209.5t-186 78h-401v-1157z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1346" 
d="M1066 802v0h1q0 -1 1 -1q58 -64 90.5 -146.5t32.5 -174.5q0 -71 -19 -135q-46 -151 -168 -245.5t-287 -99.5v0h-615l-102 262v0v0l179 -144v0v0l2 -1q91 -76 211 -76h219q99 0 180 48.5t129 129.5t48 179q0 94 -45 173v1v0q-7 13 -16 25q-5 8 -13 18.5t-16 20.5t-13.5 16
l-5.5 6l-632 633q-103 107 -103 256q0 86 37 161v0q46 95 135 152.5t200 57.5h2h2h1h593l102 -261l-179 144h-1q-91 76 -212 76h-207v0v0h-2h-2q-103 0 -176 -73t-73 -176q0 -106 77 -180l144 -144z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1843" 
d="M971 1872v0v-1749l90 -128v0h-428v0l90 128v1749h-448l-275 -220l102 262h1489l102 -262l-274 220h-448z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1655" 
d="M1505 1916v0v0l-90 -127v-1174q0 -195 -110 -350q-82 -117 -207.5 -187t-278.5 -75l-117 -1q-169 0 -308 83t-221.5 221.5t-82.5 308.5v1174l-90 127v0h428v0l-90 -127v-188v-428.5v-428.5v-189q0 -136 64.5 -249t173 -182.5t243.5 -78.5h5q3 -1 9 -1q137 7 250.5 70
t187.5 168q102 143 102 323v2v2v2v1v1177l-90 127v0h222z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1986" 
d="M1701 1791v0l-590 -1668l51 -123h-269l-750 1791l-143 127v0h449v0l-36 -127l674 -1611l571 1613l-46 125v0h224v0z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="3053" 
d="M2903 1918v0l-144 -127l-704 -1668l37 -123h-259l-414 1386l-533 -1263l36 -123h-259l-535 1791l-128 127v0h440v0l-52 -127l479 -1606l533 1263l-102 343l-128 127v0h439v0l-52 -127l480 -1606l677 1606l-36 127v0h225v0z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1918" 
d="M976 1042v0l612 -915l176 -127h-479v0l46 65l-517 773l-569 -711l-12 -127h-233l192 127l597 747l-613 917l-175 127h479v0l-47 -65l519 -775l571 713l12 127h233l-192 -127z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="2111" 
d="M1105 1046v0v0v-919l90 -127v0h-90h-249h-90v0l90 127v832l-664 832l-192 127v0h498v0l-33 -71l613 -768l668 760l-21 79v0h236v0l-202 -127v0v0v0z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1622" 
d="M317 41v0h880l275 221l-102 -262h-1370l1155 1877h-757l-274 -220l102 261h1246z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="591" 
d="M310 2098v0q-37 0 -63 -26t-26 -64v-2097q0 -38 26 -64t63 -26h131v-42h-441v2360h441v-41h-131z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="1574" 
d="M221 2001v0l1203 -2083h-221l-1203 2083h221z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="591" 
d="M131 2098v0q37 0 63.5 -26t26.5 -64v-2097q0 -38 -26.5 -64t-63.5 -26h-131v-42h441v2360h-441v-41h131z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="874" 
d="M724 1279v0l-362 640l-362 -640h117l245 307l245 -307h117z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1347" 
d="M1197 138v0h-1197v-138h1197v138z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="815" 
d="M313 1919v0l352 -353l-29 -29l-636 382h313z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1252" 
d="M1030 110v0l72 -111v0h-293v116l-180 -73l-5 -2q-65 -26 -128 -33.5t-133 -7.5h-1v0q-148 12 -230 62q-18 10 -32 21q-53 40 -76.5 91t-23.5 105q0 74 36.5 143t95.5 111q32 25 65 43q17 10 33 18q22 11 72 32.5t114 46.5q3 1 61.5 24t135.5 53t136.5 53.5l59.5 23.5v56
q0 43 -4 81q-2 38 -13 74v0v1v0v1v0q-30 91 -105 147t-175 58h-1q-38 0 -73 -9q-155 -45 -208 -196h-151q68 111 180.5 177.5t251.5 69.5h5h9v0v0v0q23 1 45 1q74 0 135 -6v0v0v0h1q5 -1 9 -1q6 -1 11 -2q120 -20 201.5 -104.5t97.5 -205.5q2 -13 3 -27q2 -37 2 -78v-4v-166
v-301v-282zM809 160v0v621l-19 -7q-36 -15 -102 -40.5t-124 -48.5t-71 -28q-16 -7 -34 -16q-53 -24 -100 -56q-9 -7 -16 -12q-46 -36 -75 -81q-38 -59 -46 -134q0 -1 -1 -2v-4q-1 -15 -1 -31q0 -11 1 -22q9 -71 60.5 -136.5t137 -94t195.5 12.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1289" 
d="M661 1272v0q138 -32 245.5 -121.5t170 -221t62.5 -286.5q0 -150 -59 -279t-161.5 -218.5t-234.5 -125.5v0v0q-18 -5 -36 -9v0v0q-9 -2 -18 -3q-18 -3 -37 -5q-7 -1 -13 -1q-4 -1 -7 -1h-3h-4h-3q-6 -1 -12 -1h-14h-103v0h-434l72 111v1697l-72 110v0v0l293 42v-729v-28
l30 12l71 29l59 24q33 14 72 17q5 1 11 1h2h1q6 0 12 -1h4v0h13q7 -1 14 -1q20 -2 40 -5q2 0 5 -1q4 0 8 -1q3 -1 6 -1q3 -1 7 -2l12 -2h1v0v0zM536 41v0q106 0 193 81.5t138.5 218t51.5 302.5q0 153 -44.5 281.5t-119.5 211.5q-19 21 -39 38q-63 53 -136 66h-1q-2 1 -4 1
q-14 3 -29 3h-5h-5v0q-13 0 -25 -2q-18 -3 -34 -9l-10 -4l-83 -34l-34 -14l-57 -23v-1117h243z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1076" 
d="M890 145v0l36 -30l-239 -97q-2 -1 -3 -1q-1 -1 -2 -1q-45 -16 -94 -16h-3q-7 1 -14 1t-14 1h-1l-38 4q-3 1 -6 1q-4 1 -7 2q-4 0 -8 1q-3 1 -5 1q-7 1 -13 3v0v0v0q-27 6 -52 14v0q-125 41 -220.5 130.5t-151 214.5t-55.5 270q0 177 81.5 323t218.5 233t303 87
q12 0 24 -1q162 -5 299 -64v-257q-51 128 -136 204t-187 76q-105 0 -192 -81t-138.5 -217.5t-51.5 -302.5q0 -201 74 -357q53 -111 132 -177v0q26 -21 53 -36h1l1 -1q50 -30 111 -30v0v0q42 0 79 15l6 2q12 5 46.5 19t73.5 30t67 27q19 8 25 10z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1289" 
d="M1067 90v0l72 -111v0h-293v83l-160 -65q-33 -14 -72 -17q-5 -1 -11 -1h-2h-1q-6 0 -12 1h-3h-14q-7 1 -14 1q-20 2 -40 5q-2 0 -5 1q-4 0 -8 1q-3 1 -6 1q-3 1 -7 1q-6 2 -12 3h-1v0v0q-138 32 -245.5 121.5t-170 221t-62.5 286.5q0 150 59.5 279t161.5 218.5t234 125.5
v0v0q18 5 36 9v0v0q9 2 18 3q19 3 37 5q7 1 13 1q4 1 7 1h4h3h3q6 1 12 1h5.5h6h2.5h244v522l-71 111v0v0l292 41v-1849zM846 107v0v1117h-243q-106 0 -193 -81.5t-138.5 -218t-51.5 -302.5q0 -153 44.5 -281.5t119.5 -211.5q19 -21 39 -38q63 -53 136 -66q1 0 1 -1h4
q14 -3 29 -3h5h5v0q13 0 25 2q18 3 34 9q3 1 30.5 12t63 25.5t63 26z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1224" 
d="M861 827v0h-624q-16 -88 -16 -184q0 -201 74 -357q53 -111 132 -177v0q26 -21 53 -36q1 0 1 -1h1q50 -30 111 -30v0v0q42 0 79 15l6 2q38 15 111 45q16 7 32 13q43 18 74 30.5l31 12.5v-45l-214 -87l-25 -10q-2 -1 -3 -1q-1 -1 -2 -1q-45 -16 -94 -16h-3q-7 1 -14 1
t-14 1h-1l-38 4q-3 1 -6 1q-4 1 -7 2q-4 0 -8 1q-3 1 -5 1q-7 1 -13 3v0v0v0q-27 6 -52 14v0q-125 41 -220.5 130.5t-151 214.5t-55.5 270q0 177 74 323t198.5 233t276.5 87v0v0q190 -4 331 -130.5t194 -328.5h-27h-186zM549 1244v0q-104 0 -185 -104t-119 -272h608
q-38 168 -119.5 272t-184.5 104zM549 1286v0q190 -4 331 -130.5t194 -328.5h-837q-16 -88 -16 -184q0 -201 74 -357q53 -111 132 -177v0q26 -21 53 -36q1 0 1 -1h1q50 -30 111 -30v0v0q42 0 79 15l6 2q12 5 45.5 18.5t72 29t66.5 27.5q22 9 28 11l36 -30l-214 -87l-25 -10
q-2 -1 -3 -1q-1 -1 -2 -1q-45 -16 -94 -16h-3q-7 1 -14 1t-14 1h-1l-38 4q-3 1 -6 1q-4 1 -7 2q-4 0 -8 1q-3 1 -5 1q-7 1 -13 3v0v0v0q-27 6 -52 14v0q-125 41 -220.5 130.5t-151 214.5t-55.5 270q0 177 74 323t198.5 233t276.5 87v0v0zM245 868v0h608q-38 168 -119.5 272
t-184.5 104q-104 0 -185 -104t-119 -272z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="1093" 
d="M737 1917v0q110 -5 206 -39v-276q-30 123 -94.5 199t-146.5 76q-72 0 -131 -59.5t-94 -159t-35 -220.5v0v-152h331v-42h-331v-1133l72 -111v0h-365v0l72 111v1133h-221v42h221v152q0 132 65 241.5t174 174t242 64.5q18 0 35 -1z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1405" 
d="M952 1286v0q-83 -5 -128 -70q112 -63 178 -168t66 -231q0 -155 -96 -274.5t-251 -169.5q-79 -26 -166 -26q-88 0 -167 26q-94 29 -167 87q-68 -39 -68 -112v0v-0.5v-0.5q1 -53 37.5 -89.5t88.5 -36.5h568q132 0 235.5 -74.5t148.5 -194.5q24 -65 24 -138v-4v-4
q-2 -68 -25 -130v0q0 -1 -1 -1q-73 -183 -268 -266q-99 -42 -213 -42h-121q-145 0 -270 42q-161 53 -259 155.5t-98 229.5q0 41 10 79h219q-8 -36 -8 -75q0 -104 49.5 -190.5t132.5 -139t187 -58.5q18 -1 37 -1h112q123 0 209.5 86.5t86.5 208.5q0 13 -1 26q-1 10 -3 21
q-23 130 -130 201q-72 48 -162 48h-68.5h-157h-156.5h-73v0v0v0q-86 7 -149 59q-75 62 -91 162q-3 18 -3 37q0 47 16 89q37 97 129 143q-68 64 -106.5 147.5t-38.5 179.5q0 129 69.5 235.5t186 170t258.5 63.5q114 0 213 -42q0 2 1 4q0 3 1 6q33 112 116 182.5t191 70.5h178
v-221h-303v0zM555 389v0q80 0 146.5 57.5t106 155t39.5 215.5t-39.5 215t-106 154.5t-146.5 57.5q-81 0 -147.5 -57.5t-106 -154.5t-39.5 -215t39.5 -215.5t106 -155t147.5 -57.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1404" 
d="M1182 111v0l72 -111v0h-365v0l72 111v675v0v0q0 172 -66.5 297.5t-169.5 155.5v0h-1q-9 3 -19 4q-7 1 -15 1v0h-1q-70 0 -139 -18l-257 -66v-1049l72 -111v0h-365v0l72 111v1697l-72 110v0v0l293 42v-757l247 63q75 20 145 20v0v0q52 -1 101 -11q173 -40 282.5 -172
t113.5 -316v0v-1v0v0v-675z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="515" 
d="M293 1639v0v-243l-243 243h243zM293 111v0l72 -111v0h-365v0l72 111v1064l-72 111v0h72h221v-1175z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="541" 
d="M147 1639v0h244v-243zM87 1286v0v0h292v-1438q0 -126 -58.5 -231t-158 -170.5t-224.5 -77.5l28 55q85 40 139 155.5t54 268.5h-1v1327z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1424" 
d="M1104 1175v0l-467 -532l565 -643h-366v0l30 47l-376 429l-197 -225v-140l71 -111v0h-364v0l72 111v1697l-72 110v0v0l293 42v-1646l756 861v0l26 111v0h199v0l-169 -111h-1z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="515" 
d="M293 111v0l72 -111v0h-365v0l72 111v1697l-72 110v0v0l293 42v-1849z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1959" 
d="M1738 111v0l71 -111v0h-362v0l70 111v853q0 159 -72 246l-2 2q-10 11 -21 18q-22 12 -45 13.5t-43 -1.5q-17 -3 -34 -7q-7 -2 -50 -13t-98 -25.5t-96.5 -25.5l-41.5 -11l-62 -14q63 -80 63 -182v-853l71 -111v0h-362v0l70 111v853q0 159 -72 246l-2 2q-10 11 -21 18
q-22 12 -45 13.5t-43 -1.5q-17 -3 -34 -7q-7 -2 -50 -13t-97 -25t-96 -25l-42 -11v-1050l71 -111h-1h-361v0l70 111v1064l-71 111v0h292v-82l269 69q17 5 42 8.5t51 4.5h22q32 -2 63 -9q11 -3 23 -6q95 -27 160 -92l82 22l257 66l23 6q17 5 42 8.5t51 4.5h22q33 -2 63 -9
q11 -3 23 -6q112 -32 182.5 -115.5t70.5 -191.5v-853z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1402" 
d="M1182 111v0l70 -111v0h-361v0l70 111v675v0v0q0 172 -66.5 297.5t-169.5 155.5v0h-1q-9 3 -20 4q-7 1 -14 1h-0.5h-0.5q-71 0 -139 -18l-244 -62l-14 -4v-1049l71 -111v0h-362v0l71 111v1064l-72 111v0h292v-83l248 63q75 20 145 20v0v0q52 -1 101 -11q114 -25 203 -95
t141 -172t52 -222v0v-5v0v-670z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1356" 
d="M603 1286v0q167 0 303.5 -87t218 -233t81.5 -323q0 -178 -81.5 -324t-218 -232.5t-303.5 -86.5t-303.5 86.5t-218 232.5t-81.5 324q0 177 81.5 323t218 233t303.5 87zM603 41v0q106 0 192.5 81.5t138 218t51.5 302.5t-51.5 302.5t-138 217.5t-192.5 81t-192.5 -81
t-138 -217.5t-51.5 -302.5t51.5 -302.5t138 -218t192.5 -81.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1289" 
d="M661 1272v0q138 -32 245.5 -121.5t170 -221t62.5 -286.5q0 -150 -59 -278.5t-161.5 -218t-234.5 -126.5v0v0q-18 -5 -36 -8v0v0l-18 -4q-18 -3 -37 -5q-6 0 -13 -1h-7q-2 -1 -3 -1h-4h-3h-11q-8 -1 -15 -1h-244v-522l70 -110v0h-361v0l70 110v1697l-72 111v0h293v-83
l160 65q33 15 72 17q5 1 11 1h2h1h13h3q7 -1 13 -1q7 0 14 -1l40 -4q2 -1 5 -1l8 -2q3 0 6 -1q3 0 7 -1q6 -1 12 -3h1v0v0zM536 42v0q106 0 193 81t138.5 217.5t51.5 302.5q0 153 -44 281.5t-120 211.5q-19 21 -39 38q-63 53 -136 67h-1h-4q-14 3 -29 4h-5h-5v0
q-12 -1 -24 -3q-18 -2 -35 -9q-5 -1 -10 -3l-174 -71v-1117h243z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1288" 
d="M1068 -522v0l70 -110v0h-362v0l71 110v605l-160 -65q-34 -14 -72 -17h-11q-2 -1 -3 -1h-1q-6 0 -12 1h-3h-14q-7 1 -14 1q-20 2 -39 5q-3 0 -6 1q-3 0 -7 1q-3 1 -7 1l-6 2l-12 2h-1v0v0q-138 32 -245.5 121.5t-170.5 221t-63 286.5q0 151 59.5 279.5t161.5 218
t234 125.5v0h1q17 5 35 9v0v0q9 2 19 3q18 3 37 5q6 1 13 1q3 1 7 1h3h3h3q6 1 12 1h15h465v-1808zM847 128v0v1117h-243q-106 0 -193 -81.5t-139 -218t-52 -302.5q0 -153 44.5 -281.5t119.5 -211.5q19 -21 40 -38q62 -53 136 -66h1q1 -1 3 -1q15 -3 29 -3h6h5v0q12 0 24 2
q18 3 35 9q4 2 9 4l83 34l34 14z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="1050" 
d="M725 1245v0q135 -69 175 -206h-216q-16 108 -63 166l-2 2t-2 3q-9 9 -20 16h-1q-7 5 -14 8q-3 1 -5 3q-4 1 -7 2l-14 4v0h-1v0v0q-10 2 -21 2q-23 0 -44 -9v0l-197 -75v-1050l70 -111v0h-361v0l70 111v1064l-72 111v0h293v-81l164 63l10 4l7 2q4 2 8 3q32 9 66 9h17h6
q20 -1 39 -4q12 -2 23 -5l24 -6q36 -10 68 -26z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="1034" 
d="M791 494v0q93 -85 93 -210q0 -75 -35 -137q-37 -66 -100.5 -105t-142.5 -42v0h-538l-67 172l163 -131h29.5h73h87h74.5h31h1q2 0 4 1v0v0q81 4 135 59t54 134q0 86 -65 145v0v0l-492 440q0 1 -1 1v0l-13 12q-59 56 -76 137l-2 8v2t-1 3q-3 20 -3 40q0 109 77 186t186 77
v0h443l68 -173l-164 131h-311v0h-1q-54 0 -93 -38.5t-39 -93.5q0 -63 51 -104l574 -514v0z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="741" 
d="M570 43v0l21 -43q-132 0 -241.5 65t-174 174t-64.5 242v763h-111v42l331 331v-331h260v-42h-260v-763v0q0 -173 68 -296.5t171 -141.5z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1402" 
d="M1182 111v0l70 -111v0h-291v83l-248 -63q-74 -20 -144 -20v0h-1q-52 1 -101 11q-114 25 -203 95t-140.5 172t-51.5 222v0v5v0v670l-72 111v0h293v-786v0v0q0 -172 66.5 -297.5t168.5 -155.5h1v0q9 -3 20 -4q7 -1 14 -1h1v0q71 0 139 18l258 66v1049l-72 111v0h293v-1175z
" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1492" 
d="M1342 1286v0l-111 -111l-386 -1092l34 -83h-269l-492 1175v0v0l-118 111v0h413v0l-25 -111l433 -1035l366 1035l-32 111v0h187v0z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="2360" 
d="M2210 1286v0l-111 -111v0v0l-372 -1052l51 -123h-269l-376 897l-288 -814l34 -83h-269l-491 1173l-119 113v0h413v0l-25 -111l433 -1035l288 814l-138 332h269l463 -1107l353 996v0v0l-33 111h187z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1461" 
d="M743 712v0l420 -601l148 -111v0h-411l27 63v0l-333 477l-372 -429l-25 -111v0h-196v0l166 111l402 464l-420 600l-149 111v0h413v0l-28 -64l333 -475l371 428l24 111v0h198v0l-167 -111v0v0z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1469" 
d="M1319 1286v0l-122 -111l-770 -1697l20 -111v0h-187v0l121 111v0l256 563l-515 1134l-122 111v0h386l-21 -111l393 -867l394 867l-21 111v0h188v0z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="1198" 
d="M313 41v0h571l164 131l-68 -172h-980l734 1244h-461l-163 -131l67 173h870z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="813" 
d="M221 984v0q-4 -2 -9 -4q-44 -19 -93 -20q49 -2 93 -21h1l8 -4q176 -90 216 -338q5 -37 5 -77v-301q0 -165 64 -281.5t154 -116.5h3v-41h-3q-100 0 -186 41q-16 8 -32 17q-159 93 -208 275q-13 51 -13 106v291q0 173 -60.5 295.5t-151.5 133.5v0h1h-10v21v20h10h-1v1
q91 10 151.5 132.5t60.5 295.5v291q0 55 13 107q49 181 208 274q16 9 32 17q86 41 186 41h3v-41h-3q-90 0 -154 -116.5t-64 -281.5v-301q0 -39 -5 -77q-40 -247 -216 -338z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="371" 
d="M221 -221v0v2360h-221v-2360h221z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="813" 
d="M442 984v0q5 -2 9 -4q45 -19 93 -20q-48 -2 -93 -21v0q-5 -2 -9 -4q-175 -90 -215 -338q-6 -37 -6 -77v-301q0 -165 -64 -281.5t-154 -116.5h-3v-41h3q100 0 186 41q17 8 32 17q159 93 208 275q13 51 13 106v291q0 173 61 295.5t151 133.5v0v0h9v21v20h-9v0v1
q-90 10 -151 132.5t-61 295.5v291q0 55 -13 107q-49 181 -208 274q-15 9 -32 17q-86 41 -186 41h-3v-41h3q90 0 154 -116.5t64 -281.5v-301q0 -39 6 -77q40 -247 215 -338z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="823" 
d="M598 1724v0q44 51 75 168l-78 27q-23 -57 -45 -79q-11 -11 -34.5 -16.5t-44.5 -5.5q-27 0 -61 13q-34 14 -70 29t-74 29q-38 13 -71 13q-74 0 -119 -51q-45 -50 -76 -169l80 -22q22 58 44 80q12 11 35.5 14.5t44.5 3.5q26 0 59 -13q33 -14 70 -29q37 -16 74 -29t72 -13
q75 0 119 50z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="672" 
 />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1139" 
 />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="657" 
 />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="657" 
 />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="657" 
 />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="657" 
 />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="657" 
 />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="657" 
 />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="657" 
 />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="648" 
 />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="657" 
 />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="657" 
 />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="657" 
 />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="670" 
 />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="657" 
 />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1014" 
 />
    <glyph glyph-name="uni00B2" unicode="&#xb2;" horiz-adv-x="1014" 
 />
    <glyph glyph-name="uni00B3" unicode="&#xb3;" horiz-adv-x="1014" 
 />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="657" 
 />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1180" 
 />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="657" 
 />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="657" 
 />
    <glyph glyph-name="periodcentered" unicode="&#x2219;" horiz-adv-x="657" 
 />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="657" 
 />
    <glyph glyph-name="uni00B9" unicode="&#xb9;" horiz-adv-x="1014" 
 />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="718" 
 />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="657" 
 />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1708" 
 />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1964" 
 />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1708" 
 />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="748" 
 />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="2161" 
d="M1866 127v0l145 -127v0h-451v0l56 79l-161 369h-1125l-140 -321l35 -127v0h-225v0l145 127l725 1665l-55 126h271zM366 531v0h1053l-526 1209zM1338 2304v0l-29 -29l-636 382h254h58z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="2161" 
d="M1865 127v0l146 -127v0h-451v0l56 79l-161 369h-1125l-140 -321l35 -127v0h-225v0l145 127l725 1665l-55 126h271zM366 531v0h1053l-527 1209zM1338 2657v0l-636 -382l-29 29l352 353h59h254z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="2162" 
d="M1866 127v0l146 -127v0h-451v0l56 79l-161 369h-1125l-140 -321l35 -127v0h-226v0l146 127l725 1665l-55 126h271zM367 531v0h1053l-527 1209zM1006 2388v0l-198 -249h-95l217 383l76 135l76 -135l217 -383h-95z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="2162" 
d="M1866 127v0l146 -127v0h-451v0l55 79l-161 369h-1124l-140 -321l35 -127v0h-226v0l146 127l725 1665l-56 126h271zM367 531v0h1052l-526 1209zM815 2354v0q-19 -20 -39 -72l-72 20q28 106 68 152q41 45 107 45q30 0 64 -12t67 -25q32 -14 63 -26q30 -12 54 -12q18 0 39 5
t32 15q19 20 40 70l70 -24q-28 -105 -67 -150q-40 -46 -107 -46q-31 0 -64 12q-34 12 -67 26t-63 26q-29 12 -53 12q-19 0 -40 -3t-32 -13z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="2162" 
d="M1866 127v0l146 -127v0h-451h-1l56 79l-161 369h-1124l-140 -321l34 -127v0h-225v0l146 127l724 1665l-55 126h271zM367 531v0h1052l-526 1209zM931 2493v0v-189h-189v189h189zM1270 2493v0v-189h-189v189h189z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="2161" 
d="M2011 0v0h-451l56 79l-161 369h-1124l-140 -321l34 -127h-225l146 127l724 1665v1q-55 34 -87.5 90.5t-32.5 124.5q0 106 75 181t181 75q105 0 180 -75t75 -181q0 -68 -32.5 -124.5t-87.5 -91.5l725 -1665zM1006 2223v0q-49 0 -83.5 -63t-34.5 -152q0 -88 34.5 -151
t83.5 -63q48 0 82.5 63t34.5 151q0 89 -34.5 152t-82.5 63zM367 531v0h1052l-526 1209z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="2242" 
d="M1818 41v0l76 61l198 159l-102 -261h-1175l90 127v321h-575l-140 -321l35 -127h-225l145 127l725 1665l-55 126v0h1175l102 -261l-274 220h-665v-918h690v-83h-690v-835h665zM366 531v0h539v1180l-13 29z" />
    <glyph glyph-name="OE" unicode="&#x152;" 
 />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1855" 
d="M1020 1877v0q-159 0 -299 -71.5t-246 -197.5t-166 -292.5t-60 -356.5t60 -356.5t166 -292.5t246 -197.5t299 -71.5h2h2v0q174 0 330 53.5t282 148.5l25 -34q-132 -99 -294.5 -154t-342.5 -55q-126 0 -243 27v-124h87q87 0 149 -61.5t62 -148.5t-62 -149t-149 -62h-31
q-87 0 -149 62t-62 149h78q0 -81 39 -138.5t94 -57.5h31q55 0 94 57.5t39 138.5t-39 138.5t-94 57.5h-164v159q-206 65 -364 198t-249 317t-91 396q0 199 80 373t220 305.5t326 206t398 74.5h19h8h9h17q176 0 335 -47t293 -133v-355q-66 148 -169 258.5t-235 173t-281 62.5z
" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1427" 
d="M339 41v0h664l274 220l-102 -261h-1175l90 127v1664l-90 127v0h1175l102 -261l-274 220h-664v-918h690v-83h-690v-835zM971 2304v0l-29 -29l-636 382h254h59z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1427" 
d="M339 41v0h664l274 220l-102 -261h-1175l91 127v1664l-91 127v0h1175l102 -261l-274 220h-664v-918h690v-83h-690v-835zM971 2657v0l-635 -382l-29 29l352 353h59h253z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1427" 
d="M338 41v0h664l275 220l-102 -261h-1175l90 127v1664l-90 127v0h1175l102 -261l-275 220h-664v-918h690v-83h-690v-835zM638 2388v0l-198 -249h-95l217 383l76 135l76 -135l217 -383h-95z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1427" 
d="M338 41v0h664l275 220l-102 -261h-1175l90 127v1664l-90 127v0h1175l102 -261l-275 220h-664v-918h690v-83h-690v-835zM563 2493v0v-189h-189v189h189zM902 2493v0v-189h-189v189h189z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="815" 
d="M457 1795v0v-1668l90 -127v0h-429v0l88 124v1671l-88 123v0h426v0zM313 2657v0l352 -353l-29 -29l-636 382h254h59z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="815" 
d="M457 1795v0v-1668l90 -127v0h-428v0l87 124v1671l-87 123v0h426v0zM412 2657v0h253l-635 -382l-30 29l353 353h59z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="736" 
d="M417 1795v0v-1668l90 -127v0h-428v0l87 124v1671l-87 123v0h426v0zM293 2388v0l-198 -249h-95l217 383l76 135l76 -135l217 -383h-95z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="678" 
d="M389 1795v0v-1668l90 -127v0h-429v0l88 124v1671l-88 123v0h426v0zM189 2493v0v-189h-189v189h189zM339 2493v0h189v-189h-189v189z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1914" 
d="M741 1918v0q212 0 397.5 -74.5t326 -206t220 -305.5t79.5 -373t-79.5 -373t-220 -305.5t-326 -206t-397.5 -74.5h-582v0l90 127v763h-249v138h249v763l-90 127v0h582zM744 41v0q160 0 300 71.5t246 197.5t166 292.5t60 356.5t-60 356.5t-166 292.5t-246 197.5t-300 71.5
h-1h-2v0h-244v-849h497v-138h-497v-849h244v0h2h1z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1904" 
d="M1754 1918v0v0l-90 -127v-1791h-3h-304l-1225 1732v-1605l90 -127v0h-221h-1l91 127v1664l-91 127h1h304l1317 -1863v1736l-90 127v0h222zM686 2354v0q-19 -20 -39 -72l-72 20q28 106 68 152q41 45 107 45q30 0 64 -12t67 -25q32 -14 63 -26q30 -12 54 -12q19 0 39.5 5
t31.5 15q19 20 40 70l70 -24q-28 -105 -67 -150q-40 -46 -107 -46q-31 0 -64 12t-67 26q-33 14 -63 26q-29 12 -53 12q-19 0 -40 -3t-32 -13z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="2204" 
d="M1027 1918v0q213 0 399 -75t327 -206.5t221 -305.5t80 -372q0 -199 -80 -373t-221 -305.5t-327 -206t-399 -74.5t-399.5 74.5t-327 206t-220.5 305.5t-80 373q0 198 80 372t220.5 305.5t327 206.5t399.5 75zM1027 40v0q161 0 302.5 71.5t248 197.5t167 292.5t60.5 357.5
q0 190 -60.5 357t-167 292.5t-248 197.5t-302.5 72t-302.5 -72t-248 -197.5t-167.5 -292.5t-61 -357q0 -191 61 -357.5t167.5 -292.5t248 -197.5t302.5 -71.5zM1359 2304v0l-29 -29l-636 382h254h59z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="2204" 
d="M1027 1918v0q213 0 399.5 -75t327 -206.5t220.5 -305.5t80 -372q0 -199 -80 -373t-220.5 -305.5t-327 -206t-399.5 -74.5t-399 74.5t-327 206t-221 305.5t-80 373q0 198 80 372t221 305.5t327 206.5t399 75zM1027 40v0q161 0 302.5 71.5t248 197.5t167.5 292.5t61 357.5
q0 190 -61 357t-167.5 292.5t-248 197.5t-302.5 72t-302.5 -72t-248 -197.5t-167 -292.5t-60.5 -357q0 -191 60.5 -357.5t167 -292.5t248 -197.5t302.5 -71.5zM1360 2657v0l-636 -382l-29 29l352 353h59h254z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="2204" 
d="M1027 1918v0q213 0 399.5 -75t327 -206.5t220.5 -305.5t80 -372q0 -199 -80 -373t-220.5 -305.5t-327 -206t-399.5 -74.5q-212 0 -398.5 74.5t-327.5 206t-221 305.5t-80 373q0 198 80 372t221 305.5t327.5 206.5t398.5 75zM1027 40v0q162 0 303 71.5t248 197.5
t167.5 292.5t60.5 357.5q0 190 -60.5 357t-167.5 292.5t-248 197.5t-303 72q-161 0 -302 -72t-248 -197.5t-167.5 -292.5t-60.5 -357q0 -191 60.5 -357.5t167.5 -292.5t248 -197.5t302 -71.5zM1027 2388v0l-198 -249h-95l217 383l76 135l77 -135l217 -383h-95z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="2204" 
d="M1027 1918v0q212 0 398.5 -75t327.5 -206.5t221 -305.5t80 -372q0 -199 -80 -373t-221 -305.5t-327.5 -206t-398.5 -74.5q-213 0 -399.5 74.5t-327 206t-220.5 305.5t-80 373q0 198 80 372t220.5 305.5t327 206.5t399.5 75zM1027 40v0q161 0 302.5 71.5t248 197.5
t167 292.5t60.5 357.5q0 190 -60.5 357t-167 292.5t-248 197.5t-302.5 72q-162 0 -303 -72t-248 -197.5t-167.5 -292.5t-60.5 -357q0 -191 60.5 -357.5t167.5 -292.5t248 -197.5t303 -71.5zM836 2354v0q-19 -21 -40 -72l-71 20q27 106 68 151q41 46 107 46q30 0 64 -12
t66 -26q33 -14 63 -26q31 -12 55 -12q18 0 39 5t32 15q19 20 39 71l71 -24q-28 -105 -68 -151q-39 -45 -106 -45q-31 0 -65 12q-33 12 -66 26t-63 26q-29 12 -53 12q-19 0 -40 -3t-32 -13z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="2204" 
d="M1027 1918v0q213 0 399 -75t327 -206.5t221 -305.5t80 -372q0 -199 -80 -373t-221 -305.5t-327 -206t-399 -74.5t-399 74.5t-327 206t-221 305.5t-80 373q0 198 80 372t221 305.5t327 206.5t399 75zM1027 40v0q161 0 302.5 71.5t248 197.5t167.5 292.5t61 357.5
q0 190 -61 357t-167.5 292.5t-248 197.5t-302.5 72t-302.5 -72t-248 -197.5t-167.5 -292.5t-61 -357q0 -191 61 -357.5t167.5 -292.5t248 -197.5t302.5 -71.5zM952 2492v0v-189h-189v189h189zM1291 2492v0v-189h-189v189h189z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="1006" 
d="M856 290v0l-156 -156l-272 272l-272 -272l-156 156l272 272l-272 272l156 156l272 -272l272 272l156 -156l-272 -272z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="2204" 
d="M1598 1756v0q138 -87 240.5 -209.5t159 -272t56.5 -315.5q0 -199 -80 -373t-220.5 -305.5t-327 -206t-399.5 -74.5q-212 0 -400 75l-91 -158h-221l141 244q-138 87 -240 209.5t-159 272t-57 316.5q0 198 80 372t221 305.5t327.5 206.5t398.5 75q213 0 400 -76l91 159h221
zM249 959v0q0 -208 71.5 -387t196.5 -307l870 1508q-165 105 -360 105q-161 0 -302.5 -72t-248 -197.5t-167 -292.5t-60.5 -357zM1027 40v0q162 0 303 71.5t248 197.5t167.5 292.5t60.5 357.5q0 207 -72 386t-196 307l-871 -1508q165 -104 360 -104z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1655" 
d="M1505 1913v0v0l-90 -127v-1174q0 -195 -109 -349q-82 -118 -207.5 -188t-278.5 -75h-117q-169 0 -308 82.5t-221.5 221.5t-82.5 308v1174l-91 127h1h428v0l-90 -127v-187.5v-428.5v-429v-188q0 -137 64.5 -250t173 -182.5t243.5 -78.5h4h10q137 6 250 69t188 169
q102 142 102 322v2v2.5v2v0.5v1177l-90 127v0h221zM1085 2304v0l-29 -29l-635 382h253h59z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1655" 
d="M1505 1913v0v0l-90 -127v-1174q0 -195 -109 -349q-82 -118 -207.5 -188t-278.5 -75h-117q-169 0 -308 82.5t-222 221.5t-83 308v1174l-90 127v0h429v0l-90 -127v-187.5v-428.5v-429v-188q0 -137 64.5 -250t173 -182.5t243.5 -78.5h4h10q137 6 250 69t188 169
q102 142 102 322v2v2.5v2v0.5v1177l-90 127v0h221zM1085 2657v0l-635 -382l-29 29l352 353h59h253z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1655" 
d="M1505 1913v0v0l-90 -127v-1174q0 -195 -109 -349q-82 -118 -207.5 -188t-278.5 -75h-117q-169 0 -308 82.5t-222 221.5t-83 308v1174l-90 127v0h429v0l-90 -127v-187.5v-428.5v-429v-188q0 -137 64.5 -250t172.5 -182.5t244 -78.5h4h10q137 6 250 69t188 169
q102 142 102 322v2v2.5v2v0.5v1177l-90 127v0h221zM753 2388v0l-198 -249h-95l217 383l76 135l76 -135l217 -383h-95z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1655" 
d="M1505 1913v0v0l-90 -127v-1174q0 -195 -109 -349q-82 -118 -208 -188t-278 -75h-117q-169 0 -308 82.5t-222 221.5t-83 308v1174l-90 127v0h429v0l-90 -127v-187.5v-428.5v-429v-188q0 -137 64.5 -250t172.5 -182.5t244 -78.5h4h10q137 6 250 69t188 169q102 142 102 322
v2v2.5v2v0.5v1177l-90 127v0h221zM678 2492v0v-189h-189v189h189zM1017 2492v0v-189h-189v189h189z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="2111" 
d="M1105 1046v0v0v-919l90 -127v0h-90h-249h-90v0l90 127v832l-664 831l-192 128v0h498v0l-33 -72l614 -767l667 760l-20 79h235v0l-202 -128zM1313 2657v0l-636 -382l-29 29l353 353h58h254z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="2110" 
d="M905 2492v0v-189h-189v189h189zM1244 2492v0v-189h-189v189h189zM1104 1046v0v0v-919l90 -127v0h-90h-248h-90v0l90 127v832l-665 831l-191 128v0h498v0l-33 -72l613 -767l668 760l-21 79h235v0l-202 -128z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1120" 
 />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1251" 
 />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1251" 
d="M1029 111v0l72 -111v0h-292v116l-180 -73l-5 -2q-66 -25 -128.5 -33t-133.5 -8v0v0q-149 12 -231 62q-17 10 -32 21q-52 40 -75.5 91t-23.5 105q0 74 36.5 143t94.5 112q33 24 65 42q17 10 33 18q23 11 72.5 33t113.5 46q4 1 62.5 24t135 53t136.5 53.5l60 23.5v56
q0 43 -5 81q-1 39 -12 74v0.5v0.5v0q-1 0 -1 1v0q-29 91 -104.5 147.5t-175.5 57.5v0q-38 0 -74 -9q-155 -45 -207 -196h-152q68 111 180.5 177.5t252.5 69.5h5h8v0v0v0q23 1 46 1q74 0 134 -6v0v0v0h4q3 -1 7 -1l10 -2q121 -20 202.5 -104t97.5 -206q1 -13 2 -27
q3 -37 3 -78v-4q-1 -29 -1 -165v-301.5v-282.5zM809 161v0v621l-20 -7q-36 -14 -101.5 -40t-123.5 -49t-71 -28q-16 -7 -35 -16q-53 -24 -100 -56q-9 -7 -16 -12q-45 -36 -74 -81q-39 -59 -47 -134v-2v-4q-2 -15 -2 -30q0 -12 2 -23q9 -71 60.5 -136.5t137 -94t194.5 12.5z
M806 1573v0l-22 -22l-489 294h195h45z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1251" 
d="M1030 111v0l71 -111v0h-292v116l-180 -73l-5 -2q-65 -25 -128 -33t-134 -8v0v0q-148 12 -231 62q-17 10 -31 21q-53 40 -76.5 91t-23.5 105q0 74 36.5 143t94.5 112q33 24 65 42q18 10 34 18q22 11 72 33t114 46q3 1 61.5 24t135 53t136.5 53.5l60 23.5v56q0 43 -4 81
q-2 39 -13 74v0.5v0.5v0v1v0q-30 91 -105 147.5t-175 57.5h-1q-38 0 -74 -9q-154 -45 -207 -196h-151q68 111 180.5 177.5t251.5 69.5h5h8v0h0.5h0.5q22 1 45 1q74 0 135 -6v0v0v0h3q3 -1 7 -1q5 -1 11 -2q120 -20 201.5 -104t97.5 -206q1 -13 2 -27q3 -37 3 -78v-4v-165
v-301.5v-282.5zM809 161v0v621l-19 -7q-36 -14 -102 -40t-124 -49t-71 -28q-16 -7 -34 -16q-54 -24 -100 -56q-9 -7 -17 -12q-45 -36 -74 -81q-39 -59 -47 -134v-2v-4q-1 -15 -1 -30q0 -12 1 -23q9 -71 60.5 -136.5t137 -94t195.5 12.5zM806 1845v0l-488 -294l-23 22
l271 272h45h195z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1252" 
d="M1030 111v0l72 -111v0h-293v116l-180 -73l-5 -2q-65 -25 -128 -33t-133 -8h-1v0q-148 12 -230 62q-17 10 -32 21q-53 40 -76.5 91t-23.5 105q0 74 36.5 143t95.5 112q32 24 65 42q17 10 33 18q22 11 72 33t114 46q3 1 61.5 24t135.5 53t136.5 53.5l59.5 23.5v56
q0 43 -4 81q-2 39 -13 74v0.5v0.5v0v1v0q-30 91 -105 147.5t-175 57.5h-1q-38 0 -73 -9q-155 -45 -207 -196h-152q68 111 180.5 177.5t251.5 69.5h6h8v0v0v0q23 1 45 1q74 0 135 -6v0v0v0h3q4 -1 7 -1q6 -1 11 -2q120 -20 201.5 -104t98.5 -206q1 -13 2 -27q2 -37 2 -78v-4
v-166v-301v-282zM809 161v0v621l-19 -7q-36 -14 -102 -40t-124 -49t-71 -28q-16 -7 -34 -16q-53 -24 -100 -56q-9 -7 -16 -12q-46 -36 -75 -81q-38 -59 -46 -134v-2q-1 -2 -1 -4q-1 -15 -1 -30q0 -12 1 -23q9 -71 61 -136.5t137 -94t195 12.5zM551 1634v0l-155 -194h-74
l169 299l60 106l60 -106l169 -299h-74z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1251" 
d="M1029 111v0l72 -111v0h-293v116l-179 -73l-5 -2q-66 -25 -129 -33t-133 -8v0h-1q-148 12 -230 62q-17 10 -32 21q-52 40 -75.5 91t-23.5 105q0 74 36.5 143t94.5 112q33 24 65 42q17 10 33 18q23 11 72.5 33t113.5 46q4 1 62.5 24t135 53t136 53.5l59.5 23.5v56
q0 43 -4 81q-1 39 -12 74v0.5t-1 0.5v0v1v0q-30 91 -105 147.5t-175 57.5v0q-38 0 -74 -9q-155 -45 -207 -196h-152q68 111 180.5 177.5t251.5 69.5h6h8v0v0v0q23 1 45 1q75 0 135 -6v0v0v0h3q4 -1 7 -1q6 -1 11 -2q121 -20 202.5 -104t97.5 -206q1 -13 2 -27q2 -37 2 -78
v-4v-166v-301v-282zM808 161v0v621l-19 -7q-36 -14 -101.5 -40t-123.5 -49t-71 -28q-16 -7 -35 -16q-53 -24 -100 -56q-9 -7 -16 -12q-45 -36 -74 -81q-39 -59 -47 -134v-2v-4q-2 -15 -2 -30q0 -12 2 -23q9 -71 60.5 -136.5t136.5 -94t195 12.5zM394 1606v0q-16 -17 -32 -59
l-59 16q23 87 56 125q33 37 88 37q24 0 52 -10t54 -21q27 -11 52 -21t44 -10q16 0 33 4t26 12q15 17 32 58l58 -19q-23 -87 -56 -124q-32 -37 -87 -37q-25 0 -52 10q-28 10 -55 21q-27 12 -51 21q-25 10 -44 10q-15 0 -32.5 -2.5t-26.5 -10.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1251" 
d="M1030 111v0l71 -111v0h-292v116l-180 -73l-5 -2q-65 -25 -128 -33t-134 -8v0v0q-148 12 -231 62q-17 10 -32 21q-52 40 -75.5 91t-23.5 105q0 74 36.5 143t94.5 112q33 24 65 42q17 10 34 18q22 11 72 33t113 46q4 1 62.5 24t135 53t136.5 53.5l60 23.5v56q0 43 -4 81
q-2 39 -13 74v0.5v0.5v0v1h-1q-29 91 -104 147.5t-176 57.5v0q-38 0 -74 -9q-154 -45 -207 -196h-151q68 111 180.5 177.5t251.5 69.5h5h8v0v0v0q23 1 46 1q74 0 134 -6h0.5h0.5v0h3q3 -1 7 -1q5 -1 11 -2q120 -20 201.5 -104t97.5 -206q1 -13 2 -27q3 -37 3 -78v-4v-166
v-301v-282zM809 161v0v621l-20 -7q-36 -14 -101.5 -40t-123.5 -49t-71 -28q-16 -7 -34 -16q-54 -24 -100 -56q-10 -7 -17 -12q-45 -36 -74 -81q-39 -59 -47 -134v-2v-4q-1 -15 -1 -30q0 -12 1 -23q9 -71 60.5 -136.5t137 -94t195.5 12.5zM488 1721v0v-157h-156v157h156z
M770 1721v0v-157h-157v157h157z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1252" 
d="M1030 111v0l72 -111v0h-293v116l-180 -73l-5 -2q-65 -25 -128 -33t-133 -8h-1v0q-148 12 -230 62q-18 10 -32 21q-53 40 -76.5 91t-23.5 105q0 74 36.5 143t95.5 112q32 24 65 42q17 10 33 18q22 11 72 33t114 46q3 1 61.5 24t135.5 53t136.5 53.5l59.5 23.5v56
q0 43 -4 81q-2 39 -13 74v0.5v0.5v0v1v0q-30 91 -105 147.5t-175 57.5h-1q-38 0 -73 -9q-155 -45 -208 -196h-151q68 111 180.5 177.5t251.5 69.5h5h9v0v0v0q23 1 45 1q74 0 135 -6v0v0v0h3q4 -1 7 -1q6 -1 11 -2q120 -20 201.5 -104t97.5 -206q2 -13 3 -27q2 -37 2 -78v-4
v-166v-301v-282zM809 161v0v621l-19 -7q-36 -14 -102 -40t-124 -49t-71 -28q-16 -7 -34 -16q-53 -24 -100 -56q-9 -7 -16 -12q-46 -36 -75 -81q-38 -59 -46 -134q0 -1 -1 -2v-4q-1 -15 -1 -30q0 -12 1 -23q9 -71 60.5 -136.5t137 -94t195.5 12.5zM551 1444v0
q-83 0 -141.5 58.5t-58.5 141.5t58.5 142t141.5 59t141.5 -59t58.5 -142t-58.5 -141.5t-141.5 -58.5zM551 1813v0q-38 0 -65 -49.5t-27 -119.5q0 -69 27 -118.5t65 -49.5t65 49.5t27 118.5q0 70 -27 119.5t-65 49.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="2033" 
d="M1486 59v0q16 6 63 25t92 37.5t57 23.5l37 -30l-215 -87l-25 -10q-1 -1 -2 -1q-1 -1 -3 -1q-44 -16 -94 -16h-3q-7 0 -14 1h-1q-7 0 -13 1h-1q-19 1 -38 4q-3 1 -6 1q-3 1 -7 1q-3 1 -7 2q-3 0 -6 1q-7 1 -13 3v0v0q-27 6 -52 14v0q-115 38 -205 117q-23 20 -45 43
l-176 -72l-43 -17l-137 -56l-5 -2q-65 -25 -128 -33t-133 -8h-1v0q-148 12 -230 62q-18 10 -32 21q-53 40 -76.5 91t-23.5 105q0 74 36.5 143t95.5 112q32 24 65 42q17 10 33 18q22 11 72 33t114 46q3 1 61.5 24t135.5 53t136.5 53.5l59.5 23.5v56q0 43 -4 81q-2 39 -13 74
v0.5v0.5v0v1v0q-30 91 -105 147.5t-175 57.5h-1h-9q-33 -1 -64 -9q-155 -45 -207 -196h-152q67 109 176.5 175t245.5 71q5 1 10 1h6h8v0v0v0q23 1 45 1q74 0 135 -6v0v0v0h3q4 -1 7 -1q6 -1 11 -2q167 -30 253 -171q74 83 171 130t207 47v0h1q189 -4 330 -130t195 -329h-838
q-12 -68 -15 -144q-1 -19 -1 -39q0 -18 1 -36q8 -182 74 -321q52 -112 131 -177v0q26 -21 54 -36v-1h1q50 -31 111 -31v0h1q42 1 78 15q3 1 6 3zM1357 1244v0q-103 0 -184.5 -104t-119.5 -272h608q-38 168 -119.5 272t-184.5 104zM809 161v0l147 60q-133 163 -147 392v63
v106l-19 -7q-36 -14 -102 -40t-124 -49t-71 -28q-16 -7 -34 -16q-53 -24 -100 -56q-9 -7 -16 -12q-46 -36 -75 -81q-38 -59 -46 -134v-2q-1 -2 -1 -4q-1 -15 -1 -30q0 -12 1 -23q9 -71 60.5 -136.5t137 -94t195.5 12.5l29 11z" />
    <glyph glyph-name="oe" unicode="&#x153;" 
 />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1077" 
d="M604 1244v0q-106 0 -193 -81t-138.5 -217.5t-51.5 -302.5q0 -201 75 -357q52 -112 131 -177v0q26 -21 54 -36q0 -1 1 -1v0q50 -31 111 -31h0.5h0.5q42 1 78 15q3 2 6 3q14 5 52 20.5t79.5 32.5t66.5 27q11 4 15 6l36 -30l-240 -97q-1 -1 -2 -1q-1 -1 -3 -1
q-44 -16 -94 -16h-3q-7 0 -13 1q-8 0 -15 1h-1q-19 1 -38 4q-3 1 -5 1q-4 1 -8 1l-8 2h-1v-98h74q74 0 126 -52t52 -126t-52 -126.5t-126 -52.5h-27q-74 0 -126.5 52.5t-52.5 126.5h66q0 -69 33 -117.5t80 -48.5h27q46 0 79.5 48.5t33.5 117.5t-33.5 117.5t-79.5 48.5h-140
v127q-1 0 -1 1h-2v0q-124 41 -220 130.5t-151.5 214.5t-55.5 270q0 177 81.5 323t218.5 232.5t304 86.5h24q161 -6 299 -65v-256q-52 128 -136.5 204t-186.5 76z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1224" 
d="M677 59v0q14 5 52 20.5t80 32.5t66 27q11 4 15 6l36 -30l-215 -87l-25 -10q-1 -1 -2 -1q-1 -1 -2 -1q-45 -16 -94 -16h-4q-6 0 -13 1q-7 0 -14 1h-1q-20 1 -39 4q-2 1 -5 1q-4 1 -8 1q-6 2 -13 3q-6 1 -13 3v0v0q-26 6 -52 14v0q-124 41 -220 130.5t-151 214.5t-55 270
q0 177 74 323t198.5 232.5t275.5 86.5h1v0q189 -4 330 -130t195 -329h-838q-15 -87 -15 -183q0 -201 74 -357q53 -112 131 -177v0q26 -21 54 -36l1 -1h1q50 -31 110 -31h1v0q42 1 79 15q3 2 5 3zM548 1244v0q-103 0 -184 -104t-119 -272h607q-38 168 -119 272t-185 104z
M793 1573v0l-23 -22l-489 294h196h45z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1224" 
d="M677 59v0q14 5 54 21.5t82.5 34t65.5 26.5q8 3 11 4l36 -30l-215 -87l-25 -10q-1 -1 -2 -1q-1 -1 -2 -1q-45 -16 -95 -16h-3q-7 0 -13 1q-8 0 -15 1v0q-20 1 -39 4q-2 1 -5 1q-4 1 -8 1q-7 2 -13 3q-7 1 -13 3v0v0q-26 6 -52 14v0q-124 41 -220 130.5t-151 214.5t-55 270
q0 177 74 323t198.5 232.5t275.5 86.5h0.5h0.5q189 -4 330 -130t195 -329h-838q-16 -87 -16 -183q0 -201 75 -357q52 -112 131 -177v0q26 -21 54 -36q0 -1 1 -1v0q51 -31 111 -31h0.5h0.5q42 1 79 15q2 2 5 3zM548 1244v0q-103 0 -184.5 -104t-119.5 -272h608
q-38 168 -119.5 272t-184.5 104zM792 1845v0l-489 -294l-22 22l271 272h45h195z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1224" 
d="M678 59v0q16 6 63 25t92 37.5t57 23.5l37 -30l-215 -87l-25 -10q-1 -1 -3 -1q-1 -1 -2 -1q-45 -16 -94 -16h-3q-7 0 -14 1q-7 0 -14 1h-1q-19 1 -38 4q-3 1 -5 1q-4 1 -8 1q-7 2 -13 3q-7 1 -13 3v0v0q-27 6 -52 14v0q-125 41 -220.5 130.5t-151 214.5t-55.5 270
q0 177 74 323t198.5 232.5t276.5 86.5v0v0q190 -4 331 -130t194 -329h-837q-16 -87 -16 -183q0 -201 75 -357q52 -112 131 -177v0q26 -21 53 -36q1 -1 2 -1v0q50 -31 111 -31v0v0q42 1 79 15q3 2 6 3zM549 1244v0q-103 0 -184.5 -104t-119.5 -272h608q-38 168 -119.5 272
t-184.5 104zM537 1634v0l-154 -194h-75l170 299l59 106l60 -106l170 -299h-75z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1224" 
d="M678 59v0q16 6 62.5 25t92 37.5t57.5 23.5l36 -30l-215 -87l-25 -10q-1 -1 -2 -1q-1 -1 -2 -1q-45 -16 -94 -16h-3q-7 0 -14 1q-7 0 -14 1h-1q-20 1 -39 4q-2 1 -5 1q-4 1 -8 1q-6 2 -12 3h-1q-6 1 -13 3v0v0q-26 6 -52 14v0q-124 41 -220 130.5t-151 214.5t-55 270
q0 177 74 323t198.5 232.5t275.5 86.5h1v0q189 -4 330 -130t195 -329h-838q-15 -87 -15 -183q0 -201 74 -357q53 -112 131 -177v0q26 -21 54 -36l1 -1h1q50 -31 111 -31v0v0q42 1 79 15q3 2 6 3zM548 1244v0q-103 0 -184 -104t-119 -272h608q-39 168 -120 272t-185 104z
M475 1721v0v-157h-157v157h157zM756 1721v0v-157h-157v157h157z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="661" 
d="M366 1286v0v-1175l72 -111v0h-72h-221h-72v0l72 111v1064l-72 111v0h72h221zM240 1845v0l271 -272l-22 -22l-489 294h195h45z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="661" 
d="M366 1286v0v-1175l71 -111v0h-71h-221h-72v0l72 111v1064l-72 111v0h72h221zM316 1845v0h195l-489 -294l-22 22l271 272h45z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="608" 
d="M339 1286v0v-1175l72 -111v0h-72h-220h-72l72 111v1064l-72 111v0h72h220zM229 1634v0l-155 -194h-74l169 299l60 106l60 -106l169 -299h-74z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="588" 
d="M329 1286v0v-1175l72 -111v0h-72h-221h-71l71 111v1064l-71 111v0h71h221zM156 1721v0v-157h-156v157h156zM281 1721v0h157v-157h-157v157z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1139" 
 />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1402" 
d="M1182 111v0l70 -111v0h-362v0l71 111v675v0v0q0 172 -67 297.5t-169 155.5v0h-1q-9 3 -20 4q-7 1 -14 1h-0.5h-0.5q-71 0 -139 -18l-244 -62l-14 -4v-1049l71 -111v0h-362v0l71 111v1064l-72 111v0h292v-83l248 63q75 20 145 20v0v0q52 -1 101 -11q114 -25 203 -95
t141 -172t52 -222v0v-5v0v-670zM470 1606v0q-16 -17 -33 -59l-58 16q22 87 56 125q33 37 87 37q24 0 52 -10t55 -21q26 -11 51 -21t45 -10q15 0 32 4t26 12q16 17 32 58l58 -19q-23 -87 -55 -124q-33 -37 -87 -37q-26 0 -53 10t-54 21q-28 12 -52 21q-24 10 -44 10
q-15 0 -32.5 -2.5t-25.5 -10.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1356" 
d="M603 1286v0q166 0 303 -87t218.5 -233t81.5 -323q0 -178 -81.5 -324t-218.5 -232.5t-303 -86.5q-167 0 -304 86.5t-218 232.5t-81 324q0 177 81 323t218 233t304 87zM603 41v0q105 0 192 81.5t138.5 218t51.5 302.5t-51.5 302.5t-138.5 217.5t-192 81q-106 0 -192.5 -81
t-138.5 -217.5t-52 -302.5t52 -302.5t138.5 -218t192.5 -81.5zM858 1573v0l-22 -22l-489 294h195h45z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1357" 
d="M604 1286v0q166 0 303 -87t218.5 -233t81.5 -323q0 -178 -81.5 -324t-218.5 -232.5t-303 -86.5q-167 0 -304 86.5t-218.5 232.5t-81.5 324q0 177 81.5 323t218.5 233t304 87zM604 41v0q105 0 192 81.5t138.5 218t51.5 302.5t-51.5 302.5t-138.5 217.5t-192 81
q-106 0 -193 -81t-138.5 -217.5t-51.5 -302.5t51.5 -302.5t138.5 -218t193 -81.5zM859 1845v0l-489 -294l-22 22l271 272h45h195z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1356" 
d="M603 1286v0q167 0 304 -87t218 -233t81 -323q0 -178 -81 -324t-218 -232.5t-304 -86.5q-166 0 -303 86.5t-218.5 232.5t-81.5 324q0 177 81.5 323t218.5 233t303 87zM603 41v0q106 0 192.5 81.5t138.5 218t52 302.5t-52 302.5t-138.5 217.5t-192.5 81q-105 0 -192 -81
t-138.5 -217.5t-51.5 -302.5t51.5 -302.5t138.5 -218t192 -81.5zM603 1634v0l-155 -194h-74l170 299l59 106l60 -106l170 -299h-75z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1356" 
d="M603 1286v0q167 0 303.5 -87t218 -233t81.5 -323q0 -178 -81.5 -324t-218 -232.5t-303.5 -86.5t-303.5 86.5t-218 232.5t-81.5 324q0 177 81.5 323t218 233t303.5 87zM603 41v0q106 0 192.5 81.5t138 218t51.5 302.5t-51.5 302.5t-138 217.5t-192.5 81t-192.5 -81
t-138 -217.5t-51.5 -302.5t51.5 -302.5t138 -218t192.5 -81.5zM447 1606v0q-16 -17 -33 -59l-58 16q22 87 56 125q33 37 87 37q25 0 53 -10t54 -21t51 -21t45 -10q15 0 32 4t26 12q16 17 33 58l57 -19q-23 -87 -55 -124q-33 -37 -87 -37q-26 0 -53 10t-54 21q-27 12 -52 21
q-24 10 -44 10q-15 0 -32 -2.5t-26 -10.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1357" 
d="M604 1286v0q166 0 303 -87t218.5 -233t81.5 -323q0 -178 -81.5 -324t-218.5 -232.5t-303 -86.5q-167 0 -304 86.5t-218.5 232.5t-81.5 324q0 177 81.5 323t218.5 233t304 87zM604 41v0q105 0 192 81.5t138.5 218t51.5 302.5t-51.5 302.5t-138.5 217.5t-192 81
q-106 0 -192.5 -81t-138.5 -217.5t-52 -302.5t52 -302.5t138.5 -218t192.5 -81.5zM541 1721v0v-157h-156v157h156zM823 1721v0v-157h-157v157h157z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="1140" 
d="M0 637v0h990v-138h-990v138zM385 159v0v221h220v-221h-220zM605 976v0v-221h-220v221h220z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1357" 
d="M957 1163v0q115 -89 182.5 -224.5t67.5 -295.5q0 -178 -81.5 -324t-218.5 -232.5t-304 -86.5q-149 0 -276 72l-41 -72h-107l71 122q-115 90 -182.5 225t-67.5 296q0 177 81.5 323t218.5 233t303 87q150 0 277 -72l41 72h107zM221 643v0q0 -231 97 -401l512 886
q-48 55 -105.5 85.5t-121.5 30.5q-105 0 -192 -81t-138.5 -217.5t-51.5 -302.5zM603 41v0q106 0 193 81.5t138.5 218t51.5 302.5q0 231 -98 401l-511 -886q48 -55 105.5 -86t120.5 -31z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1403" 
d="M1182 111v0l71 -111v0h-292v83l-247 -63q-75 -20 -145 -20v0v0q-52 1 -101 11q-114 25 -203 95t-141 172t-52 222v0v5v0v670l-72 111v0h293v-786v0v0q0 -172 66.5 -297.5t169.5 -155.5v0h1q9 -3 20 -4q7 -1 14 -1h0.5h0.5q71 0 139 18l244 63l13 3v1049l-71 111v0h292
v-1175zM882 1573v0l-22 -22l-489 294h195h45z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1402" 
d="M1181 111v0l71 -111v0h-291v83l-248 -63q-75 -20 -145 -20v0v0q-52 1 -101 11q-114 25 -203 95t-141 172t-52 222v0v5v0v670l-71 111v0h292v-786v0v0q0 -172 66.5 -297.5t169.5 -155.5v0h1q9 -3 20 -4q7 -1 14 -1h0.5h0.5q71 0 139 18l244 63l14 3v1049l-72 111v0h292
v-1175zM881 1845v0l-489 -294l-22 22l271 272h45h195z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1402" 
d="M1182 111v0l70 -111v0h-291v83l-248 -63q-75 -20 -145 -20v0v0q-52 1 -101 11q-114 25 -203 95t-141 172t-52 222v0v5v0v670l-71 111v0h292v-786v0v0q0 -172 67 -297.5t169 -155.5v0h1q9 -3 20 -4q7 -1 14 -1h0.5h0.5q71 0 139 18l244 63l14 3v1049l-72 111v0h293v-1175z
M626 1634v0l-155 -194h-75l170 299l60 106l59 -106l170 -299h-74z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1402" 
d="M1182 111v0l70 -111v0h-291v83l-248 -63q-74 -20 -145 -20v0v0q-52 1 -101 11q-114 25 -203 95t-140.5 172t-51.5 222v0v5v0v670l-72 111v0h292v-786v0v0q0 -172 67 -297.5t169 -155.5v0h1q9 -3 20 -4q7 -1 14 -1h0.5h0.5q71 0 139 18l245 63l13 3v1049l-72 111v0h293
v-1175zM564 1721v0v-157h-157v157h157zM845 1721v0v-157h-157v157h157z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1468" 
d="M1318 1286v0l-121 -111l-771 -1697l20 -111h-186v0l121 111l256 563l-515 1134l-122 111v0h386l-22 -111l394 -867l394 867l-22 111v0h188v0zM915 1845v0l-489 -294l-23 22l272 272h45h195z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1010" 
 />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1469" 
d="M1319 1286v0l-121 -111l-771 -1697l20 -111h-186v0l120 111l256 563l-515 1134l-122 111v0h387l-22 -111l394 -867l393 867l-21 111v0h188v0zM597 1721v0v-157h-156v157h156zM879 1721v0v-157h-157v157h157z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="514" 
 />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1108" 
 />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="2048" 
 />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="657" 
 />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="657" 
 />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="657" 
 />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="657" 
 />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="657" 
 />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="657" 
 />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="1014" 
 />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="1014" 
 />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="1024" 
 />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="657" 
 />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="631" 
 />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="631" 
 />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="342" 
 />
    <glyph glyph-name="fraction" unicode="&#x2215;" horiz-adv-x="342" 
 />
    <glyph glyph-name="franc" unicode="&#x20a3;" horiz-adv-x="657" 
 />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="657" 
 />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="657" 
 />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="657" 
 />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="2271" 
 />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1497" 
 />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="657" 
 />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="657" 
 />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="657" 
 />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="657" 
 />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="657" 
 />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="657" 
 />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="657" 
 />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="657" 
 />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="657" 
 />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="657" 
 />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="657" 
 />
    <glyph glyph-name="A.salt" unicode="&#xe000;" horiz-adv-x="1073" 
 />
    <glyph glyph-name="B.salt" unicode="&#xe001;" horiz-adv-x="1073" 
 />
    <glyph glyph-name="C.salt" unicode="&#xe002;" 
 />
    <glyph glyph-name="D.salt" unicode="&#xe003;" 
 />
    <glyph glyph-name="E.salt" unicode="&#xe004;" 
 />
    <glyph glyph-name="F.salt" unicode="&#xe005;" 
 />
    <glyph glyph-name="G.salt" unicode="&#xe006;" 
 />
    <glyph glyph-name="H.salt" unicode="&#xe007;" 
 />
    <glyph glyph-name="I.salt" unicode="&#xe008;" 
 />
    <glyph glyph-name="J.salt" unicode="&#xe009;" 
 />
    <glyph glyph-name="K.salt" unicode="&#xe010;" 
 />
    <glyph glyph-name="L.salt" unicode="&#xe011;" 
 />
    <glyph glyph-name="M.salt" unicode="&#xe012;" 
 />
    <glyph glyph-name="N.salt" unicode="&#xe013;" 
 />
    <glyph glyph-name="O.salt" unicode="&#xe014;" 
 />
    <glyph glyph-name="P.salt" unicode="&#xe015;" 
 />
    <glyph glyph-name="Q.salt" unicode="&#xe016;" 
 />
    <glyph glyph-name="R.salt" unicode="&#xe017;" 
 />
    <glyph glyph-name="S.salt" unicode="&#xe018;" 
 />
    <glyph glyph-name="T.salt" unicode="&#xe019;" 
 />
    <glyph glyph-name="U.salt" unicode="&#xe020;" 
 />
    <glyph glyph-name="V.salt" unicode="&#xe021;" 
 />
    <glyph glyph-name="W.salt" unicode="&#xe022;" 
 />
    <glyph glyph-name="X.salt" unicode="&#xe023;" 
 />
    <glyph glyph-name="Y.salt" unicode="&#xe024;" 
 />
    <glyph glyph-name="Z.salt" unicode="&#xe025;" 
 />
    <glyph glyph-name="a.salt" unicode="&#xe026;" 
 />
    <glyph glyph-name="b.salt" unicode="&#xe027;" 
 />
    <glyph glyph-name="c.salt" unicode="&#xe028;" 
 />
    <glyph glyph-name="d.salt" unicode="&#xe029;" 
 />
    <glyph glyph-name="e.salt" unicode="&#xe030;" 
 />
    <glyph glyph-name="f.salt" unicode="&#xe031;" 
 />
    <glyph glyph-name="g.salt" unicode="&#xe032;" 
 />
    <glyph glyph-name="h.salt" unicode="&#xe033;" 
 />
    <glyph glyph-name="i.salt" unicode="&#xe034;" 
 />
    <glyph glyph-name="j.salt" unicode="&#xe035;" 
 />
    <glyph glyph-name="k.salt" unicode="&#xe036;" 
 />
    <glyph glyph-name="l.salt" unicode="&#xe037;" 
 />
    <glyph glyph-name="m.salt" unicode="&#xe038;" 
 />
    <glyph glyph-name="n.salt" unicode="&#xe039;" 
 />
    <glyph glyph-name="o.salt" unicode="&#xe040;" 
 />
    <glyph glyph-name="p.salt" unicode="&#xe041;" 
 />
    <glyph glyph-name="q.salt" unicode="&#xe042;" 
 />
    <glyph glyph-name="r.salt" unicode="&#xe043;" 
 />
    <glyph glyph-name="s.salt" unicode="&#xe044;" 
 />
    <glyph glyph-name="t.salt" unicode="&#xe045;" 
 />
    <glyph glyph-name="u.salt" unicode="&#xe046;" 
 />
    <glyph glyph-name="v.salt" unicode="&#xe047;" 
 />
    <glyph glyph-name="w.salt" unicode="&#xe048;" 
 />
    <glyph glyph-name="x.salt" unicode="&#xe049;" 
 />
    <glyph glyph-name="y.salt" unicode="&#xe050;" 
 />
    <glyph glyph-name="z.salt" unicode="&#xe051;" 
 />
    <glyph glyph-name="A.swsh" unicode="&#xe052;" 
 />
    <glyph glyph-name="B.swsh" unicode="&#xe053;" 
 />
    <glyph glyph-name="C.swsh" unicode="&#xe054;" 
 />
    <glyph glyph-name="D.swsh" unicode="&#xe055;" 
 />
    <glyph glyph-name="E.swsh" unicode="&#xe056;" 
 />
    <glyph glyph-name="F.swsh" unicode="&#xe057;" 
 />
    <glyph glyph-name="G.swsh" unicode="&#xe058;" 
 />
    <glyph glyph-name="H.swsh" unicode="&#xe059;" 
 />
    <glyph glyph-name="I.swsh" unicode="&#xe060;" 
 />
    <glyph glyph-name="J.swsh" unicode="&#xe061;" 
 />
    <glyph glyph-name="K.swsh" unicode="&#xe062;" 
 />
    <glyph glyph-name="L.swsh" unicode="&#xe063;" 
 />
    <glyph glyph-name="M.swsh" unicode="&#xe064;" 
 />
    <glyph glyph-name="N.swsh" unicode="&#xe065;" 
 />
    <glyph glyph-name="O.swsh" unicode="&#xe066;" 
 />
    <glyph glyph-name="P.swsh" unicode="&#xe067;" 
 />
    <glyph glyph-name="Q.swsh" unicode="&#xe068;" 
 />
    <glyph glyph-name="R.swsh" unicode="&#xe069;" 
 />
    <glyph glyph-name="S.swsh" unicode="&#xe070;" 
 />
    <glyph glyph-name="T.swsh" unicode="&#xe071;" 
 />
    <glyph glyph-name="U.swsh" unicode="&#xe072;" 
 />
    <glyph glyph-name="V.swsh" unicode="&#xe073;" 
 />
    <glyph glyph-name="W.swsh" unicode="&#xe074;" 
 />
    <glyph glyph-name="X.swsh" unicode="&#xe075;" 
 />
    <glyph glyph-name="Y.swsh" unicode="&#xe076;" 
 />
    <glyph glyph-name="Z.swsh" unicode="&#xe077;" 
 />
    <glyph glyph-name="a.swsh" unicode="&#xe078;" 
 />
    <glyph glyph-name="b.swsh" unicode="&#xe079;" 
 />
    <glyph glyph-name="c.swsh" unicode="&#xe080;" 
 />
    <glyph glyph-name="d.swsh" unicode="&#xe081;" 
 />
    <glyph glyph-name="e.swsh" unicode="&#xe082;" 
 />
    <glyph glyph-name="f.swsh" unicode="&#xe083;" 
 />
    <glyph glyph-name="g.swsh" unicode="&#xe084;" 
 />
    <glyph glyph-name="h.swsh" unicode="&#xe085;" 
 />
    <glyph glyph-name="i.swsh" unicode="&#xe086;" 
 />
    <glyph glyph-name="j.swsh" unicode="&#xe087;" 
 />
    <glyph glyph-name="k.swsh" unicode="&#xe088;" 
 />
    <glyph glyph-name="l.swsh" unicode="&#xe089;" 
 />
    <glyph glyph-name="m.swsh" unicode="&#xe090;" 
 />
    <glyph glyph-name="n.swsh" unicode="&#xe091;" 
 />
    <glyph glyph-name="o.swsh" unicode="&#xe092;" 
 />
    <glyph glyph-name="p.swsh" unicode="&#xe093;" 
 />
    <glyph glyph-name="q.swsh" unicode="&#xe094;" 
 />
    <glyph glyph-name="r.swsh" unicode="&#xe095;" 
 />
    <glyph glyph-name="s.swsh" unicode="&#xe096;" 
 />
    <glyph glyph-name="t.swsh" unicode="&#xe097;" 
 />
    <glyph glyph-name="u.swsh" unicode="&#xe098;" 
 />
    <glyph glyph-name="v.swsh" unicode="&#xe099;" 
 />
    <glyph glyph-name="w.swsh" unicode="&#xe100;" 
 />
    <glyph glyph-name="x.swsh" unicode="&#xe101;" 
 />
    <glyph glyph-name="y.swsh" unicode="&#xe102;" 
 />
    <glyph glyph-name="z.swsh" unicode="&#xe103;" 
 />
    <glyph glyph-name="a.titl" unicode="&#xe104;" 
 />
    <glyph glyph-name="b.titl" unicode="&#xe105;" 
 />
    <glyph glyph-name="c.titl" unicode="&#xe106;" 
 />
    <glyph glyph-name="d.titl" unicode="&#xe107;" 
 />
    <glyph glyph-name="e.titl" unicode="&#xe108;" 
 />
    <glyph glyph-name="f.titl" unicode="&#xe109;" 
 />
    <glyph glyph-name="g.titl" unicode="&#xe110;" 
 />
    <glyph glyph-name="h.titl" unicode="&#xe111;" 
 />
    <glyph glyph-name="i.titl" unicode="&#xe112;" 
 />
    <glyph glyph-name="j.titl" unicode="&#xe113;" 
 />
    <glyph glyph-name="k.titl" unicode="&#xe114;" 
 />
    <glyph glyph-name="l.titl" unicode="&#xe115;" 
 />
    <glyph glyph-name="m.titl" unicode="&#xe116;" 
 />
    <glyph glyph-name="n.titl" unicode="&#xe117;" 
 />
    <glyph glyph-name="o.titl" unicode="&#xe118;" 
 />
    <glyph glyph-name="p.titl" unicode="&#xe119;" 
 />
    <glyph glyph-name="q.titl" unicode="&#xe120;" 
 />
    <glyph glyph-name="r.titl" unicode="&#xe121;" 
 />
    <glyph glyph-name="s.titl" unicode="&#xe122;" 
 />
    <glyph glyph-name="t.titl" unicode="&#xe123;" 
 />
    <glyph glyph-name="u.titl" unicode="&#xe124;" 
 />
    <glyph glyph-name="v.titl" unicode="&#xe125;" 
 />
    <glyph glyph-name="w.titl" unicode="&#xe126;" 
 />
    <glyph glyph-name="x.titl" unicode="&#xe127;" 
 />
    <glyph glyph-name="y.titl" unicode="&#xe128;" 
 />
    <glyph glyph-name="z.titl" unicode="&#xe129;" 
 />
    <glyph glyph-name="a.ss01" unicode="&#xe130;" 
 />
    <glyph glyph-name="b.ss01" unicode="&#xe131;" 
 />
    <glyph glyph-name="c.ss01" unicode="&#xe132;" 
 />
    <glyph glyph-name="d.ss01" unicode="&#xe133;" 
 />
    <glyph glyph-name="e.ss01" unicode="&#xe134;" 
 />
    <glyph glyph-name="f.ss01" unicode="&#xe135;" 
 />
    <glyph glyph-name="g.ss01" unicode="&#xe136;" 
 />
    <glyph glyph-name="h.ss01" unicode="&#xe137;" 
 />
    <glyph glyph-name="i.ss01" unicode="&#xe138;" 
 />
    <glyph glyph-name="j.ss01" unicode="&#xe139;" 
 />
    <glyph glyph-name="k.ss01" unicode="&#xe140;" 
 />
    <glyph glyph-name="l.ss01" unicode="&#xe141;" 
 />
    <glyph glyph-name="m.ss01" unicode="&#xe142;" 
 />
    <glyph glyph-name="n.ss01" unicode="&#xe143;" 
 />
    <glyph glyph-name="o.ss01" unicode="&#xe144;" 
 />
    <glyph glyph-name="p.ss01" unicode="&#xe145;" 
 />
    <glyph glyph-name="q.ss01" unicode="&#xe146;" 
 />
    <glyph glyph-name="r.ss01" unicode="&#xe147;" 
 />
    <glyph glyph-name="s.ss01" unicode="&#xe148;" 
 />
    <glyph glyph-name="t.ss01" unicode="&#xe149;" 
 />
    <glyph glyph-name="u.ss01" unicode="&#xe150;" 
 />
    <glyph glyph-name="v.ss01" unicode="&#xe151;" 
 />
    <glyph glyph-name="w.ss01" unicode="&#xe152;" 
 />
    <glyph glyph-name="x.ss01" unicode="&#xe153;" 
 />
    <glyph glyph-name="y.ss01" unicode="&#xe154;" 
 />
    <glyph glyph-name="z.ss01" unicode="&#xe155;" 
 />
    <glyph glyph-name="a.ss02" unicode="&#xe156;" 
 />
    <glyph glyph-name="b.ss02" unicode="&#xe157;" 
 />
    <glyph glyph-name="c.ss02" unicode="&#xe158;" 
 />
    <glyph glyph-name="d.ss02" unicode="&#xe159;" 
 />
    <glyph glyph-name="e.ss02" unicode="&#xe160;" 
 />
    <glyph glyph-name="f.ss02" unicode="&#xe161;" 
 />
    <glyph glyph-name="g.ss02" unicode="&#xe162;" 
 />
    <glyph glyph-name="h.ss02" unicode="&#xe163;" 
 />
    <glyph glyph-name="i.ss02" unicode="&#xe164;" 
 />
    <glyph glyph-name="j.ss02" unicode="&#xe165;" 
 />
    <glyph glyph-name="k.ss02" unicode="&#xe166;" 
 />
    <glyph glyph-name="l.ss02" unicode="&#xe167;" 
 />
    <glyph glyph-name="m.ss02" unicode="&#xe168;" 
 />
    <glyph glyph-name="n.ss02" unicode="&#xe169;" 
 />
    <glyph glyph-name="o.ss02" unicode="&#xe170;" 
 />
    <glyph glyph-name="p.ss02" unicode="&#xe171;" 
 />
    <glyph glyph-name="q.ss02" unicode="&#xe172;" 
 />
    <glyph glyph-name="r.ss02" unicode="&#xe173;" 
 />
    <glyph glyph-name="s.ss02" unicode="&#xe174;" 
 />
    <glyph glyph-name="t.ss02" unicode="&#xe175;" 
 />
    <glyph glyph-name="u.ss02" unicode="&#xe176;" 
 />
    <glyph glyph-name="v.ss02" unicode="&#xe177;" 
 />
    <glyph glyph-name="w.ss02" unicode="&#xe178;" 
 />
    <glyph glyph-name="x.ss02" unicode="&#xe179;" 
 />
    <glyph glyph-name="y.ss02" unicode="&#xe180;" 
 />
    <glyph glyph-name="z.ss02" unicode="&#xe181;" 
 />
    <glyph glyph-name="a.ss03" unicode="&#xe182;" 
 />
    <glyph glyph-name="b.ss03" unicode="&#xe183;" 
 />
    <glyph glyph-name="c.ss03" unicode="&#xe184;" 
 />
    <glyph glyph-name="d.ss03" unicode="&#xe185;" 
 />
    <glyph glyph-name="uni0E18" unicode="&#xe18;" 
 />
    <glyph glyph-name="f.ss03" unicode="&#xe187;" 
 />
    <glyph glyph-name="g.ss03" unicode="&#xe188;" 
 />
    <glyph glyph-name="h.ss03" unicode="&#xe189;" 
 />
    <glyph glyph-name="i.ss03" unicode="&#xe190;" 
 />
    <glyph glyph-name="j.ss03" unicode="&#xe191;" 
 />
    <glyph glyph-name="k.ss03" unicode="&#xe192;" 
 />
    <glyph glyph-name="l.ss03" unicode="&#xe193;" 
 />
    <glyph glyph-name="m.ss03" unicode="&#xe194;" 
 />
    <glyph glyph-name="n.ss03" unicode="&#xe195;" 
 />
    <glyph glyph-name="o.ss03" unicode="&#xe196;" 
 />
    <glyph glyph-name="p.ss03" unicode="&#xe197;" 
 />
    <glyph glyph-name="q.ss03" unicode="&#xe198;" 
 />
    <glyph glyph-name="r.ss03" unicode="&#xe199;" 
 />
    <glyph glyph-name="s.ss03" unicode="&#xe200;" 
 />
    <glyph glyph-name="t.ss03" unicode="&#xe201;" 
 />
    <glyph glyph-name="u.ss03" unicode="&#xe202;" 
 />
    <glyph glyph-name="v.ss03" unicode="&#xe203;" 
 />
    <glyph glyph-name="w.ss03" unicode="&#xe204;" 
 />
    <glyph glyph-name="x.ss03" unicode="&#xe205;" 
 />
    <glyph glyph-name="y.ss03" unicode="&#xe206;" 
 />
    <glyph glyph-name="z.ss03" unicode="&#xe207;" 
 />
    <glyph glyph-name="ee" unicode="&#xe208;" 
 />
    <glyph glyph-name="gg" unicode="&#xe209;" 
 />
    <glyph glyph-name="ll" unicode="&#xe210;" 
 />
    <glyph glyph-name="oo" unicode="&#xe211;" 
 />
    <glyph glyph-name="rr" unicode="&#xe212;" 
 />
    <glyph glyph-name="ss" unicode="&#xe213;" 
 />
    <glyph glyph-name="tt" unicode="&#xe214;" 
 />
    <glyph glyph-name="th" unicode="&#xe215;" 
 />
    <glyph glyph-name="_1" unicode="&#xe216;" 
 />
    <glyph glyph-name="_2" unicode="&#xe217;" 
 />
    <glyph glyph-name="_3" unicode="&#xe218;" 
 />
    <glyph glyph-name="_4" unicode="&#xe219;" 
 />
    <glyph glyph-name="_5" unicode="&#xe220;" 
 />
    <glyph glyph-name="_6" unicode="&#xe221;" 
 />
    <glyph glyph-name="_7" unicode="&#xe222;" 
 />
    <glyph glyph-name="_8" unicode="&#xe223;" 
 />
    <glyph glyph-name="_9" unicode="&#xe224;" 
 />
    <glyph glyph-name="_10" unicode="&#xe225;" 
 />
    <glyph glyph-name="_11" unicode="&#xe226;" 
 />
    <glyph glyph-name="_12" unicode="&#xe227;" 
 />
    <glyph glyph-name="_13" unicode="&#xe228;" 
 />
    <glyph glyph-name="_14" unicode="&#xe229;" 
 />
    <glyph glyph-name="_15" unicode="&#xe230;" 
 />
    <glyph glyph-name="_16" unicode="&#xe231;" 
 />
    <glyph glyph-name="_17" unicode="&#xe232;" 
 />
    <glyph glyph-name="_18" unicode="&#xe233;" 
 />
    <glyph glyph-name="_19" unicode="&#xe234;" 
 />
    <glyph glyph-name="_20" unicode="&#xe235;" 
 />
    <glyph glyph-name="_21" unicode="&#xe236;" 
 />
    <glyph glyph-name="_22" unicode="&#xe237;" 
 />
    <glyph glyph-name="_23" unicode="&#xe238;" 
 />
    <glyph glyph-name="_24" unicode="&#xe239;" 
 />
    <glyph glyph-name="_25" unicode="&#xe240;" 
 />
    <glyph glyph-name="_26" unicode="&#xe241;" 
 />
    <glyph glyph-name="_27" unicode="&#xe242;" 
 />
    <glyph glyph-name="_28" unicode="&#xe243;" 
 />
    <glyph glyph-name="_29" unicode="&#xe244;" 
 />
    <glyph glyph-name="_30" unicode="&#xe245;" 
 />
    <glyph glyph-name="_31" unicode="&#xe246;" 
 />
    <glyph glyph-name="_32" unicode="&#xe247;" 
 />
    <glyph glyph-name="_33" unicode="&#xe248;" 
 />
    <glyph glyph-name="_34" unicode="&#xe249;" 
 />
    <glyph glyph-name="_35" unicode="&#xe250;" 
 />
    <glyph glyph-name="_36" unicode="&#xe251;" 
 />
    <glyph glyph-name="_37" unicode="&#xe260;" 
 />
    <glyph glyph-name="_38" unicode="&#xe261;" 
 />
    <glyph glyph-name="_39" unicode="&#xe262;" 
 />
    <glyph glyph-name="_40" unicode="&#xe263;" 
 />
    <glyph glyph-name="_41" unicode="&#xe264;" 
 />
    <glyph glyph-name="_42" unicode="&#xe265;" 
 />
    <glyph glyph-name="_43" unicode="&#xe266;" 
 />
    <glyph glyph-name="_44" unicode="&#xe267;" 
 />
    <glyph glyph-name="_45" unicode="&#xe268;" 
 />
    <glyph glyph-name="_46" unicode="&#xe269;" 
 />
    <glyph glyph-name="_47" unicode="&#xe270;" 
 />
    <hkern u1="&#x22;" u2="&#xf0;" k="290" />
    <hkern u1="&#x22;" u2="d" k="290" />
    <hkern u1="&#x26;" u2="&#xff;" k="250" />
    <hkern u1="&#x26;" u2="&#xfd;" k="250" />
    <hkern u1="&#x26;" u2="&#x178;" k="525" />
    <hkern u1="&#x26;" u2="&#xdd;" k="525" />
    <hkern u1="&#x26;" u2="y" k="250" />
    <hkern u1="&#x26;" u2="x" k="125" />
    <hkern u1="&#x26;" u2="w" k="250" />
    <hkern u1="&#x26;" u2="v" k="250" />
    <hkern u1="&#x26;" u2="j" k="75" />
    <hkern u1="&#x26;" u2="f" k="100" />
    <hkern u1="&#x26;" u2="Y" k="525" />
    <hkern u1="&#x26;" u2="X" k="125" />
    <hkern u1="&#x26;" u2="W" k="400" />
    <hkern u1="&#x26;" u2="V" k="450" />
    <hkern u1="&#x26;" u2="T" k="325" />
    <hkern u1="&#x27;" u2="&#xf0;" k="290" />
    <hkern u1="&#x27;" u2="d" k="290" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="290" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="300" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="300" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="300" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="300" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="300" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="300" />
    <hkern u1="&#x2a;" u2="d" k="290" />
    <hkern u1="&#x2a;" u2="A" k="300" />
    <hkern u1="&#x2f;" u2="&#xff;" k="250" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="250" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="250" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="250" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="250" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="250" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="425" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="425" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="425" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="425" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="425" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="425" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="250" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="425" />
    <hkern u1="&#x2f;" u2="&#xef;" k="125" />
    <hkern u1="&#x2f;" u2="&#xee;" k="125" />
    <hkern u1="&#x2f;" u2="&#xed;" k="125" />
    <hkern u1="&#x2f;" u2="&#xec;" k="125" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="425" />
    <hkern u1="&#x2f;" u2="&#xea;" k="425" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="425" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="425" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="425" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="450" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="450" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="450" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="450" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="450" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="450" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="150" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="150" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="150" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="150" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="150" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="150" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="125" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="650" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="650" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="650" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="650" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="650" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="650" />
    <hkern u1="&#x2f;" u2="z" k="250" />
    <hkern u1="&#x2f;" u2="y" k="250" />
    <hkern u1="&#x2f;" u2="x" k="250" />
    <hkern u1="&#x2f;" u2="w" k="250" />
    <hkern u1="&#x2f;" u2="v" k="250" />
    <hkern u1="&#x2f;" u2="u" k="250" />
    <hkern u1="&#x2f;" u2="t" k="225" />
    <hkern u1="&#x2f;" u2="s" k="250" />
    <hkern u1="&#x2f;" u2="r" k="250" />
    <hkern u1="&#x2f;" u2="q" k="425" />
    <hkern u1="&#x2f;" u2="p" k="250" />
    <hkern u1="&#x2f;" u2="o" k="425" />
    <hkern u1="&#x2f;" u2="n" k="250" />
    <hkern u1="&#x2f;" u2="m" k="250" />
    <hkern u1="&#x2f;" u2="j" k="175" />
    <hkern u1="&#x2f;" u2="i" k="125" />
    <hkern u1="&#x2f;" u2="g" k="425" />
    <hkern u1="&#x2f;" u2="f" k="275" />
    <hkern u1="&#x2f;" u2="e" k="425" />
    <hkern u1="&#x2f;" u2="d" k="425" />
    <hkern u1="&#x2f;" u2="c" k="425" />
    <hkern u1="&#x2f;" u2="a" k="450" />
    <hkern u1="&#x2f;" u2="Z" k="125" />
    <hkern u1="&#x2f;" u2="S" k="125" />
    <hkern u1="&#x2f;" u2="Q" k="150" />
    <hkern u1="&#x2f;" u2="O" k="150" />
    <hkern u1="&#x2f;" u2="J" k="575" />
    <hkern u1="&#x2f;" u2="G" k="125" />
    <hkern u1="&#x2f;" u2="C" k="125" />
    <hkern u1="&#x2f;" u2="A" k="650" />
    <hkern u1="&#x2f;" u2="&#x39;" k="150" />
    <hkern u1="&#x2f;" u2="&#x38;" k="175" />
    <hkern u1="&#x2f;" u2="&#x37;" k="125" />
    <hkern u1="&#x2f;" u2="&#x36;" k="300" />
    <hkern u1="&#x2f;" u2="&#x35;" k="125" />
    <hkern u1="&#x2f;" u2="&#x34;" k="600" />
    <hkern u1="&#x2f;" u2="&#x33;" k="150" />
    <hkern u1="&#x2f;" u2="&#x32;" k="225" />
    <hkern u1="&#x2f;" u2="&#x30;" k="200" />
    <hkern u1="&#x30;" u2="&#x178;" k="225" />
    <hkern u1="&#x30;" u2="&#xdd;" k="225" />
    <hkern u1="&#x30;" u2="&#xc5;" k="150" />
    <hkern u1="&#x30;" u2="&#xc4;" k="150" />
    <hkern u1="&#x30;" u2="&#xc3;" k="150" />
    <hkern u1="&#x30;" u2="&#xc2;" k="150" />
    <hkern u1="&#x30;" u2="&#xc1;" k="150" />
    <hkern u1="&#x30;" u2="&#xc0;" k="150" />
    <hkern u1="&#x30;" u2="\" k="200" />
    <hkern u1="&#x30;" u2="Z" k="175" />
    <hkern u1="&#x30;" u2="Y" k="225" />
    <hkern u1="&#x30;" u2="X" k="250" />
    <hkern u1="&#x30;" u2="W" k="150" />
    <hkern u1="&#x30;" u2="V" k="175" />
    <hkern u1="&#x30;" u2="T" k="100" />
    <hkern u1="&#x30;" u2="A" k="150" />
    <hkern u1="&#x30;" u2="&#x39;" k="-50" />
    <hkern u1="&#x30;" u2="&#x38;" k="-50" />
    <hkern u1="&#x30;" u2="&#x37;" k="200" />
    <hkern u1="&#x30;" u2="&#x36;" k="-50" />
    <hkern u1="&#x30;" u2="&#x32;" k="75" />
    <hkern u1="&#x30;" u2="&#x30;" k="-50" />
    <hkern u1="&#x30;" u2="&#x2f;" k="200" />
    <hkern u1="&#x31;" u2="&#x34;" k="50" />
    <hkern u1="&#x31;" u2="&#x30;" k="-50" />
    <hkern u1="&#x32;" u2="&#x178;" k="100" />
    <hkern u1="&#x32;" u2="&#xdd;" k="100" />
    <hkern u1="&#x32;" u2="Y" k="100" />
    <hkern u1="&#x32;" u2="X" k="100" />
    <hkern u1="&#x32;" u2="&#x34;" k="290" />
    <hkern u1="&#x32;" u2="&#x30;" k="-50" />
    <hkern u1="&#x33;" u2="&#xff;" k="150" />
    <hkern u1="&#x33;" u2="&#xfd;" k="150" />
    <hkern u1="&#x33;" u2="&#x178;" k="150" />
    <hkern u1="&#x33;" u2="&#xdd;" k="150" />
    <hkern u1="&#x33;" u2="&#xc5;" k="100" />
    <hkern u1="&#x33;" u2="&#xc4;" k="100" />
    <hkern u1="&#x33;" u2="&#xc3;" k="100" />
    <hkern u1="&#x33;" u2="&#xc2;" k="100" />
    <hkern u1="&#x33;" u2="&#xc1;" k="100" />
    <hkern u1="&#x33;" u2="&#xc0;" k="100" />
    <hkern u1="&#x33;" u2="y" k="150" />
    <hkern u1="&#x33;" u2="x" k="150" />
    <hkern u1="&#x33;" u2="v" k="125" />
    <hkern u1="&#x33;" u2="\" k="100" />
    <hkern u1="&#x33;" u2="Y" k="150" />
    <hkern u1="&#x33;" u2="X" k="200" />
    <hkern u1="&#x33;" u2="W" k="175" />
    <hkern u1="&#x33;" u2="V" k="175" />
    <hkern u1="&#x33;" u2="T" k="225" />
    <hkern u1="&#x33;" u2="A" k="100" />
    <hkern u1="&#x33;" u2="&#x39;" k="90" />
    <hkern u1="&#x33;" u2="&#x37;" k="90" />
    <hkern u1="&#x33;" u2="&#x32;" k="75" />
    <hkern u1="&#x33;" u2="&#x31;" k="75" />
    <hkern u1="&#x33;" u2="&#x30;" k="-50" />
    <hkern u1="&#x33;" u2="&#x2f;" k="125" />
    <hkern u1="&#x34;" u2="&#x178;" k="150" />
    <hkern u1="&#x34;" u2="&#xdd;" k="150" />
    <hkern u1="&#x34;" u2="\" k="100" />
    <hkern u1="&#x34;" u2="Y" k="150" />
    <hkern u1="&#x34;" u2="X" k="150" />
    <hkern u1="&#x34;" u2="W" k="125" />
    <hkern u1="&#x34;" u2="V" k="150" />
    <hkern u1="&#x34;" u2="T" k="150" />
    <hkern u1="&#x34;" u2="&#x37;" k="50" />
    <hkern u1="&#x34;" u2="&#x32;" k="40" />
    <hkern u1="&#x34;" u2="&#x31;" k="75" />
    <hkern u1="&#x34;" u2="&#x2f;" k="100" />
    <hkern u1="&#x35;" u2="&#xff;" k="150" />
    <hkern u1="&#x35;" u2="&#xfd;" k="150" />
    <hkern u1="&#x35;" u2="&#x178;" k="150" />
    <hkern u1="&#x35;" u2="&#xdd;" k="150" />
    <hkern u1="&#x35;" u2="&#xc5;" k="100" />
    <hkern u1="&#x35;" u2="&#xc4;" k="100" />
    <hkern u1="&#x35;" u2="&#xc3;" k="100" />
    <hkern u1="&#x35;" u2="&#xc2;" k="100" />
    <hkern u1="&#x35;" u2="&#xc1;" k="100" />
    <hkern u1="&#x35;" u2="&#xc0;" k="100" />
    <hkern u1="&#x35;" u2="y" k="150" />
    <hkern u1="&#x35;" u2="x" k="150" />
    <hkern u1="&#x35;" u2="v" k="125" />
    <hkern u1="&#x35;" u2="\" k="100" />
    <hkern u1="&#x35;" u2="Y" k="150" />
    <hkern u1="&#x35;" u2="X" k="175" />
    <hkern u1="&#x35;" u2="W" k="100" />
    <hkern u1="&#x35;" u2="V" k="175" />
    <hkern u1="&#x35;" u2="T" k="100" />
    <hkern u1="&#x35;" u2="A" k="100" />
    <hkern u1="&#x35;" u2="&#x39;" k="70" />
    <hkern u1="&#x35;" u2="&#x37;" k="80" />
    <hkern u1="&#x35;" u2="&#x31;" k="50" />
    <hkern u1="&#x35;" u2="&#x30;" k="-50" />
    <hkern u1="&#x35;" u2="&#x2f;" k="125" />
    <hkern u1="&#x36;" u2="&#xff;" k="150" />
    <hkern u1="&#x36;" u2="&#xfd;" k="150" />
    <hkern u1="&#x36;" u2="&#x178;" k="400" />
    <hkern u1="&#x36;" u2="&#xdd;" k="400" />
    <hkern u1="&#x36;" u2="&#xc5;" k="100" />
    <hkern u1="&#x36;" u2="&#xc4;" k="100" />
    <hkern u1="&#x36;" u2="&#xc3;" k="100" />
    <hkern u1="&#x36;" u2="&#xc2;" k="100" />
    <hkern u1="&#x36;" u2="&#xc1;" k="100" />
    <hkern u1="&#x36;" u2="&#xc0;" k="100" />
    <hkern u1="&#x36;" u2="y" k="150" />
    <hkern u1="&#x36;" u2="x" k="150" />
    <hkern u1="&#x36;" u2="v" k="125" />
    <hkern u1="&#x36;" u2="\" k="350" />
    <hkern u1="&#x36;" u2="Y" k="400" />
    <hkern u1="&#x36;" u2="X" k="175" />
    <hkern u1="&#x36;" u2="W" k="275" />
    <hkern u1="&#x36;" u2="V" k="400" />
    <hkern u1="&#x36;" u2="T" k="350" />
    <hkern u1="&#x36;" u2="A" k="100" />
    <hkern u1="&#x36;" u2="&#x39;" k="170" />
    <hkern u1="&#x36;" u2="&#x37;" k="70" />
    <hkern u1="&#x36;" u2="&#x32;" k="70" />
    <hkern u1="&#x36;" u2="&#x31;" k="75" />
    <hkern u1="&#x36;" u2="&#x30;" k="-50" />
    <hkern u1="&#x36;" u2="&#x2f;" k="125" />
    <hkern u1="&#x37;" u2="&#xff;" k="250" />
    <hkern u1="&#x37;" u2="&#xfd;" k="250" />
    <hkern u1="&#x37;" u2="&#xfc;" k="150" />
    <hkern u1="&#x37;" u2="&#xfb;" k="150" />
    <hkern u1="&#x37;" u2="&#xfa;" k="150" />
    <hkern u1="&#x37;" u2="&#xf9;" k="150" />
    <hkern u1="&#x37;" u2="&#xf8;" k="300" />
    <hkern u1="&#x37;" u2="&#xf7;" k="200" />
    <hkern u1="&#x37;" u2="&#xf6;" k="300" />
    <hkern u1="&#x37;" u2="&#xf5;" k="300" />
    <hkern u1="&#x37;" u2="&#xf4;" k="300" />
    <hkern u1="&#x37;" u2="&#xf3;" k="300" />
    <hkern u1="&#x37;" u2="&#xf2;" k="300" />
    <hkern u1="&#x37;" u2="&#xf1;" k="150" />
    <hkern u1="&#x37;" u2="&#xf0;" k="300" />
    <hkern u1="&#x37;" u2="&#xef;" k="125" />
    <hkern u1="&#x37;" u2="&#xee;" k="125" />
    <hkern u1="&#x37;" u2="&#xed;" k="125" />
    <hkern u1="&#x37;" u2="&#xec;" k="125" />
    <hkern u1="&#x37;" u2="&#xeb;" k="300" />
    <hkern u1="&#x37;" u2="&#xea;" k="300" />
    <hkern u1="&#x37;" u2="&#xe9;" k="300" />
    <hkern u1="&#x37;" u2="&#xe8;" k="300" />
    <hkern u1="&#x37;" u2="&#xe7;" k="300" />
    <hkern u1="&#x37;" u2="&#xe5;" k="350" />
    <hkern u1="&#x37;" u2="&#xe4;" k="350" />
    <hkern u1="&#x37;" u2="&#xe3;" k="350" />
    <hkern u1="&#x37;" u2="&#xe2;" k="350" />
    <hkern u1="&#x37;" u2="&#xe1;" k="350" />
    <hkern u1="&#x37;" u2="&#xe0;" k="350" />
    <hkern u1="&#x37;" u2="&#x178;" k="50" />
    <hkern u1="&#x37;" u2="&#xdd;" k="50" />
    <hkern u1="&#x37;" u2="&#xd7;" k="175" />
    <hkern u1="&#x37;" u2="&#xc7;" k="125" />
    <hkern u1="&#x37;" u2="&#xc5;" k="500" />
    <hkern u1="&#x37;" u2="&#xc4;" k="500" />
    <hkern u1="&#x37;" u2="&#xc3;" k="500" />
    <hkern u1="&#x37;" u2="&#xc2;" k="500" />
    <hkern u1="&#x37;" u2="&#xc1;" k="500" />
    <hkern u1="&#x37;" u2="&#xc0;" k="500" />
    <hkern u1="&#x37;" u2="z" k="300" />
    <hkern u1="&#x37;" u2="y" k="250" />
    <hkern u1="&#x37;" u2="x" k="225" />
    <hkern u1="&#x37;" u2="w" k="250" />
    <hkern u1="&#x37;" u2="v" k="250" />
    <hkern u1="&#x37;" u2="u" k="150" />
    <hkern u1="&#x37;" u2="t" k="150" />
    <hkern u1="&#x37;" u2="s" k="175" />
    <hkern u1="&#x37;" u2="r" k="150" />
    <hkern u1="&#x37;" u2="q" k="300" />
    <hkern u1="&#x37;" u2="p" k="150" />
    <hkern u1="&#x37;" u2="o" k="300" />
    <hkern u1="&#x37;" u2="n" k="150" />
    <hkern u1="&#x37;" u2="m" k="150" />
    <hkern u1="&#x37;" u2="j" k="225" />
    <hkern u1="&#x37;" u2="i" k="125" />
    <hkern u1="&#x37;" u2="g" k="300" />
    <hkern u1="&#x37;" u2="f" k="175" />
    <hkern u1="&#x37;" u2="e" k="300" />
    <hkern u1="&#x37;" u2="d" k="300" />
    <hkern u1="&#x37;" u2="c" k="300" />
    <hkern u1="&#x37;" u2="a" k="350" />
    <hkern u1="&#x37;" u2="_" k="400" />
    <hkern u1="&#x37;" u2="Z" k="200" />
    <hkern u1="&#x37;" u2="Y" k="50" />
    <hkern u1="&#x37;" u2="X" k="100" />
    <hkern u1="&#x37;" u2="T" k="50" />
    <hkern u1="&#x37;" u2="S" k="125" />
    <hkern u1="&#x37;" u2="J" k="500" />
    <hkern u1="&#x37;" u2="G" k="150" />
    <hkern u1="&#x37;" u2="C" k="125" />
    <hkern u1="&#x37;" u2="A" k="500" />
    <hkern u1="&#x37;" u2="&#x3d;" k="200" />
    <hkern u1="&#x37;" u2="&#x3c;" k="300" />
    <hkern u1="&#x37;" u2="&#x3b;" k="250" />
    <hkern u1="&#x37;" u2="&#x3a;" k="250" />
    <hkern u1="&#x37;" u2="&#x39;" k="80" />
    <hkern u1="&#x37;" u2="&#x38;" k="140" />
    <hkern u1="&#x37;" u2="&#x37;" k="70" />
    <hkern u1="&#x37;" u2="&#x36;" k="150" />
    <hkern u1="&#x37;" u2="&#x35;" k="50" />
    <hkern u1="&#x37;" u2="&#x34;" k="400" />
    <hkern u1="&#x37;" u2="&#x33;" k="100" />
    <hkern u1="&#x37;" u2="&#x32;" k="135" />
    <hkern u1="&#x37;" u2="&#x30;" k="190" />
    <hkern u1="&#x37;" u2="&#x2f;" k="600" />
    <hkern u1="&#x37;" u2="&#x2d;" k="225" />
    <hkern u1="&#x37;" u2="&#x2b;" k="250" />
    <hkern u1="&#x38;" u2="&#xff;" k="150" />
    <hkern u1="&#x38;" u2="&#xfd;" k="150" />
    <hkern u1="&#x38;" u2="&#x178;" k="250" />
    <hkern u1="&#x38;" u2="&#xdd;" k="250" />
    <hkern u1="&#x38;" u2="&#xc5;" k="75" />
    <hkern u1="&#x38;" u2="&#xc4;" k="75" />
    <hkern u1="&#x38;" u2="&#xc3;" k="75" />
    <hkern u1="&#x38;" u2="&#xc2;" k="75" />
    <hkern u1="&#x38;" u2="&#xc1;" k="75" />
    <hkern u1="&#x38;" u2="&#xc0;" k="75" />
    <hkern u1="&#x38;" u2="y" k="150" />
    <hkern u1="&#x38;" u2="x" k="75" />
    <hkern u1="&#x38;" u2="\" k="150" />
    <hkern u1="&#x38;" u2="Y" k="250" />
    <hkern u1="&#x38;" u2="X" k="175" />
    <hkern u1="&#x38;" u2="W" k="125" />
    <hkern u1="&#x38;" u2="V" k="175" />
    <hkern u1="&#x38;" u2="S" k="50" />
    <hkern u1="&#x38;" u2="A" k="75" />
    <hkern u1="&#x38;" u2="&#x39;" k="75" />
    <hkern u1="&#x38;" u2="&#x37;" k="100" />
    <hkern u1="&#x38;" u2="&#x32;" k="50" />
    <hkern u1="&#x38;" u2="&#x31;" k="50" />
    <hkern u1="&#x38;" u2="&#x30;" k="-50" />
    <hkern u1="&#x38;" u2="&#x2f;" k="125" />
    <hkern u1="&#x39;" u2="&#x178;" k="200" />
    <hkern u1="&#x39;" u2="&#xdd;" k="200" />
    <hkern u1="&#x39;" u2="&#xc5;" k="300" />
    <hkern u1="&#x39;" u2="&#xc4;" k="300" />
    <hkern u1="&#x39;" u2="&#xc3;" k="300" />
    <hkern u1="&#x39;" u2="&#xc2;" k="300" />
    <hkern u1="&#x39;" u2="&#xc1;" k="300" />
    <hkern u1="&#x39;" u2="&#xc0;" k="300" />
    <hkern u1="&#x39;" u2="\" k="150" />
    <hkern u1="&#x39;" u2="Z" k="125" />
    <hkern u1="&#x39;" u2="Y" k="200" />
    <hkern u1="&#x39;" u2="X" k="125" />
    <hkern u1="&#x39;" u2="W" k="100" />
    <hkern u1="&#x39;" u2="V" k="100" />
    <hkern u1="&#x39;" u2="A" k="300" />
    <hkern u1="&#x39;" u2="&#x37;" k="150" />
    <hkern u1="&#x39;" u2="&#x34;" k="90" />
    <hkern u1="&#x39;" u2="&#x33;" k="50" />
    <hkern u1="&#x39;" u2="&#x32;" k="75" />
    <hkern u1="&#x39;" u2="&#x30;" k="-50" />
    <hkern u1="&#x39;" u2="&#x2f;" k="350" />
    <hkern u1="&#x40;" u2="&#xe5;" k="75" />
    <hkern u1="&#x40;" u2="&#xe4;" k="75" />
    <hkern u1="&#x40;" u2="&#xe3;" k="75" />
    <hkern u1="&#x40;" u2="&#xe2;" k="75" />
    <hkern u1="&#x40;" u2="&#xe1;" k="75" />
    <hkern u1="&#x40;" u2="&#xe0;" k="75" />
    <hkern u1="&#x40;" u2="j" k="75" />
    <hkern u1="&#x40;" u2="a" k="75" />
    <hkern u1="A" u2="&#xfc;" k="125" />
    <hkern u1="A" u2="&#xfb;" k="125" />
    <hkern u1="A" u2="&#xfa;" k="125" />
    <hkern u1="A" u2="&#xf9;" k="125" />
    <hkern u1="A" u2="&#xf8;" k="125" />
    <hkern u1="A" u2="&#xf6;" k="125" />
    <hkern u1="A" u2="&#xf5;" k="125" />
    <hkern u1="A" u2="&#xf4;" k="125" />
    <hkern u1="A" u2="&#xf3;" k="125" />
    <hkern u1="A" u2="&#xf2;" k="125" />
    <hkern u1="A" u2="&#xf0;" k="125" />
    <hkern u1="A" u2="&#xeb;" k="125" />
    <hkern u1="A" u2="&#xea;" k="125" />
    <hkern u1="A" u2="&#xe9;" k="125" />
    <hkern u1="A" u2="&#xe8;" k="125" />
    <hkern u1="A" u2="&#xe7;" k="125" />
    <hkern u1="A" u2="&#xe5;" k="75" />
    <hkern u1="A" u2="&#xe4;" k="75" />
    <hkern u1="A" u2="&#xe3;" k="75" />
    <hkern u1="A" u2="&#xe2;" k="75" />
    <hkern u1="A" u2="&#xe1;" k="75" />
    <hkern u1="A" u2="&#xe0;" k="75" />
    <hkern u1="A" u2="&#x178;" k="600" />
    <hkern u1="A" u2="&#xdd;" k="600" />
    <hkern u1="A" u2="&#xdc;" k="175" />
    <hkern u1="A" u2="&#xdb;" k="175" />
    <hkern u1="A" u2="&#xda;" k="175" />
    <hkern u1="A" u2="&#xd9;" k="175" />
    <hkern u1="A" u2="&#xd8;" k="225" />
    <hkern u1="A" u2="&#xd6;" k="225" />
    <hkern u1="A" u2="&#xd5;" k="225" />
    <hkern u1="A" u2="&#xd4;" k="225" />
    <hkern u1="A" u2="&#xd3;" k="225" />
    <hkern u1="A" u2="&#xd2;" k="225" />
    <hkern u1="A" u2="&#xd1;" k="50" />
    <hkern u1="A" u2="&#xcf;" k="50" />
    <hkern u1="A" u2="&#xce;" k="50" />
    <hkern u1="A" u2="&#xcd;" k="50" />
    <hkern u1="A" u2="&#xcc;" k="50" />
    <hkern u1="A" u2="&#xc7;" k="225" />
    <hkern u1="A" u2="&#xc5;" k="75" />
    <hkern u1="A" u2="&#xc4;" k="75" />
    <hkern u1="A" u2="&#xc3;" k="75" />
    <hkern u1="A" u2="&#xc2;" k="75" />
    <hkern u1="A" u2="&#xc1;" k="75" />
    <hkern u1="A" u2="&#xc0;" k="75" />
    <hkern u1="A" u2="v" k="375" />
    <hkern u1="A" u2="u" k="125" />
    <hkern u1="A" u2="t" k="125" />
    <hkern u1="A" u2="q" k="125" />
    <hkern u1="A" u2="o" k="125" />
    <hkern u1="A" u2="j" k="62" />
    <hkern u1="A" u2="g" k="50" />
    <hkern u1="A" u2="f" k="125" />
    <hkern u1="A" u2="e" k="125" />
    <hkern u1="A" u2="d" k="125" />
    <hkern u1="A" u2="c" k="125" />
    <hkern u1="A" u2="a" k="75" />
    <hkern u1="A" u2="^" k="375" />
    <hkern u1="A" u2="\" k="700" />
    <hkern u1="A" u2="Z" k="50" />
    <hkern u1="A" u2="Y" k="600" />
    <hkern u1="A" u2="X" k="75" />
    <hkern u1="A" u2="W" k="475" />
    <hkern u1="A" u2="V" k="600" />
    <hkern u1="A" u2="U" k="175" />
    <hkern u1="A" u2="T" k="475" />
    <hkern u1="A" u2="S" k="75" />
    <hkern u1="A" u2="R" k="50" />
    <hkern u1="A" u2="Q" k="225" />
    <hkern u1="A" u2="P" k="50" />
    <hkern u1="A" u2="O" k="225" />
    <hkern u1="A" u2="N" k="50" />
    <hkern u1="A" u2="M" k="50" />
    <hkern u1="A" u2="L" k="50" />
    <hkern u1="A" u2="K" k="50" />
    <hkern u1="A" u2="J" k="225" />
    <hkern u1="A" u2="I" k="50" />
    <hkern u1="A" u2="H" k="50" />
    <hkern u1="A" u2="G" k="225" />
    <hkern u1="A" u2="C" k="225" />
    <hkern u1="A" u2="B" k="50" />
    <hkern u1="A" u2="A" k="75" />
    <hkern u1="A" u2="&#x39;" k="350" />
    <hkern u1="A" u2="&#x38;" k="100" />
    <hkern u1="A" u2="&#x36;" k="100" />
    <hkern u1="A" u2="&#x35;" k="100" />
    <hkern u1="A" u2="&#x34;" k="150" />
    <hkern u1="A" u2="&#x33;" k="100" />
    <hkern u1="A" u2="&#x31;" k="75" />
    <hkern u1="A" u2="&#x30;" k="150" />
    <hkern u1="A" u2="&#x2a;" k="300" />
    <hkern u1="B" u2="&#xff;" k="125" />
    <hkern u1="B" u2="&#xfd;" k="125" />
    <hkern u1="B" u2="&#x178;" k="225" />
    <hkern u1="B" u2="&#xdd;" k="225" />
    <hkern u1="B" u2="&#xc5;" k="100" />
    <hkern u1="B" u2="&#xc4;" k="100" />
    <hkern u1="B" u2="&#xc3;" k="100" />
    <hkern u1="B" u2="&#xc2;" k="100" />
    <hkern u1="B" u2="&#xc1;" k="100" />
    <hkern u1="B" u2="&#xc0;" k="100" />
    <hkern u1="B" u2="z" k="75" />
    <hkern u1="B" u2="y" k="125" />
    <hkern u1="B" u2="x" k="75" />
    <hkern u1="B" u2="w" k="125" />
    <hkern u1="B" u2="v" k="125" />
    <hkern u1="B" u2="t" k="50" />
    <hkern u1="B" u2="j" k="75" />
    <hkern u1="B" u2="f" k="125" />
    <hkern u1="B" u2="\" k="225" />
    <hkern u1="B" u2="Z" k="75" />
    <hkern u1="B" u2="Y" k="225" />
    <hkern u1="B" u2="X" k="125" />
    <hkern u1="B" u2="W" k="200" />
    <hkern u1="B" u2="V" k="225" />
    <hkern u1="B" u2="T" k="150" />
    <hkern u1="B" u2="J" k="75" />
    <hkern u1="B" u2="A" k="100" />
    <hkern u1="C" u2="&#xff;" k="125" />
    <hkern u1="C" u2="&#xfd;" k="125" />
    <hkern u1="C" u2="&#xfc;" k="75" />
    <hkern u1="C" u2="&#xfb;" k="75" />
    <hkern u1="C" u2="&#xfa;" k="75" />
    <hkern u1="C" u2="&#xf9;" k="75" />
    <hkern u1="C" u2="&#xf8;" k="75" />
    <hkern u1="C" u2="&#xf6;" k="75" />
    <hkern u1="C" u2="&#xf5;" k="75" />
    <hkern u1="C" u2="&#xf4;" k="75" />
    <hkern u1="C" u2="&#xf3;" k="75" />
    <hkern u1="C" u2="&#xf2;" k="75" />
    <hkern u1="C" u2="&#xf0;" k="75" />
    <hkern u1="C" u2="&#xeb;" k="75" />
    <hkern u1="C" u2="&#xea;" k="75" />
    <hkern u1="C" u2="&#xe9;" k="75" />
    <hkern u1="C" u2="&#xe8;" k="75" />
    <hkern u1="C" u2="&#xe7;" k="75" />
    <hkern u1="C" u2="&#xd8;" k="25" />
    <hkern u1="C" u2="&#xd6;" k="25" />
    <hkern u1="C" u2="&#xd5;" k="25" />
    <hkern u1="C" u2="&#xd4;" k="25" />
    <hkern u1="C" u2="&#xd3;" k="25" />
    <hkern u1="C" u2="&#xd2;" k="25" />
    <hkern u1="C" u2="&#xc7;" k="50" />
    <hkern u1="C" u2="&#xc5;" k="100" />
    <hkern u1="C" u2="&#xc4;" k="100" />
    <hkern u1="C" u2="&#xc3;" k="100" />
    <hkern u1="C" u2="&#xc2;" k="100" />
    <hkern u1="C" u2="&#xc1;" k="100" />
    <hkern u1="C" u2="&#xc0;" k="100" />
    <hkern u1="C" u2="z" k="75" />
    <hkern u1="C" u2="y" k="125" />
    <hkern u1="C" u2="x" k="75" />
    <hkern u1="C" u2="w" k="125" />
    <hkern u1="C" u2="v" k="125" />
    <hkern u1="C" u2="u" k="75" />
    <hkern u1="C" u2="q" k="75" />
    <hkern u1="C" u2="o" k="75" />
    <hkern u1="C" u2="e" k="75" />
    <hkern u1="C" u2="d" k="75" />
    <hkern u1="C" u2="c" k="75" />
    <hkern u1="C" u2="Z" k="75" />
    <hkern u1="C" u2="X" k="75" />
    <hkern u1="C" u2="W" k="50" />
    <hkern u1="C" u2="V" k="50" />
    <hkern u1="C" u2="Q" k="25" />
    <hkern u1="C" u2="O" k="25" />
    <hkern u1="C" u2="J" k="125" />
    <hkern u1="C" u2="C" k="50" />
    <hkern u1="C" u2="A" k="100" />
    <hkern u1="C" u2="&#x34;" k="200" />
    <hkern u1="D" u2="&#xff;" k="50" />
    <hkern u1="D" u2="&#xfd;" k="50" />
    <hkern u1="D" u2="&#xf8;" k="-25" />
    <hkern u1="D" u2="&#xf6;" k="-25" />
    <hkern u1="D" u2="&#xf5;" k="-25" />
    <hkern u1="D" u2="&#xf4;" k="-25" />
    <hkern u1="D" u2="&#xf3;" k="-25" />
    <hkern u1="D" u2="&#xf2;" k="-25" />
    <hkern u1="D" u2="&#xf0;" k="-25" />
    <hkern u1="D" u2="&#xeb;" k="-25" />
    <hkern u1="D" u2="&#xea;" k="-25" />
    <hkern u1="D" u2="&#xe9;" k="-25" />
    <hkern u1="D" u2="&#xe8;" k="-25" />
    <hkern u1="D" u2="&#xe7;" k="-25" />
    <hkern u1="D" u2="&#xe5;" k="50" />
    <hkern u1="D" u2="&#xe4;" k="50" />
    <hkern u1="D" u2="&#xe3;" k="50" />
    <hkern u1="D" u2="&#xe2;" k="50" />
    <hkern u1="D" u2="&#xe1;" k="50" />
    <hkern u1="D" u2="&#xe0;" k="50" />
    <hkern u1="D" u2="&#x178;" k="275" />
    <hkern u1="D" u2="&#xdd;" k="275" />
    <hkern u1="D" u2="&#xd8;" k="-50" />
    <hkern u1="D" u2="&#xd6;" k="-50" />
    <hkern u1="D" u2="&#xd5;" k="-50" />
    <hkern u1="D" u2="&#xd4;" k="-50" />
    <hkern u1="D" u2="&#xd3;" k="-50" />
    <hkern u1="D" u2="&#xd2;" k="-50" />
    <hkern u1="D" u2="&#xc7;" k="-50" />
    <hkern u1="D" u2="&#xc5;" k="225" />
    <hkern u1="D" u2="&#xc4;" k="225" />
    <hkern u1="D" u2="&#xc3;" k="225" />
    <hkern u1="D" u2="&#xc2;" k="225" />
    <hkern u1="D" u2="&#xc1;" k="225" />
    <hkern u1="D" u2="&#xc0;" k="225" />
    <hkern u1="D" u2="z" k="125" />
    <hkern u1="D" u2="y" k="50" />
    <hkern u1="D" u2="x" k="75" />
    <hkern u1="D" u2="w" k="75" />
    <hkern u1="D" u2="v" k="75" />
    <hkern u1="D" u2="q" k="-25" />
    <hkern u1="D" u2="o" k="-25" />
    <hkern u1="D" u2="j" k="25" />
    <hkern u1="D" u2="e" k="-25" />
    <hkern u1="D" u2="d" k="-25" />
    <hkern u1="D" u2="c" k="-25" />
    <hkern u1="D" u2="a" k="50" />
    <hkern u1="D" u2="\" k="250" />
    <hkern u1="D" u2="Z" k="150" />
    <hkern u1="D" u2="Y" k="275" />
    <hkern u1="D" u2="X" k="250" />
    <hkern u1="D" u2="W" k="175" />
    <hkern u1="D" u2="V" k="225" />
    <hkern u1="D" u2="T" k="225" />
    <hkern u1="D" u2="S" k="75" />
    <hkern u1="D" u2="Q" k="-50" />
    <hkern u1="D" u2="O" k="-50" />
    <hkern u1="D" u2="J" k="225" />
    <hkern u1="D" u2="C" k="-50" />
    <hkern u1="D" u2="A" k="225" />
    <hkern u1="D" u2="&#x37;" k="150" />
    <hkern u1="E" u2="&#xff;" k="150" />
    <hkern u1="E" u2="&#xfd;" k="150" />
    <hkern u1="E" u2="&#xfc;" k="50" />
    <hkern u1="E" u2="&#xfb;" k="50" />
    <hkern u1="E" u2="&#xfa;" k="50" />
    <hkern u1="E" u2="&#xf9;" k="50" />
    <hkern u1="E" u2="&#xf8;" k="75" />
    <hkern u1="E" u2="&#xf6;" k="75" />
    <hkern u1="E" u2="&#xf5;" k="75" />
    <hkern u1="E" u2="&#xf4;" k="75" />
    <hkern u1="E" u2="&#xf3;" k="75" />
    <hkern u1="E" u2="&#xf2;" k="75" />
    <hkern u1="E" u2="&#xf0;" k="50" />
    <hkern u1="E" u2="&#xeb;" k="75" />
    <hkern u1="E" u2="&#xea;" k="75" />
    <hkern u1="E" u2="&#xe9;" k="75" />
    <hkern u1="E" u2="&#xe8;" k="75" />
    <hkern u1="E" u2="&#xe7;" k="50" />
    <hkern u1="E" u2="&#xd8;" k="75" />
    <hkern u1="E" u2="&#xd6;" k="75" />
    <hkern u1="E" u2="&#xd5;" k="75" />
    <hkern u1="E" u2="&#xd4;" k="75" />
    <hkern u1="E" u2="&#xd3;" k="75" />
    <hkern u1="E" u2="&#xd2;" k="75" />
    <hkern u1="E" u2="&#xc7;" k="75" />
    <hkern u1="E" u2="&#xc5;" k="75" />
    <hkern u1="E" u2="&#xc4;" k="75" />
    <hkern u1="E" u2="&#xc3;" k="75" />
    <hkern u1="E" u2="&#xc2;" k="75" />
    <hkern u1="E" u2="&#xc1;" k="75" />
    <hkern u1="E" u2="&#xc0;" k="75" />
    <hkern u1="E" u2="y" k="150" />
    <hkern u1="E" u2="x" k="50" />
    <hkern u1="E" u2="w" k="150" />
    <hkern u1="E" u2="v" k="150" />
    <hkern u1="E" u2="u" k="50" />
    <hkern u1="E" u2="t" k="50" />
    <hkern u1="E" u2="q" k="50" />
    <hkern u1="E" u2="o" k="75" />
    <hkern u1="E" u2="j" k="75" />
    <hkern u1="E" u2="f" k="125" />
    <hkern u1="E" u2="e" k="75" />
    <hkern u1="E" u2="d" k="50" />
    <hkern u1="E" u2="c" k="50" />
    <hkern u1="E" u2="Z" k="50" />
    <hkern u1="E" u2="X" k="75" />
    <hkern u1="E" u2="S" k="50" />
    <hkern u1="E" u2="Q" k="75" />
    <hkern u1="E" u2="O" k="75" />
    <hkern u1="E" u2="J" k="175" />
    <hkern u1="E" u2="G" k="75" />
    <hkern u1="E" u2="C" k="75" />
    <hkern u1="E" u2="A" k="75" />
    <hkern u1="E" u2="&#x34;" k="175" />
    <hkern u1="E" u2="&#x30;" k="100" />
    <hkern u1="F" u2="&#xff;" k="225" />
    <hkern u1="F" u2="&#xfd;" k="225" />
    <hkern u1="F" u2="&#xfc;" k="125" />
    <hkern u1="F" u2="&#xfb;" k="125" />
    <hkern u1="F" u2="&#xfa;" k="125" />
    <hkern u1="F" u2="&#xf9;" k="125" />
    <hkern u1="F" u2="&#xf8;" k="225" />
    <hkern u1="F" u2="&#xf6;" k="225" />
    <hkern u1="F" u2="&#xf5;" k="225" />
    <hkern u1="F" u2="&#xf4;" k="225" />
    <hkern u1="F" u2="&#xf3;" k="225" />
    <hkern u1="F" u2="&#xf2;" k="225" />
    <hkern u1="F" u2="&#xf1;" k="125" />
    <hkern u1="F" u2="&#xf0;" k="225" />
    <hkern u1="F" u2="&#xef;" k="125" />
    <hkern u1="F" u2="&#xee;" k="125" />
    <hkern u1="F" u2="&#xed;" k="125" />
    <hkern u1="F" u2="&#xec;" k="125" />
    <hkern u1="F" u2="&#xeb;" k="225" />
    <hkern u1="F" u2="&#xea;" k="225" />
    <hkern u1="F" u2="&#xe9;" k="225" />
    <hkern u1="F" u2="&#xe8;" k="225" />
    <hkern u1="F" u2="&#xe7;" k="225" />
    <hkern u1="F" u2="&#xe5;" k="225" />
    <hkern u1="F" u2="&#xe4;" k="225" />
    <hkern u1="F" u2="&#xe3;" k="225" />
    <hkern u1="F" u2="&#xe2;" k="225" />
    <hkern u1="F" u2="&#xe1;" k="225" />
    <hkern u1="F" u2="&#xe0;" k="225" />
    <hkern u1="F" u2="&#x178;" k="75" />
    <hkern u1="F" u2="&#xdd;" k="75" />
    <hkern u1="F" u2="&#xdc;" k="50" />
    <hkern u1="F" u2="&#xdb;" k="50" />
    <hkern u1="F" u2="&#xda;" k="50" />
    <hkern u1="F" u2="&#xd9;" k="50" />
    <hkern u1="F" u2="&#xd8;" k="125" />
    <hkern u1="F" u2="&#xd6;" k="125" />
    <hkern u1="F" u2="&#xd5;" k="125" />
    <hkern u1="F" u2="&#xd4;" k="125" />
    <hkern u1="F" u2="&#xd3;" k="125" />
    <hkern u1="F" u2="&#xd2;" k="125" />
    <hkern u1="F" u2="&#xcf;" k="50" />
    <hkern u1="F" u2="&#xce;" k="50" />
    <hkern u1="F" u2="&#xcd;" k="50" />
    <hkern u1="F" u2="&#xcc;" k="50" />
    <hkern u1="F" u2="&#xc7;" k="125" />
    <hkern u1="F" u2="&#xc5;" k="475" />
    <hkern u1="F" u2="&#xc4;" k="475" />
    <hkern u1="F" u2="&#xc3;" k="475" />
    <hkern u1="F" u2="&#xc2;" k="475" />
    <hkern u1="F" u2="&#xc1;" k="475" />
    <hkern u1="F" u2="&#xc0;" k="475" />
    <hkern u1="F" u2="z" k="225" />
    <hkern u1="F" u2="y" k="225" />
    <hkern u1="F" u2="x" k="225" />
    <hkern u1="F" u2="w" k="225" />
    <hkern u1="F" u2="v" k="225" />
    <hkern u1="F" u2="u" k="125" />
    <hkern u1="F" u2="t" k="125" />
    <hkern u1="F" u2="s" k="225" />
    <hkern u1="F" u2="r" k="125" />
    <hkern u1="F" u2="q" k="225" />
    <hkern u1="F" u2="p" k="125" />
    <hkern u1="F" u2="o" k="225" />
    <hkern u1="F" u2="n" k="125" />
    <hkern u1="F" u2="m" k="125" />
    <hkern u1="F" u2="j" k="225" />
    <hkern u1="F" u2="i" k="125" />
    <hkern u1="F" u2="g" k="225" />
    <hkern u1="F" u2="f" k="227" />
    <hkern u1="F" u2="e" k="225" />
    <hkern u1="F" u2="d" k="225" />
    <hkern u1="F" u2="c" k="225" />
    <hkern u1="F" u2="a" k="225" />
    <hkern u1="F" u2="Z" k="125" />
    <hkern u1="F" u2="Y" k="75" />
    <hkern u1="F" u2="X" k="75" />
    <hkern u1="F" u2="W" k="50" />
    <hkern u1="F" u2="V" k="50" />
    <hkern u1="F" u2="U" k="50" />
    <hkern u1="F" u2="S" k="125" />
    <hkern u1="F" u2="Q" k="125" />
    <hkern u1="F" u2="O" k="125" />
    <hkern u1="F" u2="J" k="500" />
    <hkern u1="F" u2="I" k="50" />
    <hkern u1="F" u2="G" k="125" />
    <hkern u1="F" u2="C" k="125" />
    <hkern u1="F" u2="A" k="475" />
    <hkern u1="F" u2="&#x3c;" k="250" />
    <hkern u1="F" u2="&#x39;" k="125" />
    <hkern u1="F" u2="&#x38;" k="150" />
    <hkern u1="F" u2="&#x37;" k="125" />
    <hkern u1="F" u2="&#x36;" k="125" />
    <hkern u1="F" u2="&#x35;" k="125" />
    <hkern u1="F" u2="&#x34;" k="350" />
    <hkern u1="F" u2="&#x33;" k="125" />
    <hkern u1="F" u2="&#x32;" k="125" />
    <hkern u1="F" u2="&#x30;" k="100" />
    <hkern u1="G" u2="&#xff;" k="75" />
    <hkern u1="G" u2="&#xfd;" k="75" />
    <hkern u1="G" u2="&#xfc;" k="50" />
    <hkern u1="G" u2="&#xfb;" k="50" />
    <hkern u1="G" u2="&#xfa;" k="50" />
    <hkern u1="G" u2="&#xf9;" k="50" />
    <hkern u1="G" u2="&#x178;" k="125" />
    <hkern u1="G" u2="&#xdd;" k="125" />
    <hkern u1="G" u2="&#xc5;" k="50" />
    <hkern u1="G" u2="&#xc4;" k="50" />
    <hkern u1="G" u2="&#xc3;" k="50" />
    <hkern u1="G" u2="&#xc2;" k="50" />
    <hkern u1="G" u2="&#xc1;" k="50" />
    <hkern u1="G" u2="&#xc0;" k="50" />
    <hkern u1="G" u2="y" k="75" />
    <hkern u1="G" u2="w" k="75" />
    <hkern u1="G" u2="v" k="75" />
    <hkern u1="G" u2="u" k="50" />
    <hkern u1="G" u2="t" k="50" />
    <hkern u1="G" u2="j" k="75" />
    <hkern u1="G" u2="f" k="125" />
    <hkern u1="G" u2="Y" k="125" />
    <hkern u1="G" u2="X" k="50" />
    <hkern u1="G" u2="W" k="75" />
    <hkern u1="G" u2="V" k="75" />
    <hkern u1="G" u2="T" k="50" />
    <hkern u1="G" u2="J" k="75" />
    <hkern u1="G" u2="A" k="50" />
    <hkern u1="H" u2="&#xff;" k="75" />
    <hkern u1="H" u2="&#xfd;" k="75" />
    <hkern u1="H" u2="&#xfc;" k="50" />
    <hkern u1="H" u2="&#xfb;" k="50" />
    <hkern u1="H" u2="&#xfa;" k="50" />
    <hkern u1="H" u2="&#xf9;" k="50" />
    <hkern u1="H" u2="&#xc5;" k="50" />
    <hkern u1="H" u2="&#xc4;" k="50" />
    <hkern u1="H" u2="&#xc3;" k="50" />
    <hkern u1="H" u2="&#xc2;" k="50" />
    <hkern u1="H" u2="&#xc1;" k="50" />
    <hkern u1="H" u2="&#xc0;" k="50" />
    <hkern u1="H" u2="y" k="75" />
    <hkern u1="H" u2="x" k="50" />
    <hkern u1="H" u2="w" k="75" />
    <hkern u1="H" u2="v" k="75" />
    <hkern u1="H" u2="u" k="50" />
    <hkern u1="H" u2="t" k="50" />
    <hkern u1="H" u2="j" k="75" />
    <hkern u1="H" u2="f" k="75" />
    <hkern u1="H" u2="Z" k="50" />
    <hkern u1="H" u2="J" k="75" />
    <hkern u1="H" u2="A" k="50" />
    <hkern u1="I" u2="&#xff;" k="75" />
    <hkern u1="I" u2="&#xfd;" k="75" />
    <hkern u1="I" u2="&#xfc;" k="50" />
    <hkern u1="I" u2="&#xfb;" k="50" />
    <hkern u1="I" u2="&#xfa;" k="50" />
    <hkern u1="I" u2="&#xf9;" k="50" />
    <hkern u1="I" u2="&#xc5;" k="50" />
    <hkern u1="I" u2="&#xc4;" k="50" />
    <hkern u1="I" u2="&#xc3;" k="50" />
    <hkern u1="I" u2="&#xc2;" k="50" />
    <hkern u1="I" u2="&#xc1;" k="50" />
    <hkern u1="I" u2="&#xc0;" k="50" />
    <hkern u1="I" u2="y" k="75" />
    <hkern u1="I" u2="w" k="75" />
    <hkern u1="I" u2="v" k="75" />
    <hkern u1="I" u2="u" k="50" />
    <hkern u1="I" u2="t" k="50" />
    <hkern u1="I" u2="f" k="75" />
    <hkern u1="I" u2="T" k="50" />
    <hkern u1="I" u2="J" k="75" />
    <hkern u1="I" u2="A" k="50" />
    <hkern u1="J" u2="&#xff;" k="55" />
    <hkern u1="J" u2="&#xfd;" k="55" />
    <hkern u1="J" u2="&#xe5;" k="50" />
    <hkern u1="J" u2="&#xe4;" k="50" />
    <hkern u1="J" u2="&#xe3;" k="50" />
    <hkern u1="J" u2="&#xe2;" k="50" />
    <hkern u1="J" u2="&#xe1;" k="50" />
    <hkern u1="J" u2="&#xe0;" k="50" />
    <hkern u1="J" u2="&#xc5;" k="225" />
    <hkern u1="J" u2="&#xc4;" k="225" />
    <hkern u1="J" u2="&#xc3;" k="225" />
    <hkern u1="J" u2="&#xc2;" k="225" />
    <hkern u1="J" u2="&#xc1;" k="225" />
    <hkern u1="J" u2="&#xc0;" k="225" />
    <hkern u1="J" u2="z" k="75" />
    <hkern u1="J" u2="y" k="55" />
    <hkern u1="J" u2="x" k="55" />
    <hkern u1="J" u2="w" k="55" />
    <hkern u1="J" u2="v" k="55" />
    <hkern u1="J" u2="t" k="50" />
    <hkern u1="J" u2="j" k="75" />
    <hkern u1="J" u2="a" k="50" />
    <hkern u1="J" u2="S" k="50" />
    <hkern u1="J" u2="J" k="125" />
    <hkern u1="J" u2="A" k="225" />
    <hkern u1="K" u2="&#xff;" k="375" />
    <hkern u1="K" u2="&#xfd;" k="375" />
    <hkern u1="K" u2="&#xfc;" k="150" />
    <hkern u1="K" u2="&#xfb;" k="150" />
    <hkern u1="K" u2="&#xfa;" k="150" />
    <hkern u1="K" u2="&#xf9;" k="150" />
    <hkern u1="K" u2="&#xf8;" k="150" />
    <hkern u1="K" u2="&#xf6;" k="150" />
    <hkern u1="K" u2="&#xf5;" k="150" />
    <hkern u1="K" u2="&#xf4;" k="150" />
    <hkern u1="K" u2="&#xf3;" k="150" />
    <hkern u1="K" u2="&#xf2;" k="150" />
    <hkern u1="K" u2="&#xf0;" k="150" />
    <hkern u1="K" u2="&#xeb;" k="150" />
    <hkern u1="K" u2="&#xea;" k="150" />
    <hkern u1="K" u2="&#xe9;" k="150" />
    <hkern u1="K" u2="&#xe8;" k="150" />
    <hkern u1="K" u2="&#xe7;" k="150" />
    <hkern u1="K" u2="&#xe5;" k="100" />
    <hkern u1="K" u2="&#xe4;" k="100" />
    <hkern u1="K" u2="&#xe3;" k="100" />
    <hkern u1="K" u2="&#xe2;" k="100" />
    <hkern u1="K" u2="&#xe1;" k="100" />
    <hkern u1="K" u2="&#xe0;" k="100" />
    <hkern u1="K" u2="&#x178;" k="350" />
    <hkern u1="K" u2="&#xdd;" k="350" />
    <hkern u1="K" u2="&#xdc;" k="225" />
    <hkern u1="K" u2="&#xdb;" k="225" />
    <hkern u1="K" u2="&#xda;" k="225" />
    <hkern u1="K" u2="&#xd9;" k="225" />
    <hkern u1="K" u2="&#xd8;" k="300" />
    <hkern u1="K" u2="&#xd6;" k="300" />
    <hkern u1="K" u2="&#xd5;" k="300" />
    <hkern u1="K" u2="&#xd4;" k="300" />
    <hkern u1="K" u2="&#xd3;" k="300" />
    <hkern u1="K" u2="&#xd2;" k="300" />
    <hkern u1="K" u2="&#xd1;" k="50" />
    <hkern u1="K" u2="&#xd0;" k="50" />
    <hkern u1="K" u2="&#xcf;" k="50" />
    <hkern u1="K" u2="&#xce;" k="50" />
    <hkern u1="K" u2="&#xcd;" k="50" />
    <hkern u1="K" u2="&#xcc;" k="50" />
    <hkern u1="K" u2="&#xcb;" k="50" />
    <hkern u1="K" u2="&#xca;" k="50" />
    <hkern u1="K" u2="&#xc9;" k="50" />
    <hkern u1="K" u2="&#xc8;" k="50" />
    <hkern u1="K" u2="&#xc7;" k="300" />
    <hkern u1="K" u2="&#xc5;" k="75" />
    <hkern u1="K" u2="&#xc4;" k="75" />
    <hkern u1="K" u2="&#xc3;" k="75" />
    <hkern u1="K" u2="&#xc2;" k="75" />
    <hkern u1="K" u2="&#xc1;" k="75" />
    <hkern u1="K" u2="&#xc0;" k="75" />
    <hkern u1="K" u2="z" k="75" />
    <hkern u1="K" u2="y" k="375" />
    <hkern u1="K" u2="x" k="75" />
    <hkern u1="K" u2="w" k="375" />
    <hkern u1="K" u2="v" k="375" />
    <hkern u1="K" u2="u" k="150" />
    <hkern u1="K" u2="t" k="125" />
    <hkern u1="K" u2="q" k="150" />
    <hkern u1="K" u2="o" k="150" />
    <hkern u1="K" u2="j" k="75" />
    <hkern u1="K" u2="e" k="150" />
    <hkern u1="K" u2="d" k="150" />
    <hkern u1="K" u2="c" k="150" />
    <hkern u1="K" u2="a" k="100" />
    <hkern u1="K" u2="^" k="200" />
    <hkern u1="K" u2="Z" k="75" />
    <hkern u1="K" u2="Y" k="350" />
    <hkern u1="K" u2="X" k="75" />
    <hkern u1="K" u2="W" k="325" />
    <hkern u1="K" u2="V" k="325" />
    <hkern u1="K" u2="U" k="225" />
    <hkern u1="K" u2="T" k="325" />
    <hkern u1="K" u2="S" k="125" />
    <hkern u1="K" u2="R" k="50" />
    <hkern u1="K" u2="Q" k="300" />
    <hkern u1="K" u2="P" k="50" />
    <hkern u1="K" u2="O" k="300" />
    <hkern u1="K" u2="N" k="50" />
    <hkern u1="K" u2="M" k="50" />
    <hkern u1="K" u2="L" k="50" />
    <hkern u1="K" u2="K" k="50" />
    <hkern u1="K" u2="J" k="300" />
    <hkern u1="K" u2="I" k="50" />
    <hkern u1="K" u2="H" k="50" />
    <hkern u1="K" u2="G" k="300" />
    <hkern u1="K" u2="F" k="50" />
    <hkern u1="K" u2="E" k="50" />
    <hkern u1="K" u2="D" k="50" />
    <hkern u1="K" u2="C" k="300" />
    <hkern u1="K" u2="B" k="50" />
    <hkern u1="K" u2="A" k="75" />
    <hkern u1="K" u2="&#x3c;" k="225" />
    <hkern u1="K" u2="&#x39;" k="400" />
    <hkern u1="K" u2="&#x38;" k="250" />
    <hkern u1="K" u2="&#x36;" k="250" />
    <hkern u1="K" u2="&#x35;" k="250" />
    <hkern u1="K" u2="&#x34;" k="400" />
    <hkern u1="K" u2="&#x33;" k="250" />
    <hkern u1="K" u2="&#x32;" k="75" />
    <hkern u1="K" u2="&#x31;" k="150" />
    <hkern u1="K" u2="&#x30;" k="200" />
    <hkern u1="L" u2="&#xff;" k="275" />
    <hkern u1="L" u2="&#xfd;" k="275" />
    <hkern u1="L" u2="&#xfc;" k="50" />
    <hkern u1="L" u2="&#xfb;" k="50" />
    <hkern u1="L" u2="&#xfa;" k="50" />
    <hkern u1="L" u2="&#xf9;" k="50" />
    <hkern u1="L" u2="&#xf8;" k="50" />
    <hkern u1="L" u2="&#xf6;" k="50" />
    <hkern u1="L" u2="&#xf5;" k="50" />
    <hkern u1="L" u2="&#xf4;" k="50" />
    <hkern u1="L" u2="&#xf3;" k="50" />
    <hkern u1="L" u2="&#xf2;" k="50" />
    <hkern u1="L" u2="&#xf0;" k="50" />
    <hkern u1="L" u2="&#xeb;" k="50" />
    <hkern u1="L" u2="&#xea;" k="50" />
    <hkern u1="L" u2="&#xe9;" k="50" />
    <hkern u1="L" u2="&#xe8;" k="50" />
    <hkern u1="L" u2="&#xe7;" k="50" />
    <hkern u1="L" u2="&#x178;" k="400" />
    <hkern u1="L" u2="&#xdd;" k="400" />
    <hkern u1="L" u2="&#xdc;" k="75" />
    <hkern u1="L" u2="&#xdb;" k="75" />
    <hkern u1="L" u2="&#xda;" k="75" />
    <hkern u1="L" u2="&#xd9;" k="75" />
    <hkern u1="L" u2="&#xd8;" k="150" />
    <hkern u1="L" u2="&#xd6;" k="150" />
    <hkern u1="L" u2="&#xd5;" k="150" />
    <hkern u1="L" u2="&#xd4;" k="150" />
    <hkern u1="L" u2="&#xd3;" k="150" />
    <hkern u1="L" u2="&#xd2;" k="150" />
    <hkern u1="L" u2="&#xc7;" k="150" />
    <hkern u1="L" u2="&#xc5;" k="75" />
    <hkern u1="L" u2="&#xc4;" k="75" />
    <hkern u1="L" u2="&#xc3;" k="75" />
    <hkern u1="L" u2="&#xc2;" k="75" />
    <hkern u1="L" u2="&#xc1;" k="75" />
    <hkern u1="L" u2="&#xc0;" k="75" />
    <hkern u1="L" u2="z" k="75" />
    <hkern u1="L" u2="y" k="275" />
    <hkern u1="L" u2="x" k="125" />
    <hkern u1="L" u2="w" k="275" />
    <hkern u1="L" u2="v" k="275" />
    <hkern u1="L" u2="u" k="50" />
    <hkern u1="L" u2="t" k="50" />
    <hkern u1="L" u2="q" k="50" />
    <hkern u1="L" u2="o" k="50" />
    <hkern u1="L" u2="j" k="50" />
    <hkern u1="L" u2="f" k="75" />
    <hkern u1="L" u2="e" k="50" />
    <hkern u1="L" u2="d" k="50" />
    <hkern u1="L" u2="c" k="50" />
    <hkern u1="L" u2="\" k="600" />
    <hkern u1="L" u2="Z" k="50" />
    <hkern u1="L" u2="Y" k="400" />
    <hkern u1="L" u2="X" k="75" />
    <hkern u1="L" u2="W" k="375" />
    <hkern u1="L" u2="V" k="375" />
    <hkern u1="L" u2="U" k="75" />
    <hkern u1="L" u2="T" k="375" />
    <hkern u1="L" u2="Q" k="150" />
    <hkern u1="L" u2="O" k="150" />
    <hkern u1="L" u2="M" k="50" />
    <hkern u1="L" u2="J" k="125" />
    <hkern u1="L" u2="H" k="50" />
    <hkern u1="L" u2="G" k="150" />
    <hkern u1="L" u2="C" k="150" />
    <hkern u1="L" u2="B" k="50" />
    <hkern u1="L" u2="A" k="75" />
    <hkern u1="L" u2="&#x39;" k="325" />
    <hkern u1="L" u2="&#x34;" k="200" />
    <hkern u1="L" u2="&#x30;" k="125" />
    <hkern u1="M" u2="&#xff;" k="50" />
    <hkern u1="M" u2="&#xfd;" k="50" />
    <hkern u1="M" u2="&#xfc;" k="50" />
    <hkern u1="M" u2="&#xfb;" k="50" />
    <hkern u1="M" u2="&#xfa;" k="50" />
    <hkern u1="M" u2="&#xf9;" k="50" />
    <hkern u1="M" u2="&#xc5;" k="50" />
    <hkern u1="M" u2="&#xc4;" k="50" />
    <hkern u1="M" u2="&#xc3;" k="50" />
    <hkern u1="M" u2="&#xc2;" k="50" />
    <hkern u1="M" u2="&#xc1;" k="50" />
    <hkern u1="M" u2="&#xc0;" k="50" />
    <hkern u1="M" u2="y" k="50" />
    <hkern u1="M" u2="x" k="75" />
    <hkern u1="M" u2="w" k="75" />
    <hkern u1="M" u2="v" k="75" />
    <hkern u1="M" u2="u" k="50" />
    <hkern u1="M" u2="t" k="50" />
    <hkern u1="M" u2="j" k="75" />
    <hkern u1="M" u2="f" k="75" />
    <hkern u1="M" u2="J" k="75" />
    <hkern u1="M" u2="A" k="50" />
    <hkern u1="N" u2="&#xff;" k="75" />
    <hkern u1="N" u2="&#xfd;" k="75" />
    <hkern u1="N" u2="&#xfc;" k="50" />
    <hkern u1="N" u2="&#xfb;" k="50" />
    <hkern u1="N" u2="&#xfa;" k="50" />
    <hkern u1="N" u2="&#xf9;" k="50" />
    <hkern u1="N" u2="&#xc5;" k="50" />
    <hkern u1="N" u2="&#xc4;" k="50" />
    <hkern u1="N" u2="&#xc3;" k="50" />
    <hkern u1="N" u2="&#xc2;" k="50" />
    <hkern u1="N" u2="&#xc1;" k="50" />
    <hkern u1="N" u2="&#xc0;" k="50" />
    <hkern u1="N" u2="y" k="75" />
    <hkern u1="N" u2="x" k="75" />
    <hkern u1="N" u2="w" k="75" />
    <hkern u1="N" u2="v" k="75" />
    <hkern u1="N" u2="u" k="50" />
    <hkern u1="N" u2="t" k="50" />
    <hkern u1="N" u2="j" k="75" />
    <hkern u1="N" u2="f" k="75" />
    <hkern u1="N" u2="J" k="75" />
    <hkern u1="N" u2="A" k="50" />
    <hkern u1="O" u2="&#xff;" k="50" />
    <hkern u1="O" u2="&#xfd;" k="50" />
    <hkern u1="O" u2="&#xf8;" k="-50" />
    <hkern u1="O" u2="&#xf6;" k="-50" />
    <hkern u1="O" u2="&#xf5;" k="-50" />
    <hkern u1="O" u2="&#xf4;" k="-50" />
    <hkern u1="O" u2="&#xf3;" k="-50" />
    <hkern u1="O" u2="&#xf2;" k="-50" />
    <hkern u1="O" u2="&#xf0;" k="-50" />
    <hkern u1="O" u2="&#xeb;" k="-50" />
    <hkern u1="O" u2="&#xea;" k="-50" />
    <hkern u1="O" u2="&#xe9;" k="-50" />
    <hkern u1="O" u2="&#xe8;" k="-50" />
    <hkern u1="O" u2="&#xe7;" k="-50" />
    <hkern u1="O" u2="&#x178;" k="200" />
    <hkern u1="O" u2="&#xdd;" k="200" />
    <hkern u1="O" u2="&#xd8;" k="-50" />
    <hkern u1="O" u2="&#xd6;" k="-50" />
    <hkern u1="O" u2="&#xd5;" k="-50" />
    <hkern u1="O" u2="&#xd4;" k="-50" />
    <hkern u1="O" u2="&#xd3;" k="-50" />
    <hkern u1="O" u2="&#xd2;" k="-50" />
    <hkern u1="O" u2="&#xc7;" k="-50" />
    <hkern u1="O" u2="&#xc5;" k="225" />
    <hkern u1="O" u2="&#xc4;" k="225" />
    <hkern u1="O" u2="&#xc3;" k="225" />
    <hkern u1="O" u2="&#xc2;" k="225" />
    <hkern u1="O" u2="&#xc1;" k="225" />
    <hkern u1="O" u2="&#xc0;" k="225" />
    <hkern u1="O" u2="z" k="75" />
    <hkern u1="O" u2="y" k="50" />
    <hkern u1="O" u2="x" k="50" />
    <hkern u1="O" u2="w" k="50" />
    <hkern u1="O" u2="v" k="50" />
    <hkern u1="O" u2="s" k="-50" />
    <hkern u1="O" u2="q" k="-50" />
    <hkern u1="O" u2="o" k="-50" />
    <hkern u1="O" u2="j" k="50" />
    <hkern u1="O" u2="f" k="50" />
    <hkern u1="O" u2="e" k="-50" />
    <hkern u1="O" u2="d" k="-50" />
    <hkern u1="O" u2="c" k="-50" />
    <hkern u1="O" u2="\" k="250" />
    <hkern u1="O" u2="Z" k="75" />
    <hkern u1="O" u2="Y" k="200" />
    <hkern u1="O" u2="X" k="125" />
    <hkern u1="O" u2="W" k="100" />
    <hkern u1="O" u2="V" k="125" />
    <hkern u1="O" u2="T" k="100" />
    <hkern u1="O" u2="S" k="50" />
    <hkern u1="O" u2="Q" k="-50" />
    <hkern u1="O" u2="O" k="-50" />
    <hkern u1="O" u2="J" k="125" />
    <hkern u1="O" u2="G" k="-50" />
    <hkern u1="O" u2="C" k="-50" />
    <hkern u1="O" u2="A" k="225" />
    <hkern u1="O" u2="&#x37;" k="150" />
    <hkern u1="O" u2="&#x32;" k="125" />
    <hkern u1="O" u2="&#x2f;" k="150" />
    <hkern u1="P" u2="&#xff;" k="50" />
    <hkern u1="P" u2="&#xfd;" k="50" />
    <hkern u1="P" u2="&#xfc;" k="50" />
    <hkern u1="P" u2="&#xfb;" k="50" />
    <hkern u1="P" u2="&#xfa;" k="50" />
    <hkern u1="P" u2="&#xf9;" k="50" />
    <hkern u1="P" u2="&#xf8;" k="75" />
    <hkern u1="P" u2="&#xf6;" k="75" />
    <hkern u1="P" u2="&#xf5;" k="75" />
    <hkern u1="P" u2="&#xf4;" k="75" />
    <hkern u1="P" u2="&#xf3;" k="75" />
    <hkern u1="P" u2="&#xf2;" k="75" />
    <hkern u1="P" u2="&#xf0;" k="75" />
    <hkern u1="P" u2="&#xe7;" k="75" />
    <hkern u1="P" u2="&#xe5;" k="75" />
    <hkern u1="P" u2="&#xe4;" k="75" />
    <hkern u1="P" u2="&#xe3;" k="75" />
    <hkern u1="P" u2="&#xe2;" k="75" />
    <hkern u1="P" u2="&#xe1;" k="75" />
    <hkern u1="P" u2="&#xe0;" k="75" />
    <hkern u1="P" u2="&#x178;" k="175" />
    <hkern u1="P" u2="&#xdd;" k="175" />
    <hkern u1="P" u2="&#xd8;" k="-25" />
    <hkern u1="P" u2="&#xd6;" k="-25" />
    <hkern u1="P" u2="&#xd5;" k="-25" />
    <hkern u1="P" u2="&#xd4;" k="-25" />
    <hkern u1="P" u2="&#xd3;" k="-25" />
    <hkern u1="P" u2="&#xd2;" k="-25" />
    <hkern u1="P" u2="&#xc7;" k="-25" />
    <hkern u1="P" u2="&#xc5;" k="375" />
    <hkern u1="P" u2="&#xc4;" k="375" />
    <hkern u1="P" u2="&#xc3;" k="375" />
    <hkern u1="P" u2="&#xc2;" k="375" />
    <hkern u1="P" u2="&#xc1;" k="375" />
    <hkern u1="P" u2="&#xc0;" k="375" />
    <hkern u1="P" u2="z" k="75" />
    <hkern u1="P" u2="y" k="50" />
    <hkern u1="P" u2="x" k="50" />
    <hkern u1="P" u2="w" k="50" />
    <hkern u1="P" u2="v" k="50" />
    <hkern u1="P" u2="u" k="50" />
    <hkern u1="P" u2="q" k="75" />
    <hkern u1="P" u2="o" k="75" />
    <hkern u1="P" u2="j" k="50" />
    <hkern u1="P" u2="g" k="50" />
    <hkern u1="P" u2="f" k="50" />
    <hkern u1="P" u2="d" k="75" />
    <hkern u1="P" u2="c" k="75" />
    <hkern u1="P" u2="a" k="75" />
    <hkern u1="P" u2="Z" k="125" />
    <hkern u1="P" u2="Y" k="175" />
    <hkern u1="P" u2="X" k="150" />
    <hkern u1="P" u2="W" k="125" />
    <hkern u1="P" u2="V" k="150" />
    <hkern u1="P" u2="T" k="50" />
    <hkern u1="P" u2="S" k="50" />
    <hkern u1="P" u2="Q" k="-25" />
    <hkern u1="P" u2="O" k="-25" />
    <hkern u1="P" u2="J" k="350" />
    <hkern u1="P" u2="G" k="-25" />
    <hkern u1="P" u2="C" k="-25" />
    <hkern u1="P" u2="A" k="375" />
    <hkern u1="P" u2="&#x37;" k="150" />
    <hkern u1="P" u2="&#x35;" k="100" />
    <hkern u1="P" u2="&#x34;" k="200" />
    <hkern u1="P" u2="&#x33;" k="100" />
    <hkern u1="P" u2="&#x32;" k="100" />
    <hkern u1="P" u2="&#x2f;" k="450" />
    <hkern u1="Q" u2="&#xff;" k="100" />
    <hkern u1="Q" u2="&#xfd;" k="100" />
    <hkern u1="Q" u2="&#xfc;" k="75" />
    <hkern u1="Q" u2="&#xfb;" k="75" />
    <hkern u1="Q" u2="&#xfa;" k="75" />
    <hkern u1="Q" u2="&#xf9;" k="75" />
    <hkern u1="Q" u2="&#xf8;" k="50" />
    <hkern u1="Q" u2="&#xf6;" k="50" />
    <hkern u1="Q" u2="&#xf5;" k="50" />
    <hkern u1="Q" u2="&#xf4;" k="50" />
    <hkern u1="Q" u2="&#xf3;" k="50" />
    <hkern u1="Q" u2="&#xf2;" k="50" />
    <hkern u1="Q" u2="&#xf1;" k="50" />
    <hkern u1="Q" u2="&#xf0;" k="50" />
    <hkern u1="Q" u2="&#xef;" k="50" />
    <hkern u1="Q" u2="&#xee;" k="50" />
    <hkern u1="Q" u2="&#xed;" k="50" />
    <hkern u1="Q" u2="&#xec;" k="50" />
    <hkern u1="Q" u2="&#xeb;" k="50" />
    <hkern u1="Q" u2="&#xea;" k="50" />
    <hkern u1="Q" u2="&#xe9;" k="50" />
    <hkern u1="Q" u2="&#xe8;" k="50" />
    <hkern u1="Q" u2="&#xe7;" k="50" />
    <hkern u1="Q" u2="&#xe5;" k="125" />
    <hkern u1="Q" u2="&#xe4;" k="125" />
    <hkern u1="Q" u2="&#xe3;" k="125" />
    <hkern u1="Q" u2="&#xe2;" k="125" />
    <hkern u1="Q" u2="&#xe1;" k="125" />
    <hkern u1="Q" u2="&#xe0;" k="125" />
    <hkern u1="Q" u2="&#x178;" k="350" />
    <hkern u1="Q" u2="&#xdd;" k="350" />
    <hkern u1="Q" u2="&#xdc;" k="75" />
    <hkern u1="Q" u2="&#xdb;" k="75" />
    <hkern u1="Q" u2="&#xda;" k="75" />
    <hkern u1="Q" u2="&#xd9;" k="75" />
    <hkern u1="Q" u2="&#xd1;" k="75" />
    <hkern u1="Q" u2="&#xd0;" k="75" />
    <hkern u1="Q" u2="&#xcf;" k="75" />
    <hkern u1="Q" u2="&#xce;" k="75" />
    <hkern u1="Q" u2="&#xcd;" k="75" />
    <hkern u1="Q" u2="&#xcc;" k="75" />
    <hkern u1="Q" u2="&#xcb;" k="75" />
    <hkern u1="Q" u2="&#xca;" k="75" />
    <hkern u1="Q" u2="&#xc9;" k="75" />
    <hkern u1="Q" u2="&#xc8;" k="75" />
    <hkern u1="Q" u2="&#xc7;" k="50" />
    <hkern u1="Q" u2="&#xc5;" k="325" />
    <hkern u1="Q" u2="&#xc4;" k="325" />
    <hkern u1="Q" u2="&#xc3;" k="325" />
    <hkern u1="Q" u2="&#xc2;" k="325" />
    <hkern u1="Q" u2="&#xc1;" k="325" />
    <hkern u1="Q" u2="&#xc0;" k="325" />
    <hkern u1="Q" u2="z" k="100" />
    <hkern u1="Q" u2="y" k="100" />
    <hkern u1="Q" u2="x" k="100" />
    <hkern u1="Q" u2="w" k="100" />
    <hkern u1="Q" u2="v" k="100" />
    <hkern u1="Q" u2="u" k="75" />
    <hkern u1="Q" u2="t" k="75" />
    <hkern u1="Q" u2="s" k="50" />
    <hkern u1="Q" u2="r" k="50" />
    <hkern u1="Q" u2="q" k="50" />
    <hkern u1="Q" u2="p" k="50" />
    <hkern u1="Q" u2="o" k="50" />
    <hkern u1="Q" u2="n" k="50" />
    <hkern u1="Q" u2="m" k="50" />
    <hkern u1="Q" u2="l" k="50" />
    <hkern u1="Q" u2="k" k="50" />
    <hkern u1="Q" u2="j" k="125" />
    <hkern u1="Q" u2="i" k="50" />
    <hkern u1="Q" u2="h" k="50" />
    <hkern u1="Q" u2="f" k="75" />
    <hkern u1="Q" u2="e" k="50" />
    <hkern u1="Q" u2="d" k="50" />
    <hkern u1="Q" u2="c" k="50" />
    <hkern u1="Q" u2="b" k="50" />
    <hkern u1="Q" u2="a" k="125" />
    <hkern u1="Q" u2="\" k="250" />
    <hkern u1="Q" u2="Z" k="150" />
    <hkern u1="Q" u2="Y" k="350" />
    <hkern u1="Q" u2="X" k="250" />
    <hkern u1="Q" u2="W" k="225" />
    <hkern u1="Q" u2="V" k="250" />
    <hkern u1="Q" u2="U" k="75" />
    <hkern u1="Q" u2="T" k="175" />
    <hkern u1="Q" u2="S" k="125" />
    <hkern u1="Q" u2="R" k="75" />
    <hkern u1="Q" u2="P" k="75" />
    <hkern u1="Q" u2="N" k="75" />
    <hkern u1="Q" u2="M" k="75" />
    <hkern u1="Q" u2="L" k="75" />
    <hkern u1="Q" u2="K" k="75" />
    <hkern u1="Q" u2="J" k="325" />
    <hkern u1="Q" u2="I" k="75" />
    <hkern u1="Q" u2="H" k="75" />
    <hkern u1="Q" u2="G" k="50" />
    <hkern u1="Q" u2="F" k="75" />
    <hkern u1="Q" u2="E" k="75" />
    <hkern u1="Q" u2="D" k="75" />
    <hkern u1="Q" u2="C" k="50" />
    <hkern u1="Q" u2="B" k="75" />
    <hkern u1="Q" u2="A" k="325" />
    <hkern u1="Q" u2="&#x39;" k="100" />
    <hkern u1="Q" u2="&#x38;" k="150" />
    <hkern u1="Q" u2="&#x37;" k="275" />
    <hkern u1="Q" u2="&#x36;" k="75" />
    <hkern u1="Q" u2="&#x35;" k="150" />
    <hkern u1="Q" u2="&#x34;" k="150" />
    <hkern u1="Q" u2="&#x33;" k="150" />
    <hkern u1="Q" u2="&#x32;" k="200" />
    <hkern u1="Q" u2="&#x31;" k="150" />
    <hkern u1="R" u2="&#xff;" k="100" />
    <hkern u1="R" u2="&#xfd;" k="100" />
    <hkern u1="R" u2="&#xfc;" k="75" />
    <hkern u1="R" u2="&#xfb;" k="75" />
    <hkern u1="R" u2="&#xfa;" k="75" />
    <hkern u1="R" u2="&#xf9;" k="75" />
    <hkern u1="R" u2="&#xf8;" k="125" />
    <hkern u1="R" u2="&#xf6;" k="125" />
    <hkern u1="R" u2="&#xf5;" k="125" />
    <hkern u1="R" u2="&#xf4;" k="125" />
    <hkern u1="R" u2="&#xf3;" k="125" />
    <hkern u1="R" u2="&#xf2;" k="125" />
    <hkern u1="R" u2="&#xf0;" k="125" />
    <hkern u1="R" u2="&#xeb;" k="125" />
    <hkern u1="R" u2="&#xea;" k="125" />
    <hkern u1="R" u2="&#xe9;" k="125" />
    <hkern u1="R" u2="&#xe8;" k="125" />
    <hkern u1="R" u2="&#xe7;" k="125" />
    <hkern u1="R" u2="&#xe5;" k="75" />
    <hkern u1="R" u2="&#xe4;" k="75" />
    <hkern u1="R" u2="&#xe3;" k="75" />
    <hkern u1="R" u2="&#xe2;" k="75" />
    <hkern u1="R" u2="&#xe1;" k="75" />
    <hkern u1="R" u2="&#xe0;" k="75" />
    <hkern u1="R" u2="&#x178;" k="250" />
    <hkern u1="R" u2="&#xdd;" k="250" />
    <hkern u1="R" u2="&#xdc;" k="25" />
    <hkern u1="R" u2="&#xdb;" k="25" />
    <hkern u1="R" u2="&#xda;" k="25" />
    <hkern u1="R" u2="&#xd9;" k="25" />
    <hkern u1="R" u2="&#xc7;" k="50" />
    <hkern u1="R" u2="&#xc5;" k="75" />
    <hkern u1="R" u2="&#xc4;" k="75" />
    <hkern u1="R" u2="&#xc3;" k="75" />
    <hkern u1="R" u2="&#xc2;" k="75" />
    <hkern u1="R" u2="&#xc1;" k="75" />
    <hkern u1="R" u2="&#xc0;" k="75" />
    <hkern u1="R" u2="z" k="50" />
    <hkern u1="R" u2="y" k="100" />
    <hkern u1="R" u2="x" k="75" />
    <hkern u1="R" u2="w" k="100" />
    <hkern u1="R" u2="v" k="100" />
    <hkern u1="R" u2="u" k="75" />
    <hkern u1="R" u2="t" k="75" />
    <hkern u1="R" u2="s" k="75" />
    <hkern u1="R" u2="q" k="125" />
    <hkern u1="R" u2="o" k="125" />
    <hkern u1="R" u2="j" k="75" />
    <hkern u1="R" u2="g" k="75" />
    <hkern u1="R" u2="f" k="75" />
    <hkern u1="R" u2="e" k="125" />
    <hkern u1="R" u2="d" k="125" />
    <hkern u1="R" u2="c" k="125" />
    <hkern u1="R" u2="a" k="75" />
    <hkern u1="R" u2="\" k="150" />
    <hkern u1="R" u2="Z" k="50" />
    <hkern u1="R" u2="Y" k="250" />
    <hkern u1="R" u2="X" k="75" />
    <hkern u1="R" u2="W" k="175" />
    <hkern u1="R" u2="V" k="200" />
    <hkern u1="R" u2="U" k="25" />
    <hkern u1="R" u2="T" k="75" />
    <hkern u1="R" u2="S" k="50" />
    <hkern u1="R" u2="J" k="225" />
    <hkern u1="R" u2="G" k="50" />
    <hkern u1="R" u2="C" k="50" />
    <hkern u1="R" u2="A" k="75" />
    <hkern u1="R" u2="&#x39;" k="50" />
    <hkern u1="R" u2="&#x38;" k="150" />
    <hkern u1="R" u2="&#x37;" k="50" />
    <hkern u1="R" u2="&#x36;" k="100" />
    <hkern u1="R" u2="&#x35;" k="150" />
    <hkern u1="R" u2="&#x34;" k="225" />
    <hkern u1="R" u2="&#x33;" k="150" />
    <hkern u1="R" u2="&#x32;" k="100" />
    <hkern u1="S" u2="&#xff;" k="125" />
    <hkern u1="S" u2="&#xfd;" k="125" />
    <hkern u1="S" u2="&#xf8;" k="-50" />
    <hkern u1="S" u2="&#xf6;" k="-50" />
    <hkern u1="S" u2="&#xf5;" k="-50" />
    <hkern u1="S" u2="&#xf4;" k="-50" />
    <hkern u1="S" u2="&#xf3;" k="-50" />
    <hkern u1="S" u2="&#xf2;" k="-50" />
    <hkern u1="S" u2="&#xf0;" k="-50" />
    <hkern u1="S" u2="&#xeb;" k="-50" />
    <hkern u1="S" u2="&#xea;" k="-50" />
    <hkern u1="S" u2="&#xe9;" k="-50" />
    <hkern u1="S" u2="&#xe8;" k="-50" />
    <hkern u1="S" u2="&#xe7;" k="-50" />
    <hkern u1="S" u2="&#xe5;" k="-25" />
    <hkern u1="S" u2="&#xe4;" k="-25" />
    <hkern u1="S" u2="&#xe3;" k="-25" />
    <hkern u1="S" u2="&#xe2;" k="-25" />
    <hkern u1="S" u2="&#xe1;" k="-25" />
    <hkern u1="S" u2="&#xe0;" k="-25" />
    <hkern u1="S" u2="&#x178;" k="50" />
    <hkern u1="S" u2="&#xdd;" k="50" />
    <hkern u1="S" u2="&#xd8;" k="-25" />
    <hkern u1="S" u2="&#xd6;" k="-25" />
    <hkern u1="S" u2="&#xd5;" k="-25" />
    <hkern u1="S" u2="&#xd4;" k="-25" />
    <hkern u1="S" u2="&#xd3;" k="-25" />
    <hkern u1="S" u2="&#xd2;" k="-25" />
    <hkern u1="S" u2="&#xc5;" k="125" />
    <hkern u1="S" u2="&#xc4;" k="125" />
    <hkern u1="S" u2="&#xc3;" k="125" />
    <hkern u1="S" u2="&#xc2;" k="125" />
    <hkern u1="S" u2="&#xc1;" k="125" />
    <hkern u1="S" u2="&#xc0;" k="125" />
    <hkern u1="S" u2="z" k="75" />
    <hkern u1="S" u2="y" k="125" />
    <hkern u1="S" u2="x" k="125" />
    <hkern u1="S" u2="w" k="125" />
    <hkern u1="S" u2="v" k="125" />
    <hkern u1="S" u2="q" k="-50" />
    <hkern u1="S" u2="o" k="-50" />
    <hkern u1="S" u2="j" k="50" />
    <hkern u1="S" u2="f" k="50" />
    <hkern u1="S" u2="e" k="-50" />
    <hkern u1="S" u2="d" k="-50" />
    <hkern u1="S" u2="c" k="-50" />
    <hkern u1="S" u2="a" k="-25" />
    <hkern u1="S" u2="Z" k="50" />
    <hkern u1="S" u2="Y" k="50" />
    <hkern u1="S" u2="X" k="75" />
    <hkern u1="S" u2="W" k="50" />
    <hkern u1="S" u2="V" k="50" />
    <hkern u1="S" u2="O" k="-25" />
    <hkern u1="S" u2="J" k="50" />
    <hkern u1="S" u2="A" k="125" />
    <hkern u1="S" u2="&#x39;" k="100" />
    <hkern u1="S" u2="&#x37;" k="100" />
    <hkern u1="T" u2="&#xff;" k="325" />
    <hkern u1="T" u2="&#xfd;" k="325" />
    <hkern u1="T" u2="&#xfc;" k="250" />
    <hkern u1="T" u2="&#xfb;" k="250" />
    <hkern u1="T" u2="&#xfa;" k="250" />
    <hkern u1="T" u2="&#xf9;" k="250" />
    <hkern u1="T" u2="&#xf8;" k="375" />
    <hkern u1="T" u2="&#xf6;" k="375" />
    <hkern u1="T" u2="&#xf5;" k="375" />
    <hkern u1="T" u2="&#xf4;" k="375" />
    <hkern u1="T" u2="&#xf3;" k="375" />
    <hkern u1="T" u2="&#xf2;" k="375" />
    <hkern u1="T" u2="&#xf1;" k="225" />
    <hkern u1="T" u2="&#xf0;" k="375" />
    <hkern u1="T" u2="&#xef;" k="100" />
    <hkern u1="T" u2="&#xee;" k="100" />
    <hkern u1="T" u2="&#xed;" k="100" />
    <hkern u1="T" u2="&#xec;" k="100" />
    <hkern u1="T" u2="&#xeb;" k="375" />
    <hkern u1="T" u2="&#xea;" k="375" />
    <hkern u1="T" u2="&#xe9;" k="375" />
    <hkern u1="T" u2="&#xe8;" k="375" />
    <hkern u1="T" u2="&#xe7;" k="375" />
    <hkern u1="T" u2="&#xe5;" k="325" />
    <hkern u1="T" u2="&#xe4;" k="325" />
    <hkern u1="T" u2="&#xe3;" k="325" />
    <hkern u1="T" u2="&#xe2;" k="325" />
    <hkern u1="T" u2="&#xe1;" k="325" />
    <hkern u1="T" u2="&#xe0;" k="325" />
    <hkern u1="T" u2="&#x178;" k="50" />
    <hkern u1="T" u2="&#xdd;" k="50" />
    <hkern u1="T" u2="&#xd8;" k="150" />
    <hkern u1="T" u2="&#xd6;" k="150" />
    <hkern u1="T" u2="&#xd5;" k="150" />
    <hkern u1="T" u2="&#xd4;" k="150" />
    <hkern u1="T" u2="&#xd3;" k="150" />
    <hkern u1="T" u2="&#xd2;" k="150" />
    <hkern u1="T" u2="&#xc7;" k="150" />
    <hkern u1="T" u2="&#xc5;" k="475" />
    <hkern u1="T" u2="&#xc4;" k="475" />
    <hkern u1="T" u2="&#xc3;" k="475" />
    <hkern u1="T" u2="&#xc2;" k="475" />
    <hkern u1="T" u2="&#xc1;" k="475" />
    <hkern u1="T" u2="&#xc0;" k="475" />
    <hkern u1="T" u2="z" k="225" />
    <hkern u1="T" u2="y" k="325" />
    <hkern u1="T" u2="x" k="325" />
    <hkern u1="T" u2="w" k="325" />
    <hkern u1="T" u2="v" k="325" />
    <hkern u1="T" u2="u" k="250" />
    <hkern u1="T" u2="t" k="225" />
    <hkern u1="T" u2="s" k="225" />
    <hkern u1="T" u2="r" k="225" />
    <hkern u1="T" u2="q" k="325" />
    <hkern u1="T" u2="p" k="225" />
    <hkern u1="T" u2="o" k="375" />
    <hkern u1="T" u2="n" k="225" />
    <hkern u1="T" u2="m" k="225" />
    <hkern u1="T" u2="j" k="150" />
    <hkern u1="T" u2="i" k="100" />
    <hkern u1="T" u2="g" k="325" />
    <hkern u1="T" u2="f" k="225" />
    <hkern u1="T" u2="e" k="375" />
    <hkern u1="T" u2="d" k="375" />
    <hkern u1="T" u2="c" k="375" />
    <hkern u1="T" u2="a" k="325" />
    <hkern u1="T" u2="Z" k="50" />
    <hkern u1="T" u2="Y" k="50" />
    <hkern u1="T" u2="X" k="50" />
    <hkern u1="T" u2="W" k="50" />
    <hkern u1="T" u2="V" k="50" />
    <hkern u1="T" u2="S" k="50" />
    <hkern u1="T" u2="Q" k="150" />
    <hkern u1="T" u2="O" k="150" />
    <hkern u1="T" u2="J" k="500" />
    <hkern u1="T" u2="G" k="150" />
    <hkern u1="T" u2="C" k="150" />
    <hkern u1="T" u2="A" k="475" />
    <hkern u1="T" u2="&#x39;" k="100" />
    <hkern u1="T" u2="&#x38;" k="100" />
    <hkern u1="T" u2="&#x37;" k="100" />
    <hkern u1="T" u2="&#x36;" k="150" />
    <hkern u1="T" u2="&#x35;" k="100" />
    <hkern u1="T" u2="&#x34;" k="450" />
    <hkern u1="T" u2="&#x33;" k="100" />
    <hkern u1="T" u2="&#x32;" k="100" />
    <hkern u1="T" u2="&#x30;" k="100" />
    <hkern u1="T" u2="&#x2f;" k="450" />
    <hkern u1="T" u2="&#x26;" k="175" />
    <hkern u1="U" u2="&#xff;" k="75" />
    <hkern u1="U" u2="&#xfd;" k="75" />
    <hkern u1="U" u2="&#xfc;" k="50" />
    <hkern u1="U" u2="&#xfb;" k="50" />
    <hkern u1="U" u2="&#xfa;" k="50" />
    <hkern u1="U" u2="&#xf9;" k="50" />
    <hkern u1="U" u2="&#xe5;" k="50" />
    <hkern u1="U" u2="&#xe4;" k="50" />
    <hkern u1="U" u2="&#xe3;" k="50" />
    <hkern u1="U" u2="&#xe2;" k="50" />
    <hkern u1="U" u2="&#xe1;" k="50" />
    <hkern u1="U" u2="&#xe0;" k="50" />
    <hkern u1="U" u2="&#x178;" k="50" />
    <hkern u1="U" u2="&#xdd;" k="50" />
    <hkern u1="U" u2="&#xc5;" k="175" />
    <hkern u1="U" u2="&#xc4;" k="175" />
    <hkern u1="U" u2="&#xc3;" k="175" />
    <hkern u1="U" u2="&#xc2;" k="175" />
    <hkern u1="U" u2="&#xc1;" k="175" />
    <hkern u1="U" u2="&#xc0;" k="175" />
    <hkern u1="U" u2="z" k="75" />
    <hkern u1="U" u2="y" k="75" />
    <hkern u1="U" u2="x" k="75" />
    <hkern u1="U" u2="w" k="75" />
    <hkern u1="U" u2="v" k="75" />
    <hkern u1="U" u2="u" k="50" />
    <hkern u1="U" u2="t" k="50" />
    <hkern u1="U" u2="j" k="75" />
    <hkern u1="U" u2="f" k="75" />
    <hkern u1="U" u2="a" k="50" />
    <hkern u1="U" u2="Z" k="50" />
    <hkern u1="U" u2="Y" k="50" />
    <hkern u1="U" u2="X" k="50" />
    <hkern u1="U" u2="W" k="50" />
    <hkern u1="U" u2="V" k="50" />
    <hkern u1="U" u2="J" k="150" />
    <hkern u1="U" u2="A" k="175" />
    <hkern u1="U" u2="&#x37;" k="100" />
    <hkern u1="U" u2="&#x32;" k="100" />
    <hkern u1="U" u2="&#x2f;" k="125" />
    <hkern u1="V" u2="&#xff;" k="325" />
    <hkern u1="V" u2="&#xfd;" k="325" />
    <hkern u1="V" u2="&#xfc;" k="250" />
    <hkern u1="V" u2="&#xfb;" k="250" />
    <hkern u1="V" u2="&#xfa;" k="250" />
    <hkern u1="V" u2="&#xf9;" k="250" />
    <hkern u1="V" u2="&#xf8;" k="325" />
    <hkern u1="V" u2="&#xf6;" k="325" />
    <hkern u1="V" u2="&#xf5;" k="325" />
    <hkern u1="V" u2="&#xf4;" k="325" />
    <hkern u1="V" u2="&#xf3;" k="325" />
    <hkern u1="V" u2="&#xf2;" k="325" />
    <hkern u1="V" u2="&#xf1;" k="200" />
    <hkern u1="V" u2="&#xf0;" k="325" />
    <hkern u1="V" u2="&#xef;" k="200" />
    <hkern u1="V" u2="&#xee;" k="200" />
    <hkern u1="V" u2="&#xed;" k="200" />
    <hkern u1="V" u2="&#xec;" k="200" />
    <hkern u1="V" u2="&#xeb;" k="325" />
    <hkern u1="V" u2="&#xea;" k="325" />
    <hkern u1="V" u2="&#xe9;" k="325" />
    <hkern u1="V" u2="&#xe8;" k="325" />
    <hkern u1="V" u2="&#xe7;" k="325" />
    <hkern u1="V" u2="&#xe5;" k="375" />
    <hkern u1="V" u2="&#xe4;" k="375" />
    <hkern u1="V" u2="&#xe3;" k="375" />
    <hkern u1="V" u2="&#xe2;" k="375" />
    <hkern u1="V" u2="&#xe1;" k="375" />
    <hkern u1="V" u2="&#xe0;" k="375" />
    <hkern u1="V" u2="&#x178;" k="50" />
    <hkern u1="V" u2="&#xdd;" k="50" />
    <hkern u1="V" u2="&#xdc;" k="50" />
    <hkern u1="V" u2="&#xdb;" k="50" />
    <hkern u1="V" u2="&#xda;" k="50" />
    <hkern u1="V" u2="&#xd9;" k="50" />
    <hkern u1="V" u2="&#xd8;" k="125" />
    <hkern u1="V" u2="&#xd6;" k="125" />
    <hkern u1="V" u2="&#xd5;" k="125" />
    <hkern u1="V" u2="&#xd4;" k="125" />
    <hkern u1="V" u2="&#xd3;" k="125" />
    <hkern u1="V" u2="&#xd2;" k="125" />
    <hkern u1="V" u2="&#xc7;" k="150" />
    <hkern u1="V" u2="&#xc5;" k="550" />
    <hkern u1="V" u2="&#xc4;" k="550" />
    <hkern u1="V" u2="&#xc3;" k="550" />
    <hkern u1="V" u2="&#xc2;" k="550" />
    <hkern u1="V" u2="&#xc1;" k="550" />
    <hkern u1="V" u2="&#xc0;" k="550" />
    <hkern u1="V" u2="z" k="325" />
    <hkern u1="V" u2="y" k="325" />
    <hkern u1="V" u2="x" k="325" />
    <hkern u1="V" u2="w" k="325" />
    <hkern u1="V" u2="v" k="325" />
    <hkern u1="V" u2="u" k="250" />
    <hkern u1="V" u2="t" k="250" />
    <hkern u1="V" u2="s" k="300" />
    <hkern u1="V" u2="r" k="200" />
    <hkern u1="V" u2="q" k="325" />
    <hkern u1="V" u2="p" k="200" />
    <hkern u1="V" u2="o" k="325" />
    <hkern u1="V" u2="n" k="200" />
    <hkern u1="V" u2="m" k="200" />
    <hkern u1="V" u2="l" k="50" />
    <hkern u1="V" u2="k" k="50" />
    <hkern u1="V" u2="j" k="250" />
    <hkern u1="V" u2="i" k="200" />
    <hkern u1="V" u2="h" k="50" />
    <hkern u1="V" u2="g" k="325" />
    <hkern u1="V" u2="f" k="225" />
    <hkern u1="V" u2="e" k="325" />
    <hkern u1="V" u2="d" k="325" />
    <hkern u1="V" u2="c" k="325" />
    <hkern u1="V" u2="b" k="50" />
    <hkern u1="V" u2="a" k="375" />
    <hkern u1="V" u2="Z" k="125" />
    <hkern u1="V" u2="Y" k="50" />
    <hkern u1="V" u2="X" k="50" />
    <hkern u1="V" u2="W" k="50" />
    <hkern u1="V" u2="V" k="50" />
    <hkern u1="V" u2="U" k="50" />
    <hkern u1="V" u2="T" k="50" />
    <hkern u1="V" u2="S" k="125" />
    <hkern u1="V" u2="R" k="50" />
    <hkern u1="V" u2="Q" k="150" />
    <hkern u1="V" u2="P" k="50" />
    <hkern u1="V" u2="O" k="125" />
    <hkern u1="V" u2="M" k="50" />
    <hkern u1="V" u2="L" k="50" />
    <hkern u1="V" u2="K" k="75" />
    <hkern u1="V" u2="J" k="500" />
    <hkern u1="V" u2="G" k="175" />
    <hkern u1="V" u2="F" k="50" />
    <hkern u1="V" u2="C" k="150" />
    <hkern u1="V" u2="A" k="550" />
    <hkern u1="V" u2="&#x39;" k="175" />
    <hkern u1="V" u2="&#x38;" k="175" />
    <hkern u1="V" u2="&#x37;" k="175" />
    <hkern u1="V" u2="&#x36;" k="175" />
    <hkern u1="V" u2="&#x35;" k="175" />
    <hkern u1="V" u2="&#x34;" k="400" />
    <hkern u1="V" u2="&#x33;" k="175" />
    <hkern u1="V" u2="&#x32;" k="175" />
    <hkern u1="V" u2="&#x31;" k="75" />
    <hkern u1="V" u2="&#x30;" k="175" />
    <hkern u1="V" u2="&#x2f;" k="525" />
    <hkern u1="V" u2="&#x26;" k="250" />
    <hkern u1="W" u2="&#xff;" k="375" />
    <hkern u1="W" u2="&#xfd;" k="375" />
    <hkern u1="W" u2="&#xfc;" k="325" />
    <hkern u1="W" u2="&#xfb;" k="325" />
    <hkern u1="W" u2="&#xfa;" k="325" />
    <hkern u1="W" u2="&#xf9;" k="325" />
    <hkern u1="W" u2="&#xf8;" k="400" />
    <hkern u1="W" u2="&#xf6;" k="400" />
    <hkern u1="W" u2="&#xf5;" k="400" />
    <hkern u1="W" u2="&#xf4;" k="400" />
    <hkern u1="W" u2="&#xf3;" k="400" />
    <hkern u1="W" u2="&#xf2;" k="400" />
    <hkern u1="W" u2="&#xf1;" k="325" />
    <hkern u1="W" u2="&#xf0;" k="400" />
    <hkern u1="W" u2="&#xef;" k="275" />
    <hkern u1="W" u2="&#xee;" k="275" />
    <hkern u1="W" u2="&#xed;" k="275" />
    <hkern u1="W" u2="&#xec;" k="275" />
    <hkern u1="W" u2="&#xeb;" k="400" />
    <hkern u1="W" u2="&#xea;" k="400" />
    <hkern u1="W" u2="&#xe9;" k="400" />
    <hkern u1="W" u2="&#xe8;" k="400" />
    <hkern u1="W" u2="&#xe7;" k="400" />
    <hkern u1="W" u2="&#xe5;" k="400" />
    <hkern u1="W" u2="&#xe4;" k="400" />
    <hkern u1="W" u2="&#xe3;" k="400" />
    <hkern u1="W" u2="&#xe2;" k="400" />
    <hkern u1="W" u2="&#xe1;" k="400" />
    <hkern u1="W" u2="&#xe0;" k="400" />
    <hkern u1="W" u2="&#x178;" k="50" />
    <hkern u1="W" u2="&#xdd;" k="50" />
    <hkern u1="W" u2="&#xdc;" k="50" />
    <hkern u1="W" u2="&#xdb;" k="50" />
    <hkern u1="W" u2="&#xda;" k="50" />
    <hkern u1="W" u2="&#xd9;" k="50" />
    <hkern u1="W" u2="&#xd8;" k="125" />
    <hkern u1="W" u2="&#xd6;" k="125" />
    <hkern u1="W" u2="&#xd5;" k="125" />
    <hkern u1="W" u2="&#xd4;" k="125" />
    <hkern u1="W" u2="&#xd3;" k="125" />
    <hkern u1="W" u2="&#xd2;" k="125" />
    <hkern u1="W" u2="&#xd0;" k="75" />
    <hkern u1="W" u2="&#xcb;" k="50" />
    <hkern u1="W" u2="&#xca;" k="50" />
    <hkern u1="W" u2="&#xc9;" k="50" />
    <hkern u1="W" u2="&#xc8;" k="50" />
    <hkern u1="W" u2="&#xc7;" k="175" />
    <hkern u1="W" u2="&#xc5;" k="575" />
    <hkern u1="W" u2="&#xc4;" k="575" />
    <hkern u1="W" u2="&#xc3;" k="575" />
    <hkern u1="W" u2="&#xc2;" k="575" />
    <hkern u1="W" u2="&#xc1;" k="575" />
    <hkern u1="W" u2="&#xc0;" k="575" />
    <hkern u1="W" u2="z" k="400" />
    <hkern u1="W" u2="y" k="375" />
    <hkern u1="W" u2="x" k="375" />
    <hkern u1="W" u2="w" k="350" />
    <hkern u1="W" u2="v" k="375" />
    <hkern u1="W" u2="u" k="325" />
    <hkern u1="W" u2="t" k="325" />
    <hkern u1="W" u2="s" k="375" />
    <hkern u1="W" u2="r" k="325" />
    <hkern u1="W" u2="q" k="400" />
    <hkern u1="W" u2="p" k="325" />
    <hkern u1="W" u2="o" k="400" />
    <hkern u1="W" u2="n" k="325" />
    <hkern u1="W" u2="m" k="325" />
    <hkern u1="W" u2="l" k="75" />
    <hkern u1="W" u2="k" k="75" />
    <hkern u1="W" u2="j" k="325" />
    <hkern u1="W" u2="i" k="275" />
    <hkern u1="W" u2="h" k="75" />
    <hkern u1="W" u2="g" k="400" />
    <hkern u1="W" u2="f" k="375" />
    <hkern u1="W" u2="e" k="400" />
    <hkern u1="W" u2="d" k="400" />
    <hkern u1="W" u2="c" k="400" />
    <hkern u1="W" u2="b" k="75" />
    <hkern u1="W" u2="a" k="400" />
    <hkern u1="W" u2="Z" k="125" />
    <hkern u1="W" u2="Y" k="50" />
    <hkern u1="W" u2="X" k="50" />
    <hkern u1="W" u2="W" k="50" />
    <hkern u1="W" u2="V" k="50" />
    <hkern u1="W" u2="U" k="50" />
    <hkern u1="W" u2="T" k="50" />
    <hkern u1="W" u2="S" k="150" />
    <hkern u1="W" u2="R" k="50" />
    <hkern u1="W" u2="Q" k="150" />
    <hkern u1="W" u2="P" k="50" />
    <hkern u1="W" u2="O" k="125" />
    <hkern u1="W" u2="M" k="50" />
    <hkern u1="W" u2="L" k="50" />
    <hkern u1="W" u2="K" k="75" />
    <hkern u1="W" u2="J" k="500" />
    <hkern u1="W" u2="G" k="175" />
    <hkern u1="W" u2="F" k="50" />
    <hkern u1="W" u2="E" k="50" />
    <hkern u1="W" u2="D" k="75" />
    <hkern u1="W" u2="C" k="175" />
    <hkern u1="W" u2="A" k="575" />
    <hkern u1="W" u2="&#x39;" k="175" />
    <hkern u1="W" u2="&#x38;" k="175" />
    <hkern u1="W" u2="&#x37;" k="150" />
    <hkern u1="W" u2="&#x36;" k="225" />
    <hkern u1="W" u2="&#x35;" k="175" />
    <hkern u1="W" u2="&#x34;" k="500" />
    <hkern u1="W" u2="&#x33;" k="225" />
    <hkern u1="W" u2="&#x32;" k="225" />
    <hkern u1="W" u2="&#x31;" k="75" />
    <hkern u1="W" u2="&#x30;" k="175" />
    <hkern u1="W" u2="&#x2f;" k="525" />
    <hkern u1="W" u2="&#x26;" k="275" />
    <hkern u1="X" u2="&#xff;" k="400" />
    <hkern u1="X" u2="&#xfd;" k="400" />
    <hkern u1="X" u2="&#xfc;" k="175" />
    <hkern u1="X" u2="&#xfb;" k="175" />
    <hkern u1="X" u2="&#xfa;" k="175" />
    <hkern u1="X" u2="&#xf9;" k="175" />
    <hkern u1="X" u2="&#xf8;" k="150" />
    <hkern u1="X" u2="&#xf6;" k="150" />
    <hkern u1="X" u2="&#xf5;" k="150" />
    <hkern u1="X" u2="&#xf4;" k="150" />
    <hkern u1="X" u2="&#xf3;" k="150" />
    <hkern u1="X" u2="&#xf2;" k="150" />
    <hkern u1="X" u2="&#xf0;" k="150" />
    <hkern u1="X" u2="&#xeb;" k="150" />
    <hkern u1="X" u2="&#xea;" k="150" />
    <hkern u1="X" u2="&#xe9;" k="150" />
    <hkern u1="X" u2="&#xe8;" k="150" />
    <hkern u1="X" u2="&#xe7;" k="150" />
    <hkern u1="X" u2="&#xe5;" k="75" />
    <hkern u1="X" u2="&#xe4;" k="75" />
    <hkern u1="X" u2="&#xe3;" k="75" />
    <hkern u1="X" u2="&#xe2;" k="75" />
    <hkern u1="X" u2="&#xe1;" k="75" />
    <hkern u1="X" u2="&#xe0;" k="75" />
    <hkern u1="X" u2="&#x178;" k="50" />
    <hkern u1="X" u2="&#xdd;" k="50" />
    <hkern u1="X" u2="&#xdc;" k="50" />
    <hkern u1="X" u2="&#xdb;" k="50" />
    <hkern u1="X" u2="&#xda;" k="50" />
    <hkern u1="X" u2="&#xd9;" k="50" />
    <hkern u1="X" u2="&#xd8;" k="125" />
    <hkern u1="X" u2="&#xd6;" k="125" />
    <hkern u1="X" u2="&#xd5;" k="125" />
    <hkern u1="X" u2="&#xd4;" k="125" />
    <hkern u1="X" u2="&#xd3;" k="125" />
    <hkern u1="X" u2="&#xd2;" k="125" />
    <hkern u1="X" u2="&#xc7;" k="225" />
    <hkern u1="X" u2="&#xc5;" k="75" />
    <hkern u1="X" u2="&#xc4;" k="75" />
    <hkern u1="X" u2="&#xc3;" k="75" />
    <hkern u1="X" u2="&#xc2;" k="75" />
    <hkern u1="X" u2="&#xc1;" k="75" />
    <hkern u1="X" u2="&#xc0;" k="75" />
    <hkern u1="X" u2="z" k="75" />
    <hkern u1="X" u2="y" k="400" />
    <hkern u1="X" u2="x" k="75" />
    <hkern u1="X" u2="w" k="400" />
    <hkern u1="X" u2="v" k="400" />
    <hkern u1="X" u2="u" k="175" />
    <hkern u1="X" u2="t" k="175" />
    <hkern u1="X" u2="s" k="50" />
    <hkern u1="X" u2="q" k="150" />
    <hkern u1="X" u2="o" k="150" />
    <hkern u1="X" u2="j" k="75" />
    <hkern u1="X" u2="g" k="75" />
    <hkern u1="X" u2="f" k="150" />
    <hkern u1="X" u2="e" k="150" />
    <hkern u1="X" u2="d" k="150" />
    <hkern u1="X" u2="c" k="150" />
    <hkern u1="X" u2="a" k="75" />
    <hkern u1="X" u2="Z" k="50" />
    <hkern u1="X" u2="Y" k="50" />
    <hkern u1="X" u2="X" k="50" />
    <hkern u1="X" u2="W" k="50" />
    <hkern u1="X" u2="V" k="50" />
    <hkern u1="X" u2="U" k="50" />
    <hkern u1="X" u2="T" k="50" />
    <hkern u1="X" u2="S" k="75" />
    <hkern u1="X" u2="R" k="50" />
    <hkern u1="X" u2="Q" k="150" />
    <hkern u1="X" u2="P" k="50" />
    <hkern u1="X" u2="O" k="125" />
    <hkern u1="X" u2="K" k="50" />
    <hkern u1="X" u2="J" k="250" />
    <hkern u1="X" u2="F" k="50" />
    <hkern u1="X" u2="C" k="225" />
    <hkern u1="X" u2="A" k="75" />
    <hkern u1="X" u2="&#x39;" k="250" />
    <hkern u1="X" u2="&#x38;" k="200" />
    <hkern u1="X" u2="&#x37;" k="100" />
    <hkern u1="X" u2="&#x36;" k="200" />
    <hkern u1="X" u2="&#x35;" k="200" />
    <hkern u1="X" u2="&#x34;" k="375" />
    <hkern u1="X" u2="&#x33;" k="200" />
    <hkern u1="X" u2="&#x32;" k="100" />
    <hkern u1="X" u2="&#x31;" k="100" />
    <hkern u1="X" u2="&#x30;" k="250" />
    <hkern u1="X" u2="&#x26;" k="225" />
    <hkern u1="Y" u2="&#xff;" k="475" />
    <hkern u1="Y" u2="&#xfd;" k="475" />
    <hkern u1="Y" u2="&#xfc;" k="400" />
    <hkern u1="Y" u2="&#xfb;" k="400" />
    <hkern u1="Y" u2="&#xfa;" k="400" />
    <hkern u1="Y" u2="&#xf9;" k="400" />
    <hkern u1="Y" u2="&#xf8;" k="525" />
    <hkern u1="Y" u2="&#xf6;" k="525" />
    <hkern u1="Y" u2="&#xf5;" k="525" />
    <hkern u1="Y" u2="&#xf4;" k="525" />
    <hkern u1="Y" u2="&#xf3;" k="525" />
    <hkern u1="Y" u2="&#xf2;" k="525" />
    <hkern u1="Y" u2="&#xf1;" k="400" />
    <hkern u1="Y" u2="&#xf0;" k="525" />
    <hkern u1="Y" u2="&#xef;" k="300" />
    <hkern u1="Y" u2="&#xee;" k="300" />
    <hkern u1="Y" u2="&#xed;" k="300" />
    <hkern u1="Y" u2="&#xec;" k="300" />
    <hkern u1="Y" u2="&#xeb;" k="525" />
    <hkern u1="Y" u2="&#xea;" k="525" />
    <hkern u1="Y" u2="&#xe9;" k="525" />
    <hkern u1="Y" u2="&#xe8;" k="525" />
    <hkern u1="Y" u2="&#xe7;" k="525" />
    <hkern u1="Y" u2="&#xe5;" k="525" />
    <hkern u1="Y" u2="&#xe4;" k="525" />
    <hkern u1="Y" u2="&#xe3;" k="525" />
    <hkern u1="Y" u2="&#xe2;" k="525" />
    <hkern u1="Y" u2="&#xe1;" k="525" />
    <hkern u1="Y" u2="&#xe0;" k="525" />
    <hkern u1="Y" u2="&#x178;" k="50" />
    <hkern u1="Y" u2="&#xdd;" k="50" />
    <hkern u1="Y" u2="&#xdc;" k="50" />
    <hkern u1="Y" u2="&#xdb;" k="50" />
    <hkern u1="Y" u2="&#xda;" k="50" />
    <hkern u1="Y" u2="&#xd9;" k="50" />
    <hkern u1="Y" u2="&#xd8;" k="200" />
    <hkern u1="Y" u2="&#xd6;" k="200" />
    <hkern u1="Y" u2="&#xd5;" k="200" />
    <hkern u1="Y" u2="&#xd4;" k="200" />
    <hkern u1="Y" u2="&#xd3;" k="200" />
    <hkern u1="Y" u2="&#xd2;" k="200" />
    <hkern u1="Y" u2="&#xd0;" k="50" />
    <hkern u1="Y" u2="&#xcb;" k="50" />
    <hkern u1="Y" u2="&#xca;" k="50" />
    <hkern u1="Y" u2="&#xc9;" k="50" />
    <hkern u1="Y" u2="&#xc8;" k="50" />
    <hkern u1="Y" u2="&#xc7;" k="250" />
    <hkern u1="Y" u2="&#xc5;" k="600" />
    <hkern u1="Y" u2="&#xc4;" k="600" />
    <hkern u1="Y" u2="&#xc3;" k="600" />
    <hkern u1="Y" u2="&#xc2;" k="600" />
    <hkern u1="Y" u2="&#xc1;" k="600" />
    <hkern u1="Y" u2="&#xc0;" k="600" />
    <hkern u1="Y" u2="z" k="450" />
    <hkern u1="Y" u2="y" k="475" />
    <hkern u1="Y" u2="x" k="450" />
    <hkern u1="Y" u2="w" k="450" />
    <hkern u1="Y" u2="v" k="450" />
    <hkern u1="Y" u2="u" k="400" />
    <hkern u1="Y" u2="t" k="400" />
    <hkern u1="Y" u2="s" k="400" />
    <hkern u1="Y" u2="r" k="400" />
    <hkern u1="Y" u2="q" k="525" />
    <hkern u1="Y" u2="p" k="400" />
    <hkern u1="Y" u2="o" k="525" />
    <hkern u1="Y" u2="n" k="400" />
    <hkern u1="Y" u2="m" k="400" />
    <hkern u1="Y" u2="l" k="75" />
    <hkern u1="Y" u2="k" k="75" />
    <hkern u1="Y" u2="j" k="375" />
    <hkern u1="Y" u2="i" k="300" />
    <hkern u1="Y" u2="h" k="75" />
    <hkern u1="Y" u2="g" k="525" />
    <hkern u1="Y" u2="f" k="375" />
    <hkern u1="Y" u2="e" k="525" />
    <hkern u1="Y" u2="d" k="525" />
    <hkern u1="Y" u2="c" k="525" />
    <hkern u1="Y" u2="b" k="75" />
    <hkern u1="Y" u2="a" k="525" />
    <hkern u1="Y" u2="Z" k="125" />
    <hkern u1="Y" u2="Y" k="50" />
    <hkern u1="Y" u2="X" k="50" />
    <hkern u1="Y" u2="W" k="50" />
    <hkern u1="Y" u2="V" k="50" />
    <hkern u1="Y" u2="U" k="50" />
    <hkern u1="Y" u2="T" k="50" />
    <hkern u1="Y" u2="S" k="125" />
    <hkern u1="Y" u2="R" k="50" />
    <hkern u1="Y" u2="Q" k="200" />
    <hkern u1="Y" u2="P" k="50" />
    <hkern u1="Y" u2="O" k="200" />
    <hkern u1="Y" u2="K" k="50" />
    <hkern u1="Y" u2="J" k="600" />
    <hkern u1="Y" u2="H" k="50" />
    <hkern u1="Y" u2="G" k="300" />
    <hkern u1="Y" u2="F" k="50" />
    <hkern u1="Y" u2="E" k="50" />
    <hkern u1="Y" u2="D" k="50" />
    <hkern u1="Y" u2="C" k="250" />
    <hkern u1="Y" u2="B" k="50" />
    <hkern u1="Y" u2="A" k="600" />
    <hkern u1="Y" u2="&#x39;" k="225" />
    <hkern u1="Y" u2="&#x38;" k="225" />
    <hkern u1="Y" u2="&#x37;" k="150" />
    <hkern u1="Y" u2="&#x36;" k="275" />
    <hkern u1="Y" u2="&#x35;" k="225" />
    <hkern u1="Y" u2="&#x34;" k="650" />
    <hkern u1="Y" u2="&#x33;" k="225" />
    <hkern u1="Y" u2="&#x32;" k="225" />
    <hkern u1="Y" u2="&#x31;" k="100" />
    <hkern u1="Y" u2="&#x30;" k="225" />
    <hkern u1="Y" u2="&#x2f;" k="600" />
    <hkern u1="Y" u2="&#x26;" k="325" />
    <hkern u1="Z" u2="&#xff;" k="275" />
    <hkern u1="Z" u2="&#xfd;" k="275" />
    <hkern u1="Z" u2="&#xfc;" k="50" />
    <hkern u1="Z" u2="&#xfb;" k="50" />
    <hkern u1="Z" u2="&#xfa;" k="50" />
    <hkern u1="Z" u2="&#xf9;" k="50" />
    <hkern u1="Z" u2="&#xf8;" k="50" />
    <hkern u1="Z" u2="&#xf6;" k="50" />
    <hkern u1="Z" u2="&#xf5;" k="50" />
    <hkern u1="Z" u2="&#xf4;" k="50" />
    <hkern u1="Z" u2="&#xf3;" k="50" />
    <hkern u1="Z" u2="&#xf2;" k="50" />
    <hkern u1="Z" u2="&#xf0;" k="50" />
    <hkern u1="Z" u2="&#xeb;" k="50" />
    <hkern u1="Z" u2="&#xea;" k="50" />
    <hkern u1="Z" u2="&#xe9;" k="50" />
    <hkern u1="Z" u2="&#xe8;" k="50" />
    <hkern u1="Z" u2="&#xe7;" k="50" />
    <hkern u1="Z" u2="&#x178;" k="25" />
    <hkern u1="Z" u2="&#xdd;" k="25" />
    <hkern u1="Z" u2="&#xd8;" k="125" />
    <hkern u1="Z" u2="&#xd6;" k="125" />
    <hkern u1="Z" u2="&#xd5;" k="125" />
    <hkern u1="Z" u2="&#xd4;" k="125" />
    <hkern u1="Z" u2="&#xd3;" k="125" />
    <hkern u1="Z" u2="&#xd2;" k="125" />
    <hkern u1="Z" u2="&#xc7;" k="125" />
    <hkern u1="Z" u2="&#xc5;" k="75" />
    <hkern u1="Z" u2="&#xc4;" k="75" />
    <hkern u1="Z" u2="&#xc3;" k="75" />
    <hkern u1="Z" u2="&#xc2;" k="75" />
    <hkern u1="Z" u2="&#xc1;" k="75" />
    <hkern u1="Z" u2="&#xc0;" k="75" />
    <hkern u1="Z" u2="z" k="75" />
    <hkern u1="Z" u2="y" k="275" />
    <hkern u1="Z" u2="x" k="75" />
    <hkern u1="Z" u2="w" k="275" />
    <hkern u1="Z" u2="v" k="275" />
    <hkern u1="Z" u2="u" k="50" />
    <hkern u1="Z" u2="t" k="50" />
    <hkern u1="Z" u2="q" k="50" />
    <hkern u1="Z" u2="o" k="50" />
    <hkern u1="Z" u2="j" k="100" />
    <hkern u1="Z" u2="g" k="50" />
    <hkern u1="Z" u2="f" k="75" />
    <hkern u1="Z" u2="e" k="50" />
    <hkern u1="Z" u2="d" k="50" />
    <hkern u1="Z" u2="c" k="50" />
    <hkern u1="Z" u2="Y" k="25" />
    <hkern u1="Z" u2="X" k="50" />
    <hkern u1="Z" u2="W" k="50" />
    <hkern u1="Z" u2="V" k="50" />
    <hkern u1="Z" u2="T" k="50" />
    <hkern u1="Z" u2="Q" k="125" />
    <hkern u1="Z" u2="O" k="125" />
    <hkern u1="Z" u2="J" k="125" />
    <hkern u1="Z" u2="G" k="125" />
    <hkern u1="Z" u2="C" k="125" />
    <hkern u1="Z" u2="A" k="75" />
    <hkern u1="Z" u2="&#x39;" k="100" />
    <hkern u1="Z" u2="&#x35;" k="75" />
    <hkern u1="Z" u2="&#x34;" k="225" />
    <hkern u1="Z" u2="&#x33;" k="75" />
    <hkern u1="Z" u2="&#x32;" k="125" />
    <hkern u1="Z" u2="&#x31;" k="50" />
    <hkern u1="Z" u2="&#x30;" k="175" />
    <hkern u1="Z" u2="&#x2f;" k="75" />
    <hkern u1="Z" u2="&#x26;" k="50" />
    <hkern u1="[" u2="&#xff;" k="125" />
    <hkern u1="[" u2="&#xfd;" k="125" />
    <hkern u1="[" u2="y" k="125" />
    <hkern u1="[" u2="x" k="100" />
    <hkern u1="[" u2="w" k="125" />
    <hkern u1="[" u2="v" k="125" />
    <hkern u1="\" u2="&#xff;" k="475" />
    <hkern u1="\" u2="&#xfd;" k="475" />
    <hkern u1="\" u2="&#xfc;" k="150" />
    <hkern u1="\" u2="&#xfb;" k="150" />
    <hkern u1="\" u2="&#xfa;" k="150" />
    <hkern u1="\" u2="&#xf9;" k="150" />
    <hkern u1="\" u2="&#xf8;" k="150" />
    <hkern u1="\" u2="&#xf6;" k="150" />
    <hkern u1="\" u2="&#xf5;" k="150" />
    <hkern u1="\" u2="&#xf4;" k="150" />
    <hkern u1="\" u2="&#xf3;" k="150" />
    <hkern u1="\" u2="&#xf2;" k="150" />
    <hkern u1="\" u2="&#xf0;" k="150" />
    <hkern u1="\" u2="&#xeb;" k="150" />
    <hkern u1="\" u2="&#xea;" k="150" />
    <hkern u1="\" u2="&#xe9;" k="150" />
    <hkern u1="\" u2="&#xe8;" k="150" />
    <hkern u1="\" u2="&#xe7;" k="150" />
    <hkern u1="\" u2="&#x178;" k="600" />
    <hkern u1="\" u2="&#xdd;" k="600" />
    <hkern u1="\" u2="&#xdc;" k="125" />
    <hkern u1="\" u2="&#xdb;" k="125" />
    <hkern u1="\" u2="&#xda;" k="125" />
    <hkern u1="\" u2="&#xd9;" k="125" />
    <hkern u1="\" u2="&#xd8;" k="250" />
    <hkern u1="\" u2="&#xd6;" k="250" />
    <hkern u1="\" u2="&#xd5;" k="250" />
    <hkern u1="\" u2="&#xd4;" k="250" />
    <hkern u1="\" u2="&#xd3;" k="250" />
    <hkern u1="\" u2="&#xd2;" k="250" />
    <hkern u1="\" u2="&#xc7;" k="250" />
    <hkern u1="\" u2="y" k="475" />
    <hkern u1="\" u2="x" k="75" />
    <hkern u1="\" u2="w" k="475" />
    <hkern u1="\" u2="v" k="475" />
    <hkern u1="\" u2="u" k="150" />
    <hkern u1="\" u2="t" k="150" />
    <hkern u1="\" u2="o" k="150" />
    <hkern u1="\" u2="f" k="150" />
    <hkern u1="\" u2="e" k="150" />
    <hkern u1="\" u2="d" k="150" />
    <hkern u1="\" u2="c" k="150" />
    <hkern u1="\" u2="Y" k="600" />
    <hkern u1="\" u2="X" k="75" />
    <hkern u1="\" u2="W" k="600" />
    <hkern u1="\" u2="V" k="650" />
    <hkern u1="\" u2="U" k="125" />
    <hkern u1="\" u2="T" k="525" />
    <hkern u1="\" u2="S" k="100" />
    <hkern u1="\" u2="Q" k="250" />
    <hkern u1="\" u2="O" k="250" />
    <hkern u1="\" u2="J" k="200" />
    <hkern u1="\" u2="G" k="250" />
    <hkern u1="\" u2="C" k="250" />
    <hkern u1="\" u2="&#x39;" k="450" />
    <hkern u1="\" u2="&#x38;" k="150" />
    <hkern u1="\" u2="&#x37;" k="75" />
    <hkern u1="\" u2="&#x36;" k="150" />
    <hkern u1="\" u2="&#x35;" k="200" />
    <hkern u1="\" u2="&#x34;" k="250" />
    <hkern u1="\" u2="&#x33;" k="200" />
    <hkern u1="\" u2="&#x31;" k="75" />
    <hkern u1="\" u2="&#x30;" k="200" />
    <hkern u1="^" u2="&#xf0;" k="290" />
    <hkern u1="^" u2="&#xc5;" k="375" />
    <hkern u1="^" u2="&#xc4;" k="375" />
    <hkern u1="^" u2="&#xc3;" k="375" />
    <hkern u1="^" u2="&#xc2;" k="375" />
    <hkern u1="^" u2="&#xc1;" k="375" />
    <hkern u1="^" u2="&#xc0;" k="375" />
    <hkern u1="^" u2="d" k="290" />
    <hkern u1="^" u2="J" k="525" />
    <hkern u1="^" u2="A" k="375" />
    <hkern u1="a" u2="&#xff;" k="150" />
    <hkern u1="a" u2="&#xfd;" k="150" />
    <hkern u1="a" u2="y" k="150" />
    <hkern u1="a" u2="x" k="50" />
    <hkern u1="a" u2="w" k="150" />
    <hkern u1="a" u2="v" k="125" />
    <hkern u1="a" u2="t" k="50" />
    <hkern u1="a" u2="j" k="100" />
    <hkern u1="a" u2="f" k="50" />
    <hkern u1="a" u2="\" k="350" />
    <hkern u1="a" u2="&#x3f;" k="175" />
    <hkern u1="a" u2="&#x39;" k="75" />
    <hkern u1="b" u2="&#xff;" k="150" />
    <hkern u1="b" u2="&#xfd;" k="150" />
    <hkern u1="b" u2="&#xf0;" k="-50" />
    <hkern u1="b" u2="&#xeb;" k="-50" />
    <hkern u1="b" u2="&#xea;" k="-50" />
    <hkern u1="b" u2="&#xe9;" k="-50" />
    <hkern u1="b" u2="&#xe8;" k="-50" />
    <hkern u1="b" u2="&#xe7;" k="-50" />
    <hkern u1="b" u2="z" k="125" />
    <hkern u1="b" u2="y" k="150" />
    <hkern u1="b" u2="x" k="150" />
    <hkern u1="b" u2="w" k="150" />
    <hkern u1="b" u2="v" k="150" />
    <hkern u1="b" u2="q" k="-25" />
    <hkern u1="b" u2="j" k="100" />
    <hkern u1="b" u2="e" k="-50" />
    <hkern u1="b" u2="d" k="-50" />
    <hkern u1="b" u2="c" k="-50" />
    <hkern u1="b" u2="\" k="325" />
    <hkern u1="b" u2="&#x3f;" k="175" />
    <hkern u1="b" u2="&#x39;" k="75" />
    <hkern u1="b" u2="&#x37;" k="162" />
    <hkern u1="b" u2="&#x2f;" k="150" />
    <hkern u1="c" u2="&#xff;" k="50" />
    <hkern u1="c" u2="&#xfd;" k="50" />
    <hkern u1="c" u2="&#xf8;" k="25" />
    <hkern u1="c" u2="&#xf6;" k="25" />
    <hkern u1="c" u2="&#xf5;" k="25" />
    <hkern u1="c" u2="&#xf4;" k="25" />
    <hkern u1="c" u2="&#xf3;" k="25" />
    <hkern u1="c" u2="&#xf2;" k="25" />
    <hkern u1="c" u2="&#xf0;" k="25" />
    <hkern u1="c" u2="&#xeb;" k="25" />
    <hkern u1="c" u2="&#xea;" k="25" />
    <hkern u1="c" u2="&#xe9;" k="25" />
    <hkern u1="c" u2="&#xe8;" k="25" />
    <hkern u1="c" u2="&#xe7;" k="25" />
    <hkern u1="c" u2="z" k="25" />
    <hkern u1="c" u2="y" k="50" />
    <hkern u1="c" u2="x" k="50" />
    <hkern u1="c" u2="w" k="50" />
    <hkern u1="c" u2="v" k="50" />
    <hkern u1="c" u2="q" k="25" />
    <hkern u1="c" u2="o" k="25" />
    <hkern u1="c" u2="j" k="75" />
    <hkern u1="c" u2="e" k="25" />
    <hkern u1="c" u2="d" k="25" />
    <hkern u1="c" u2="c" k="25" />
    <hkern u1="c" u2="\" k="225" />
    <hkern u1="c" u2="&#x3f;" k="175" />
    <hkern u1="c" u2="&#x37;" k="50" />
    <hkern u1="c" u2="&#x34;" k="175" />
    <hkern u1="c" u2="&#x26;" k="50" />
    <hkern u1="d" u2="&#xff;" k="50" />
    <hkern u1="d" u2="&#xfd;" k="50" />
    <hkern u1="d" u2="y" k="50" />
    <hkern u1="d" u2="w" k="50" />
    <hkern u1="d" u2="v" k="50" />
    <hkern u1="d" u2="j" k="100" />
    <hkern u1="e" u2="&#xff;" k="125" />
    <hkern u1="e" u2="&#xfd;" k="125" />
    <hkern u1="e" u2="&#xe5;" k="75" />
    <hkern u1="e" u2="&#xe4;" k="75" />
    <hkern u1="e" u2="&#xe3;" k="75" />
    <hkern u1="e" u2="&#xe2;" k="75" />
    <hkern u1="e" u2="&#xe1;" k="75" />
    <hkern u1="e" u2="&#xe0;" k="75" />
    <hkern u1="e" u2="z" k="125" />
    <hkern u1="e" u2="y" k="125" />
    <hkern u1="e" u2="x" k="150" />
    <hkern u1="e" u2="w" k="125" />
    <hkern u1="e" u2="v" k="125" />
    <hkern u1="e" u2="j" k="100" />
    <hkern u1="e" u2="a" k="75" />
    <hkern u1="e" u2="\" k="350" />
    <hkern u1="e" u2="&#x3f;" k="175" />
    <hkern u1="e" u2="&#x37;" k="125" />
    <hkern u1="e" u2="&#x32;" k="125" />
    <hkern u1="e" u2="&#x2f;" k="150" />
    <hkern u1="f" u2="&#xff;" k="150" />
    <hkern u1="f" u2="&#xfd;" k="150" />
    <hkern u1="f" u2="&#xfc;" k="125" />
    <hkern u1="f" u2="&#xfb;" k="125" />
    <hkern u1="f" u2="&#xfa;" k="125" />
    <hkern u1="f" u2="&#xf9;" k="125" />
    <hkern u1="f" u2="&#xf8;" k="250" />
    <hkern u1="f" u2="&#xf7;" k="287" />
    <hkern u1="f" u2="&#xf6;" k="250" />
    <hkern u1="f" u2="&#xf5;" k="250" />
    <hkern u1="f" u2="&#xf4;" k="250" />
    <hkern u1="f" u2="&#xf3;" k="250" />
    <hkern u1="f" u2="&#xf2;" k="250" />
    <hkern u1="f" u2="&#xf1;" k="125" />
    <hkern u1="f" u2="&#xf0;" k="225" />
    <hkern u1="f" u2="&#xef;" k="75" />
    <hkern u1="f" u2="&#xee;" k="75" />
    <hkern u1="f" u2="&#xed;" k="75" />
    <hkern u1="f" u2="&#xec;" k="75" />
    <hkern u1="f" u2="&#xeb;" k="250" />
    <hkern u1="f" u2="&#xea;" k="250" />
    <hkern u1="f" u2="&#xe9;" k="250" />
    <hkern u1="f" u2="&#xe8;" k="250" />
    <hkern u1="f" u2="&#xe7;" k="250" />
    <hkern u1="f" u2="&#xe5;" k="250" />
    <hkern u1="f" u2="&#xe4;" k="250" />
    <hkern u1="f" u2="&#xe3;" k="250" />
    <hkern u1="f" u2="&#xe2;" k="250" />
    <hkern u1="f" u2="&#xe1;" k="250" />
    <hkern u1="f" u2="&#xe0;" k="250" />
    <hkern u1="f" u2="&#xd7;" k="250" />
    <hkern u1="f" u2="z" k="225" />
    <hkern u1="f" u2="y" k="150" />
    <hkern u1="f" u2="x" k="225" />
    <hkern u1="f" u2="w" k="150" />
    <hkern u1="f" u2="v" k="150" />
    <hkern u1="f" u2="u" k="125" />
    <hkern u1="f" u2="t" k="125" />
    <hkern u1="f" u2="s" k="225" />
    <hkern u1="f" u2="r" k="125" />
    <hkern u1="f" u2="q" k="250" />
    <hkern u1="f" u2="p" k="125" />
    <hkern u1="f" u2="o" k="250" />
    <hkern u1="f" u2="n" k="125" />
    <hkern u1="f" u2="m" k="125" />
    <hkern u1="f" u2="l" k="50" />
    <hkern u1="f" u2="k" k="50" />
    <hkern u1="f" u2="j" k="187" />
    <hkern u1="f" u2="i" k="75" />
    <hkern u1="f" u2="h" k="50" />
    <hkern u1="f" u2="g" k="225" />
    <hkern u1="f" u2="f" k="175" />
    <hkern u1="f" u2="e" k="250" />
    <hkern u1="f" u2="d" k="225" />
    <hkern u1="f" u2="c" k="250" />
    <hkern u1="f" u2="b" k="50" />
    <hkern u1="f" u2="a" k="250" />
    <hkern u1="f" u2="&#x3c;" k="250" />
    <hkern u1="f" u2="&#x34;" k="225" />
    <hkern u1="f" u2="&#x30;" k="75" />
    <hkern u1="f" u2="&#x2f;" k="375" />
    <hkern u1="f" u2="&#x2b;" k="187" />
    <hkern u1="f" u2="&#x26;" k="175" />
    <hkern u1="g" u2="&#xff;" k="100" />
    <hkern u1="g" u2="&#xfd;" k="100" />
    <hkern u1="g" u2="&#xf8;" k="100" />
    <hkern u1="g" u2="&#xf6;" k="100" />
    <hkern u1="g" u2="&#xf5;" k="100" />
    <hkern u1="g" u2="&#xf4;" k="100" />
    <hkern u1="g" u2="&#xf3;" k="100" />
    <hkern u1="g" u2="&#xf2;" k="100" />
    <hkern u1="g" u2="&#xf0;" k="75" />
    <hkern u1="g" u2="&#xeb;" k="75" />
    <hkern u1="g" u2="&#xea;" k="75" />
    <hkern u1="g" u2="&#xe9;" k="75" />
    <hkern u1="g" u2="&#xe8;" k="75" />
    <hkern u1="g" u2="&#xe7;" k="75" />
    <hkern u1="g" u2="&#xe5;" k="75" />
    <hkern u1="g" u2="&#xe4;" k="75" />
    <hkern u1="g" u2="&#xe3;" k="75" />
    <hkern u1="g" u2="&#xe2;" k="75" />
    <hkern u1="g" u2="&#xe1;" k="75" />
    <hkern u1="g" u2="&#xe0;" k="75" />
    <hkern u1="g" u2="y" k="100" />
    <hkern u1="g" u2="x" k="125" />
    <hkern u1="g" u2="w" k="125" />
    <hkern u1="g" u2="v" k="75" />
    <hkern u1="g" u2="q" k="75" />
    <hkern u1="g" u2="o" k="100" />
    <hkern u1="g" u2="j" k="75" />
    <hkern u1="g" u2="f" k="50" />
    <hkern u1="g" u2="e" k="75" />
    <hkern u1="g" u2="d" k="75" />
    <hkern u1="g" u2="c" k="75" />
    <hkern u1="g" u2="a" k="75" />
    <hkern u1="g" u2="\" k="250" />
    <hkern u1="g" u2="&#x3f;" k="150" />
    <hkern u1="h" u2="&#xff;" k="150" />
    <hkern u1="h" u2="&#xfd;" k="150" />
    <hkern u1="h" u2="y" k="150" />
    <hkern u1="h" u2="x" k="50" />
    <hkern u1="h" u2="w" k="150" />
    <hkern u1="h" u2="v" k="150" />
    <hkern u1="h" u2="t" k="25" />
    <hkern u1="h" u2="j" k="100" />
    <hkern u1="h" u2="f" k="50" />
    <hkern u1="h" u2="\" k="400" />
    <hkern u1="h" u2="&#x3f;" k="175" />
    <hkern u1="i" u2="&#xff;" k="75" />
    <hkern u1="i" u2="&#xfd;" k="75" />
    <hkern u1="i" u2="y" k="75" />
    <hkern u1="i" u2="x" k="75" />
    <hkern u1="i" u2="w" k="75" />
    <hkern u1="i" u2="v" k="75" />
    <hkern u1="i" u2="t" k="50" />
    <hkern u1="i" u2="j" k="75" />
    <hkern u1="i" u2="&#x3f;" k="125" />
    <hkern u1="j" u2="&#xff;" k="50" />
    <hkern u1="j" u2="&#xfd;" k="50" />
    <hkern u1="j" u2="&#xfc;" k="-25" />
    <hkern u1="j" u2="&#xfb;" k="-25" />
    <hkern u1="j" u2="&#xfa;" k="-25" />
    <hkern u1="j" u2="&#xf9;" k="-25" />
    <hkern u1="j" u2="&#xf8;" k="-50" />
    <hkern u1="j" u2="&#xf6;" k="-50" />
    <hkern u1="j" u2="&#xf5;" k="-50" />
    <hkern u1="j" u2="&#xf4;" k="-50" />
    <hkern u1="j" u2="&#xf3;" k="-50" />
    <hkern u1="j" u2="&#xf2;" k="-50" />
    <hkern u1="j" u2="&#xf1;" k="-25" />
    <hkern u1="j" u2="&#xf0;" k="-50" />
    <hkern u1="j" u2="&#xef;" k="-25" />
    <hkern u1="j" u2="&#xee;" k="-25" />
    <hkern u1="j" u2="&#xed;" k="-25" />
    <hkern u1="j" u2="&#xec;" k="-25" />
    <hkern u1="j" u2="&#xeb;" k="-50" />
    <hkern u1="j" u2="&#xea;" k="-50" />
    <hkern u1="j" u2="&#xe9;" k="-50" />
    <hkern u1="j" u2="&#xe8;" k="-50" />
    <hkern u1="j" u2="&#xe7;" k="-50" />
    <hkern u1="j" u2="&#xe5;" k="-50" />
    <hkern u1="j" u2="&#xe4;" k="-50" />
    <hkern u1="j" u2="&#xe3;" k="-50" />
    <hkern u1="j" u2="&#xe2;" k="-50" />
    <hkern u1="j" u2="&#xe1;" k="-50" />
    <hkern u1="j" u2="&#xe0;" k="-50" />
    <hkern u1="j" u2="y" k="50" />
    <hkern u1="j" u2="x" k="50" />
    <hkern u1="j" u2="w" k="50" />
    <hkern u1="j" u2="v" k="50" />
    <hkern u1="j" u2="u" k="-25" />
    <hkern u1="j" u2="t" k="-25" />
    <hkern u1="j" u2="s" k="-25" />
    <hkern u1="j" u2="r" k="-25" />
    <hkern u1="j" u2="q" k="-50" />
    <hkern u1="j" u2="p" k="-25" />
    <hkern u1="j" u2="o" k="-50" />
    <hkern u1="j" u2="n" k="-25" />
    <hkern u1="j" u2="m" k="-25" />
    <hkern u1="j" u2="l" k="-25" />
    <hkern u1="j" u2="k" k="-25" />
    <hkern u1="j" u2="j" k="25" />
    <hkern u1="j" u2="i" k="-25" />
    <hkern u1="j" u2="h" k="-25" />
    <hkern u1="j" u2="g" k="-50" />
    <hkern u1="j" u2="e" k="-50" />
    <hkern u1="j" u2="d" k="-50" />
    <hkern u1="j" u2="c" k="-50" />
    <hkern u1="j" u2="b" k="-25" />
    <hkern u1="j" u2="a" k="-50" />
    <hkern u1="j" u2="&#x3f;" k="125" />
    <hkern u1="k" u2="&#xff;" k="75" />
    <hkern u1="k" u2="&#xfd;" k="75" />
    <hkern u1="k" u2="&#xfc;" k="75" />
    <hkern u1="k" u2="&#xfb;" k="75" />
    <hkern u1="k" u2="&#xfa;" k="75" />
    <hkern u1="k" u2="&#xf9;" k="75" />
    <hkern u1="k" u2="&#xf8;" k="175" />
    <hkern u1="k" u2="&#xf6;" k="175" />
    <hkern u1="k" u2="&#xf5;" k="175" />
    <hkern u1="k" u2="&#xf4;" k="175" />
    <hkern u1="k" u2="&#xf3;" k="175" />
    <hkern u1="k" u2="&#xf2;" k="175" />
    <hkern u1="k" u2="&#xf1;" k="50" />
    <hkern u1="k" u2="&#xf0;" k="175" />
    <hkern u1="k" u2="&#xef;" k="50" />
    <hkern u1="k" u2="&#xee;" k="50" />
    <hkern u1="k" u2="&#xed;" k="50" />
    <hkern u1="k" u2="&#xec;" k="50" />
    <hkern u1="k" u2="&#xeb;" k="175" />
    <hkern u1="k" u2="&#xea;" k="175" />
    <hkern u1="k" u2="&#xe9;" k="175" />
    <hkern u1="k" u2="&#xe8;" k="175" />
    <hkern u1="k" u2="&#xe7;" k="175" />
    <hkern u1="k" u2="&#xe5;" k="150" />
    <hkern u1="k" u2="&#xe4;" k="150" />
    <hkern u1="k" u2="&#xe3;" k="150" />
    <hkern u1="k" u2="&#xe2;" k="150" />
    <hkern u1="k" u2="&#xe1;" k="150" />
    <hkern u1="k" u2="&#xe0;" k="150" />
    <hkern u1="k" u2="z" k="50" />
    <hkern u1="k" u2="y" k="75" />
    <hkern u1="k" u2="x" k="75" />
    <hkern u1="k" u2="w" k="75" />
    <hkern u1="k" u2="v" k="75" />
    <hkern u1="k" u2="u" k="75" />
    <hkern u1="k" u2="t" k="75" />
    <hkern u1="k" u2="s" k="75" />
    <hkern u1="k" u2="r" k="50" />
    <hkern u1="k" u2="q" k="175" />
    <hkern u1="k" u2="p" k="50" />
    <hkern u1="k" u2="o" k="175" />
    <hkern u1="k" u2="n" k="50" />
    <hkern u1="k" u2="m" k="50" />
    <hkern u1="k" u2="k" k="50" />
    <hkern u1="k" u2="j" k="125" />
    <hkern u1="k" u2="i" k="50" />
    <hkern u1="k" u2="h" k="50" />
    <hkern u1="k" u2="g" k="125" />
    <hkern u1="k" u2="f" k="75" />
    <hkern u1="k" u2="e" k="175" />
    <hkern u1="k" u2="d" k="175" />
    <hkern u1="k" u2="c" k="175" />
    <hkern u1="k" u2="b" k="50" />
    <hkern u1="k" u2="a" k="150" />
    <hkern u1="k" u2="\" k="350" />
    <hkern u1="k" u2="&#x40;" k="100" />
    <hkern u1="k" u2="&#x3f;" k="175" />
    <hkern u1="k" u2="&#x3c;" k="150" />
    <hkern u1="k" u2="&#x38;" k="150" />
    <hkern u1="k" u2="&#x37;" k="150" />
    <hkern u1="k" u2="&#x36;" k="150" />
    <hkern u1="k" u2="&#x35;" k="175" />
    <hkern u1="k" u2="&#x34;" k="275" />
    <hkern u1="k" u2="&#x33;" k="175" />
    <hkern u1="k" u2="&#x2f;" k="125" />
    <hkern u1="k" u2="&#x26;" k="175" />
    <hkern u1="k" u2="&#x24;" k="125" />
    <hkern u1="l" u2="&#xff;" k="50" />
    <hkern u1="l" u2="&#xfd;" k="50" />
    <hkern u1="l" u2="&#xfc;" k="25" />
    <hkern u1="l" u2="&#xfb;" k="25" />
    <hkern u1="l" u2="&#xfa;" k="25" />
    <hkern u1="l" u2="&#xf9;" k="25" />
    <hkern u1="l" u2="z" k="50" />
    <hkern u1="l" u2="y" k="50" />
    <hkern u1="l" u2="x" k="50" />
    <hkern u1="l" u2="w" k="50" />
    <hkern u1="l" u2="v" k="50" />
    <hkern u1="l" u2="u" k="25" />
    <hkern u1="l" u2="t" k="50" />
    <hkern u1="l" u2="j" k="100" />
    <hkern u1="m" u2="&#xff;" k="150" />
    <hkern u1="m" u2="&#xfd;" k="150" />
    <hkern u1="m" u2="z" k="50" />
    <hkern u1="m" u2="y" k="150" />
    <hkern u1="m" u2="x" k="50" />
    <hkern u1="m" u2="w" k="125" />
    <hkern u1="m" u2="v" k="125" />
    <hkern u1="m" u2="t" k="50" />
    <hkern u1="m" u2="j" k="100" />
    <hkern u1="m" u2="\" k="400" />
    <hkern u1="m" u2="&#x3f;" k="175" />
    <hkern u1="n" u2="&#xff;" k="175" />
    <hkern u1="n" u2="&#xfd;" k="175" />
    <hkern u1="n" u2="&#xfc;" k="25" />
    <hkern u1="n" u2="&#xfb;" k="25" />
    <hkern u1="n" u2="&#xfa;" k="25" />
    <hkern u1="n" u2="&#xf9;" k="25" />
    <hkern u1="n" u2="y" k="175" />
    <hkern u1="n" u2="x" k="75" />
    <hkern u1="n" u2="w" k="175" />
    <hkern u1="n" u2="v" k="175" />
    <hkern u1="n" u2="u" k="25" />
    <hkern u1="n" u2="t" k="50" />
    <hkern u1="n" u2="j" k="100" />
    <hkern u1="n" u2="\" k="400" />
    <hkern u1="n" u2="&#x3f;" k="175" />
    <hkern u1="o" u2="&#xff;" k="150" />
    <hkern u1="o" u2="&#xfd;" k="150" />
    <hkern u1="o" u2="&#xf8;" k="-25" />
    <hkern u1="o" u2="&#xf6;" k="-25" />
    <hkern u1="o" u2="&#xf5;" k="-25" />
    <hkern u1="o" u2="&#xf4;" k="-25" />
    <hkern u1="o" u2="&#xf3;" k="-25" />
    <hkern u1="o" u2="&#xf2;" k="-25" />
    <hkern u1="o" u2="&#xf0;" k="-25" />
    <hkern u1="o" u2="&#xeb;" k="-25" />
    <hkern u1="o" u2="&#xea;" k="-25" />
    <hkern u1="o" u2="&#xe9;" k="-25" />
    <hkern u1="o" u2="&#xe8;" k="-25" />
    <hkern u1="o" u2="&#xe7;" k="-25" />
    <hkern u1="o" u2="z" k="100" />
    <hkern u1="o" u2="y" k="150" />
    <hkern u1="o" u2="x" k="150" />
    <hkern u1="o" u2="w" k="150" />
    <hkern u1="o" u2="v" k="150" />
    <hkern u1="o" u2="q" k="-25" />
    <hkern u1="o" u2="o" k="-25" />
    <hkern u1="o" u2="j" k="100" />
    <hkern u1="o" u2="e" k="-25" />
    <hkern u1="o" u2="d" k="-25" />
    <hkern u1="o" u2="c" k="-25" />
    <hkern u1="o" u2="\" k="425" />
    <hkern u1="o" u2="&#x3f;" k="175" />
    <hkern u1="o" u2="&#x37;" k="125" />
    <hkern u1="o" u2="&#x2f;" k="150" />
    <hkern u1="p" u2="&#xff;" k="150" />
    <hkern u1="p" u2="&#xfd;" k="150" />
    <hkern u1="p" u2="&#xf8;" k="-25" />
    <hkern u1="p" u2="&#xf6;" k="-25" />
    <hkern u1="p" u2="&#xf5;" k="-25" />
    <hkern u1="p" u2="&#xf4;" k="-25" />
    <hkern u1="p" u2="&#xf3;" k="-25" />
    <hkern u1="p" u2="&#xf2;" k="-25" />
    <hkern u1="p" u2="&#xf0;" k="-50" />
    <hkern u1="p" u2="&#xeb;" k="-25" />
    <hkern u1="p" u2="&#xea;" k="-25" />
    <hkern u1="p" u2="&#xe9;" k="-25" />
    <hkern u1="p" u2="&#xe8;" k="-25" />
    <hkern u1="p" u2="&#xe7;" k="-25" />
    <hkern u1="p" u2="z" k="100" />
    <hkern u1="p" u2="y" k="150" />
    <hkern u1="p" u2="x" k="150" />
    <hkern u1="p" u2="w" k="150" />
    <hkern u1="p" u2="v" k="150" />
    <hkern u1="p" u2="t" k="25" />
    <hkern u1="p" u2="s" k="25" />
    <hkern u1="p" u2="q" k="-25" />
    <hkern u1="p" u2="o" k="-25" />
    <hkern u1="p" u2="j" k="100" />
    <hkern u1="p" u2="e" k="-25" />
    <hkern u1="p" u2="d" k="-50" />
    <hkern u1="p" u2="c" k="-25" />
    <hkern u1="p" u2="\" k="425" />
    <hkern u1="p" u2="&#x3f;" k="175" />
    <hkern u1="p" u2="&#x37;" k="125" />
    <hkern u1="p" u2="&#x2f;" k="150" />
    <hkern u1="q" u2="&#xff;" k="75" />
    <hkern u1="q" u2="&#xfd;" k="75" />
    <hkern u1="q" u2="z" k="50" />
    <hkern u1="q" u2="y" k="75" />
    <hkern u1="q" u2="x" k="75" />
    <hkern u1="q" u2="w" k="75" />
    <hkern u1="q" u2="v" k="75" />
    <hkern u1="q" u2="t" k="25" />
    <hkern u1="q" u2="j" k="75" />
    <hkern u1="q" u2="\" k="275" />
    <hkern u1="q" u2="&#x3f;" k="175" />
    <hkern u1="r" u2="&#xff;" k="125" />
    <hkern u1="r" u2="&#xfd;" k="125" />
    <hkern u1="r" u2="&#xfc;" k="50" />
    <hkern u1="r" u2="&#xfb;" k="50" />
    <hkern u1="r" u2="&#xfa;" k="50" />
    <hkern u1="r" u2="&#xf9;" k="50" />
    <hkern u1="r" u2="&#xf8;" k="50" />
    <hkern u1="r" u2="&#xf6;" k="50" />
    <hkern u1="r" u2="&#xf5;" k="50" />
    <hkern u1="r" u2="&#xf4;" k="50" />
    <hkern u1="r" u2="&#xf3;" k="50" />
    <hkern u1="r" u2="&#xf2;" k="50" />
    <hkern u1="r" u2="&#xf1;" k="50" />
    <hkern u1="r" u2="&#xf0;" k="50" />
    <hkern u1="r" u2="&#xeb;" k="50" />
    <hkern u1="r" u2="&#xea;" k="50" />
    <hkern u1="r" u2="&#xe9;" k="50" />
    <hkern u1="r" u2="&#xe8;" k="50" />
    <hkern u1="r" u2="&#xe7;" k="50" />
    <hkern u1="r" u2="&#xe5;" k="100" />
    <hkern u1="r" u2="&#xe4;" k="100" />
    <hkern u1="r" u2="&#xe3;" k="100" />
    <hkern u1="r" u2="&#xe2;" k="100" />
    <hkern u1="r" u2="&#xe1;" k="100" />
    <hkern u1="r" u2="&#xe0;" k="100" />
    <hkern u1="r" u2="z" k="150" />
    <hkern u1="r" u2="y" k="125" />
    <hkern u1="r" u2="x" k="125" />
    <hkern u1="r" u2="w" k="125" />
    <hkern u1="r" u2="v" k="125" />
    <hkern u1="r" u2="u" k="50" />
    <hkern u1="r" u2="t" k="50" />
    <hkern u1="r" u2="s" k="25" />
    <hkern u1="r" u2="r" k="50" />
    <hkern u1="r" u2="q" k="50" />
    <hkern u1="r" u2="p" k="50" />
    <hkern u1="r" u2="o" k="50" />
    <hkern u1="r" u2="n" k="50" />
    <hkern u1="r" u2="m" k="50" />
    <hkern u1="r" u2="l" k="50" />
    <hkern u1="r" u2="k" k="50" />
    <hkern u1="r" u2="j" k="100" />
    <hkern u1="r" u2="h" k="50" />
    <hkern u1="r" u2="g" k="50" />
    <hkern u1="r" u2="f" k="50" />
    <hkern u1="r" u2="e" k="50" />
    <hkern u1="r" u2="d" k="50" />
    <hkern u1="r" u2="c" k="50" />
    <hkern u1="r" u2="b" k="50" />
    <hkern u1="r" u2="a" k="100" />
    <hkern u1="r" u2="\" k="325" />
    <hkern u1="r" u2="S" k="175" />
    <hkern u1="r" u2="&#x3f;" k="175" />
    <hkern u1="r" u2="&#x38;" k="75" />
    <hkern u1="r" u2="&#x37;" k="175" />
    <hkern u1="r" u2="&#x35;" k="200" />
    <hkern u1="r" u2="&#x34;" k="150" />
    <hkern u1="r" u2="&#x33;" k="200" />
    <hkern u1="r" u2="&#x32;" k="150" />
    <hkern u1="r" u2="&#x2f;" k="400" />
    <hkern u1="r" u2="&#x26;" k="75" />
    <hkern u1="s" u2="&#xff;" k="175" />
    <hkern u1="s" u2="&#xfd;" k="175" />
    <hkern u1="s" u2="z" k="50" />
    <hkern u1="s" u2="y" k="175" />
    <hkern u1="s" u2="x" k="150" />
    <hkern u1="s" u2="w" k="175" />
    <hkern u1="s" u2="v" k="175" />
    <hkern u1="s" u2="t" k="25" />
    <hkern u1="s" u2="j" k="100" />
    <hkern u1="s" u2="\" k="400" />
    <hkern u1="s" u2="T" k="275" />
    <hkern u1="s" u2="&#x3f;" k="175" />
    <hkern u1="t" u2="&#xff;" k="125" />
    <hkern u1="t" u2="&#xfd;" k="125" />
    <hkern u1="t" u2="&#xfc;" k="75" />
    <hkern u1="t" u2="&#xfb;" k="75" />
    <hkern u1="t" u2="&#xfa;" k="75" />
    <hkern u1="t" u2="&#xf9;" k="75" />
    <hkern u1="t" u2="&#xf8;" k="100" />
    <hkern u1="t" u2="&#xf6;" k="100" />
    <hkern u1="t" u2="&#xf5;" k="100" />
    <hkern u1="t" u2="&#xf4;" k="100" />
    <hkern u1="t" u2="&#xf3;" k="100" />
    <hkern u1="t" u2="&#xf2;" k="100" />
    <hkern u1="t" u2="&#xf0;" k="75" />
    <hkern u1="t" u2="&#xeb;" k="100" />
    <hkern u1="t" u2="&#xea;" k="100" />
    <hkern u1="t" u2="&#xe9;" k="100" />
    <hkern u1="t" u2="&#xe8;" k="100" />
    <hkern u1="t" u2="&#xe7;" k="100" />
    <hkern u1="t" u2="&#xe5;" k="75" />
    <hkern u1="t" u2="&#xe4;" k="75" />
    <hkern u1="t" u2="&#xe3;" k="75" />
    <hkern u1="t" u2="&#xe2;" k="75" />
    <hkern u1="t" u2="&#xe1;" k="75" />
    <hkern u1="t" u2="&#xe0;" k="75" />
    <hkern u1="t" u2="&#xdc;" k="75" />
    <hkern u1="t" u2="&#xdb;" k="75" />
    <hkern u1="t" u2="&#xda;" k="75" />
    <hkern u1="t" u2="&#xd9;" k="75" />
    <hkern u1="t" u2="z" k="75" />
    <hkern u1="t" u2="y" k="125" />
    <hkern u1="t" u2="x" k="50" />
    <hkern u1="t" u2="w" k="125" />
    <hkern u1="t" u2="v" k="125" />
    <hkern u1="t" u2="u" k="75" />
    <hkern u1="t" u2="t" k="50" />
    <hkern u1="t" u2="q" k="100" />
    <hkern u1="t" u2="o" k="100" />
    <hkern u1="t" u2="j" k="100" />
    <hkern u1="t" u2="g" k="50" />
    <hkern u1="t" u2="e" k="100" />
    <hkern u1="t" u2="d" k="75" />
    <hkern u1="t" u2="c" k="100" />
    <hkern u1="t" u2="a" k="75" />
    <hkern u1="t" u2="\" k="225" />
    <hkern u1="t" u2="U" k="75" />
    <hkern u1="t" u2="&#x3f;" k="175" />
    <hkern u1="t" u2="&#x38;" k="125" />
    <hkern u1="t" u2="&#x35;" k="125" />
    <hkern u1="t" u2="&#x34;" k="175" />
    <hkern u1="t" u2="&#x33;" k="125" />
    <hkern u1="t" u2="&#x26;" k="50" />
    <hkern u1="u" u2="&#xff;" k="100" />
    <hkern u1="u" u2="&#xfd;" k="100" />
    <hkern u1="u" u2="z" k="50" />
    <hkern u1="u" u2="y" k="100" />
    <hkern u1="u" u2="x" k="75" />
    <hkern u1="u" u2="w" k="100" />
    <hkern u1="u" u2="v" k="100" />
    <hkern u1="u" u2="j" k="100" />
    <hkern u1="u" u2="\" k="275" />
    <hkern u1="u" u2="V" k="325" />
    <hkern u1="u" u2="&#x3f;" k="175" />
    <hkern u1="v" u2="&#xff;" k="75" />
    <hkern u1="v" u2="&#xfd;" k="75" />
    <hkern u1="v" u2="&#xfc;" k="75" />
    <hkern u1="v" u2="&#xfb;" k="75" />
    <hkern u1="v" u2="&#xfa;" k="75" />
    <hkern u1="v" u2="&#xf9;" k="75" />
    <hkern u1="v" u2="&#xf8;" k="150" />
    <hkern u1="v" u2="&#xf6;" k="150" />
    <hkern u1="v" u2="&#xf5;" k="150" />
    <hkern u1="v" u2="&#xf4;" k="150" />
    <hkern u1="v" u2="&#xf3;" k="150" />
    <hkern u1="v" u2="&#xf2;" k="150" />
    <hkern u1="v" u2="&#xf1;" k="50" />
    <hkern u1="v" u2="&#xf0;" k="150" />
    <hkern u1="v" u2="&#xef;" k="50" />
    <hkern u1="v" u2="&#xee;" k="50" />
    <hkern u1="v" u2="&#xed;" k="50" />
    <hkern u1="v" u2="&#xec;" k="50" />
    <hkern u1="v" u2="&#xeb;" k="150" />
    <hkern u1="v" u2="&#xea;" k="150" />
    <hkern u1="v" u2="&#xe9;" k="150" />
    <hkern u1="v" u2="&#xe8;" k="150" />
    <hkern u1="v" u2="&#xe7;" k="150" />
    <hkern u1="v" u2="&#xe5;" k="150" />
    <hkern u1="v" u2="&#xe4;" k="150" />
    <hkern u1="v" u2="&#xe3;" k="150" />
    <hkern u1="v" u2="&#xe2;" k="150" />
    <hkern u1="v" u2="&#xe1;" k="150" />
    <hkern u1="v" u2="&#xe0;" k="150" />
    <hkern u1="v" u2="z" k="125" />
    <hkern u1="v" u2="y" k="75" />
    <hkern u1="v" u2="x" k="75" />
    <hkern u1="v" u2="w" k="75" />
    <hkern u1="v" u2="v" k="75" />
    <hkern u1="v" u2="u" k="75" />
    <hkern u1="v" u2="t" k="75" />
    <hkern u1="v" u2="s" k="125" />
    <hkern u1="v" u2="r" k="50" />
    <hkern u1="v" u2="q" k="150" />
    <hkern u1="v" u2="p" k="50" />
    <hkern u1="v" u2="o" k="150" />
    <hkern u1="v" u2="n" k="50" />
    <hkern u1="v" u2="m" k="50" />
    <hkern u1="v" u2="l" k="50" />
    <hkern u1="v" u2="k" k="50" />
    <hkern u1="v" u2="j" k="150" />
    <hkern u1="v" u2="i" k="50" />
    <hkern u1="v" u2="h" k="50" />
    <hkern u1="v" u2="g" k="125" />
    <hkern u1="v" u2="f" k="50" />
    <hkern u1="v" u2="e" k="150" />
    <hkern u1="v" u2="d" k="150" />
    <hkern u1="v" u2="c" k="150" />
    <hkern u1="v" u2="b" k="50" />
    <hkern u1="v" u2="a" k="150" />
    <hkern u1="v" u2="\" k="350" />
    <hkern u1="v" u2="W" k="250" />
    <hkern u1="v" u2="&#x3f;" k="175" />
    <hkern u1="v" u2="&#x38;" k="125" />
    <hkern u1="v" u2="&#x37;" k="275" />
    <hkern u1="v" u2="&#x35;" k="175" />
    <hkern u1="v" u2="&#x34;" k="225" />
    <hkern u1="v" u2="&#x33;" k="175" />
    <hkern u1="v" u2="&#x32;" k="225" />
    <hkern u1="v" u2="&#x2f;" k="375" />
    <hkern u1="v" u2="&#x26;" k="150" />
    <hkern u1="w" u2="&#xff;" k="75" />
    <hkern u1="w" u2="&#xfd;" k="75" />
    <hkern u1="w" u2="&#xfc;" k="75" />
    <hkern u1="w" u2="&#xfb;" k="75" />
    <hkern u1="w" u2="&#xfa;" k="75" />
    <hkern u1="w" u2="&#xf9;" k="75" />
    <hkern u1="w" u2="&#xf8;" k="150" />
    <hkern u1="w" u2="&#xf6;" k="150" />
    <hkern u1="w" u2="&#xf5;" k="150" />
    <hkern u1="w" u2="&#xf4;" k="150" />
    <hkern u1="w" u2="&#xf3;" k="150" />
    <hkern u1="w" u2="&#xf2;" k="150" />
    <hkern u1="w" u2="&#xf0;" k="150" />
    <hkern u1="w" u2="&#xef;" k="50" />
    <hkern u1="w" u2="&#xee;" k="50" />
    <hkern u1="w" u2="&#xed;" k="50" />
    <hkern u1="w" u2="&#xec;" k="50" />
    <hkern u1="w" u2="&#xeb;" k="150" />
    <hkern u1="w" u2="&#xea;" k="150" />
    <hkern u1="w" u2="&#xe9;" k="150" />
    <hkern u1="w" u2="&#xe8;" k="150" />
    <hkern u1="w" u2="&#xe7;" k="150" />
    <hkern u1="w" u2="&#xe5;" k="150" />
    <hkern u1="w" u2="&#xe4;" k="150" />
    <hkern u1="w" u2="&#xe3;" k="150" />
    <hkern u1="w" u2="&#xe2;" k="150" />
    <hkern u1="w" u2="&#xe1;" k="150" />
    <hkern u1="w" u2="&#xe0;" k="150" />
    <hkern u1="w" u2="z" k="125" />
    <hkern u1="w" u2="y" k="75" />
    <hkern u1="w" u2="x" k="75" />
    <hkern u1="w" u2="w" k="75" />
    <hkern u1="w" u2="v" k="75" />
    <hkern u1="w" u2="u" k="75" />
    <hkern u1="w" u2="t" k="75" />
    <hkern u1="w" u2="s" k="125" />
    <hkern u1="w" u2="r" k="50" />
    <hkern u1="w" u2="q" k="150" />
    <hkern u1="w" u2="p" k="50" />
    <hkern u1="w" u2="o" k="150" />
    <hkern u1="w" u2="m" k="50" />
    <hkern u1="w" u2="l" k="50" />
    <hkern u1="w" u2="k" k="50" />
    <hkern u1="w" u2="j" k="150" />
    <hkern u1="w" u2="i" k="50" />
    <hkern u1="w" u2="h" k="50" />
    <hkern u1="w" u2="g" k="125" />
    <hkern u1="w" u2="f" k="50" />
    <hkern u1="w" u2="e" k="150" />
    <hkern u1="w" u2="d" k="150" />
    <hkern u1="w" u2="c" k="150" />
    <hkern u1="w" u2="b" k="50" />
    <hkern u1="w" u2="a" k="150" />
    <hkern u1="w" u2="\" k="350" />
    <hkern u1="w" u2="X" k="325" />
    <hkern u1="w" u2="&#x3f;" k="175" />
    <hkern u1="w" u2="&#x37;" k="150" />
    <hkern u1="w" u2="&#x35;" k="125" />
    <hkern u1="w" u2="&#x34;" k="125" />
    <hkern u1="w" u2="&#x33;" k="125" />
    <hkern u1="w" u2="&#x32;" k="225" />
    <hkern u1="w" u2="&#x2f;" k="375" />
    <hkern u1="w" u2="&#x26;" k="150" />
    <hkern u1="x" u2="&#xff;" k="75" />
    <hkern u1="x" u2="&#xfd;" k="75" />
    <hkern u1="x" u2="&#xfc;" k="100" />
    <hkern u1="x" u2="&#xfb;" k="100" />
    <hkern u1="x" u2="&#xfa;" k="100" />
    <hkern u1="x" u2="&#xf9;" k="100" />
    <hkern u1="x" u2="&#xf8;" k="150" />
    <hkern u1="x" u2="&#xf6;" k="150" />
    <hkern u1="x" u2="&#xf5;" k="150" />
    <hkern u1="x" u2="&#xf4;" k="150" />
    <hkern u1="x" u2="&#xf3;" k="150" />
    <hkern u1="x" u2="&#xf2;" k="150" />
    <hkern u1="x" u2="&#xf0;" k="150" />
    <hkern u1="x" u2="&#xef;" k="50" />
    <hkern u1="x" u2="&#xee;" k="50" />
    <hkern u1="x" u2="&#xed;" k="50" />
    <hkern u1="x" u2="&#xec;" k="50" />
    <hkern u1="x" u2="&#xeb;" k="150" />
    <hkern u1="x" u2="&#xea;" k="150" />
    <hkern u1="x" u2="&#xe9;" k="150" />
    <hkern u1="x" u2="&#xe8;" k="150" />
    <hkern u1="x" u2="&#xe7;" k="150" />
    <hkern u1="x" u2="&#xe5;" k="100" />
    <hkern u1="x" u2="&#xe4;" k="100" />
    <hkern u1="x" u2="&#xe3;" k="100" />
    <hkern u1="x" u2="&#xe2;" k="100" />
    <hkern u1="x" u2="&#xe1;" k="100" />
    <hkern u1="x" u2="&#xe0;" k="100" />
    <hkern u1="x" u2="&#x178;" k="400" />
    <hkern u1="x" u2="&#xdd;" k="400" />
    <hkern u1="x" u2="z" k="50" />
    <hkern u1="x" u2="y" k="75" />
    <hkern u1="x" u2="x" k="75" />
    <hkern u1="x" u2="w" k="75" />
    <hkern u1="x" u2="v" k="75" />
    <hkern u1="x" u2="u" k="100" />
    <hkern u1="x" u2="t" k="100" />
    <hkern u1="x" u2="s" k="75" />
    <hkern u1="x" u2="r" k="50" />
    <hkern u1="x" u2="q" k="150" />
    <hkern u1="x" u2="p" k="50" />
    <hkern u1="x" u2="o" k="150" />
    <hkern u1="x" u2="m" k="50" />
    <hkern u1="x" u2="l" k="50" />
    <hkern u1="x" u2="k" k="50" />
    <hkern u1="x" u2="j" k="150" />
    <hkern u1="x" u2="i" k="50" />
    <hkern u1="x" u2="g" k="75" />
    <hkern u1="x" u2="f" k="50" />
    <hkern u1="x" u2="e" k="150" />
    <hkern u1="x" u2="d" k="150" />
    <hkern u1="x" u2="c" k="150" />
    <hkern u1="x" u2="a" k="100" />
    <hkern u1="x" u2="\" k="375" />
    <hkern u1="x" u2="Y" k="400" />
    <hkern u1="x" u2="&#x3f;" k="175" />
    <hkern u1="x" u2="&#x38;" k="125" />
    <hkern u1="x" u2="&#x36;" k="125" />
    <hkern u1="x" u2="&#x35;" k="125" />
    <hkern u1="x" u2="&#x34;" k="175" />
    <hkern u1="x" u2="&#x33;" k="125" />
    <hkern u1="x" u2="&#x2f;" k="75" />
    <hkern u1="x" u2="&#x26;" k="150" />
    <hkern u1="y" u2="&#xff;" k="75" />
    <hkern u1="y" u2="&#xfd;" k="75" />
    <hkern u1="y" u2="&#xfc;" k="75" />
    <hkern u1="y" u2="&#xfb;" k="75" />
    <hkern u1="y" u2="&#xfa;" k="75" />
    <hkern u1="y" u2="&#xf9;" k="75" />
    <hkern u1="y" u2="&#xf8;" k="150" />
    <hkern u1="y" u2="&#xf6;" k="150" />
    <hkern u1="y" u2="&#xf5;" k="150" />
    <hkern u1="y" u2="&#xf4;" k="150" />
    <hkern u1="y" u2="&#xf3;" k="150" />
    <hkern u1="y" u2="&#xf2;" k="150" />
    <hkern u1="y" u2="&#xf1;" k="50" />
    <hkern u1="y" u2="&#xf0;" k="150" />
    <hkern u1="y" u2="&#xef;" k="50" />
    <hkern u1="y" u2="&#xee;" k="50" />
    <hkern u1="y" u2="&#xed;" k="50" />
    <hkern u1="y" u2="&#xec;" k="50" />
    <hkern u1="y" u2="&#xeb;" k="150" />
    <hkern u1="y" u2="&#xea;" k="150" />
    <hkern u1="y" u2="&#xe9;" k="150" />
    <hkern u1="y" u2="&#xe8;" k="150" />
    <hkern u1="y" u2="&#xe7;" k="150" />
    <hkern u1="y" u2="&#xe5;" k="200" />
    <hkern u1="y" u2="&#xe4;" k="200" />
    <hkern u1="y" u2="&#xe3;" k="200" />
    <hkern u1="y" u2="&#xe2;" k="200" />
    <hkern u1="y" u2="&#xe1;" k="200" />
    <hkern u1="y" u2="&#xe0;" k="200" />
    <hkern u1="y" u2="z" k="150" />
    <hkern u1="y" u2="y" k="75" />
    <hkern u1="y" u2="x" k="75" />
    <hkern u1="y" u2="w" k="75" />
    <hkern u1="y" u2="v" k="75" />
    <hkern u1="y" u2="u" k="75" />
    <hkern u1="y" u2="t" k="75" />
    <hkern u1="y" u2="s" k="125" />
    <hkern u1="y" u2="r" k="50" />
    <hkern u1="y" u2="q" k="150" />
    <hkern u1="y" u2="p" k="50" />
    <hkern u1="y" u2="o" k="150" />
    <hkern u1="y" u2="n" k="50" />
    <hkern u1="y" u2="m" k="50" />
    <hkern u1="y" u2="l" k="75" />
    <hkern u1="y" u2="k" k="50" />
    <hkern u1="y" u2="j" k="150" />
    <hkern u1="y" u2="i" k="50" />
    <hkern u1="y" u2="h" k="50" />
    <hkern u1="y" u2="g" k="125" />
    <hkern u1="y" u2="f" k="50" />
    <hkern u1="y" u2="e" k="150" />
    <hkern u1="y" u2="d" k="150" />
    <hkern u1="y" u2="c" k="150" />
    <hkern u1="y" u2="b" k="50" />
    <hkern u1="y" u2="a" k="200" />
    <hkern u1="y" u2="\" k="350" />
    <hkern u1="y" u2="Z" k="325" />
    <hkern u1="y" u2="&#x3f;" k="175" />
    <hkern u1="y" u2="&#x38;" k="100" />
    <hkern u1="y" u2="&#x37;" k="300" />
    <hkern u1="y" u2="&#x36;" k="75" />
    <hkern u1="y" u2="&#x35;" k="175" />
    <hkern u1="y" u2="&#x34;" k="200" />
    <hkern u1="y" u2="&#x33;" k="175" />
    <hkern u1="y" u2="&#x32;" k="200" />
    <hkern u1="y" u2="&#x2f;" k="500" />
    <hkern u1="y" u2="&#x26;" k="150" />
    <hkern u1="z" u2="&#xff;" k="50" />
    <hkern u1="z" u2="&#xfd;" k="50" />
    <hkern u1="z" u2="&#xfc;" k="25" />
    <hkern u1="z" u2="&#xfb;" k="25" />
    <hkern u1="z" u2="&#xfa;" k="25" />
    <hkern u1="z" u2="&#xf9;" k="25" />
    <hkern u1="z" u2="&#xf8;" k="75" />
    <hkern u1="z" u2="&#xf6;" k="75" />
    <hkern u1="z" u2="&#xf5;" k="75" />
    <hkern u1="z" u2="&#xf4;" k="75" />
    <hkern u1="z" u2="&#xf3;" k="75" />
    <hkern u1="z" u2="&#xf2;" k="75" />
    <hkern u1="z" u2="&#xf0;" k="50" />
    <hkern u1="z" u2="&#xeb;" k="50" />
    <hkern u1="z" u2="&#xea;" k="50" />
    <hkern u1="z" u2="&#xe9;" k="50" />
    <hkern u1="z" u2="&#xe8;" k="50" />
    <hkern u1="z" u2="&#xe7;" k="50" />
    <hkern u1="z" u2="z" k="50" />
    <hkern u1="z" u2="y" k="50" />
    <hkern u1="z" u2="x" k="50" />
    <hkern u1="z" u2="w" k="50" />
    <hkern u1="z" u2="v" k="50" />
    <hkern u1="z" u2="u" k="25" />
    <hkern u1="z" u2="t" k="50" />
    <hkern u1="z" u2="q" k="75" />
    <hkern u1="z" u2="p" k="25" />
    <hkern u1="z" u2="o" k="75" />
    <hkern u1="z" u2="j" k="75" />
    <hkern u1="z" u2="f" k="50" />
    <hkern u1="z" u2="e" k="50" />
    <hkern u1="z" u2="d" k="50" />
    <hkern u1="z" u2="c" k="50" />
    <hkern u1="z" u2="\" k="275" />
    <hkern u1="z" u2="&#x3f;" k="175" />
    <hkern u1="z" u2="&#x34;" k="200" />
    <hkern u1="z" u2="&#x2f;" k="100" />
    <hkern u1="&#xc0;" u2="&#xfc;" k="125" />
    <hkern u1="&#xc0;" u2="&#xfb;" k="125" />
    <hkern u1="&#xc0;" u2="&#xfa;" k="125" />
    <hkern u1="&#xc0;" u2="&#xf9;" k="125" />
    <hkern u1="&#xc0;" u2="&#xf8;" k="125" />
    <hkern u1="&#xc0;" u2="&#xf6;" k="125" />
    <hkern u1="&#xc0;" u2="&#xf5;" k="125" />
    <hkern u1="&#xc0;" u2="&#xf4;" k="125" />
    <hkern u1="&#xc0;" u2="&#xf3;" k="125" />
    <hkern u1="&#xc0;" u2="&#xf2;" k="125" />
    <hkern u1="&#xc0;" u2="&#xf0;" k="125" />
    <hkern u1="&#xc0;" u2="&#xeb;" k="125" />
    <hkern u1="&#xc0;" u2="&#xea;" k="125" />
    <hkern u1="&#xc0;" u2="&#xe9;" k="125" />
    <hkern u1="&#xc0;" u2="&#xe8;" k="125" />
    <hkern u1="&#xc0;" u2="&#xe7;" k="125" />
    <hkern u1="&#xc0;" u2="&#xe5;" k="75" />
    <hkern u1="&#xc0;" u2="&#xe4;" k="75" />
    <hkern u1="&#xc0;" u2="&#xe3;" k="75" />
    <hkern u1="&#xc0;" u2="&#xe2;" k="75" />
    <hkern u1="&#xc0;" u2="&#xe1;" k="75" />
    <hkern u1="&#xc0;" u2="&#xe0;" k="75" />
    <hkern u1="&#xc0;" u2="&#x178;" k="600" />
    <hkern u1="&#xc0;" u2="&#xdd;" k="600" />
    <hkern u1="&#xc0;" u2="&#xdc;" k="175" />
    <hkern u1="&#xc0;" u2="&#xdb;" k="175" />
    <hkern u1="&#xc0;" u2="&#xda;" k="175" />
    <hkern u1="&#xc0;" u2="&#xd9;" k="175" />
    <hkern u1="&#xc0;" u2="&#xd8;" k="225" />
    <hkern u1="&#xc0;" u2="&#xd6;" k="225" />
    <hkern u1="&#xc0;" u2="&#xd5;" k="225" />
    <hkern u1="&#xc0;" u2="&#xd4;" k="225" />
    <hkern u1="&#xc0;" u2="&#xd3;" k="225" />
    <hkern u1="&#xc0;" u2="&#xd2;" k="225" />
    <hkern u1="&#xc0;" u2="&#xd1;" k="50" />
    <hkern u1="&#xc0;" u2="&#xcf;" k="50" />
    <hkern u1="&#xc0;" u2="&#xce;" k="50" />
    <hkern u1="&#xc0;" u2="&#xcd;" k="50" />
    <hkern u1="&#xc0;" u2="&#xcc;" k="50" />
    <hkern u1="&#xc0;" u2="&#xc7;" k="225" />
    <hkern u1="&#xc0;" u2="&#xc5;" k="75" />
    <hkern u1="&#xc0;" u2="&#xc4;" k="75" />
    <hkern u1="&#xc0;" u2="&#xc3;" k="75" />
    <hkern u1="&#xc0;" u2="&#xc2;" k="75" />
    <hkern u1="&#xc0;" u2="&#xc1;" k="75" />
    <hkern u1="&#xc0;" u2="&#xc0;" k="75" />
    <hkern u1="&#xc0;" u2="v" k="375" />
    <hkern u1="&#xc0;" u2="u" k="125" />
    <hkern u1="&#xc0;" u2="t" k="125" />
    <hkern u1="&#xc0;" u2="q" k="125" />
    <hkern u1="&#xc0;" u2="o" k="125" />
    <hkern u1="&#xc0;" u2="j" k="62" />
    <hkern u1="&#xc0;" u2="g" k="50" />
    <hkern u1="&#xc0;" u2="f" k="125" />
    <hkern u1="&#xc0;" u2="e" k="125" />
    <hkern u1="&#xc0;" u2="d" k="125" />
    <hkern u1="&#xc0;" u2="c" k="125" />
    <hkern u1="&#xc0;" u2="a" k="75" />
    <hkern u1="&#xc0;" u2="^" k="375" />
    <hkern u1="&#xc0;" u2="\" k="700" />
    <hkern u1="&#xc0;" u2="Z" k="50" />
    <hkern u1="&#xc0;" u2="Y" k="600" />
    <hkern u1="&#xc0;" u2="X" k="75" />
    <hkern u1="&#xc0;" u2="W" k="475" />
    <hkern u1="&#xc0;" u2="V" k="600" />
    <hkern u1="&#xc0;" u2="U" k="175" />
    <hkern u1="&#xc0;" u2="T" k="475" />
    <hkern u1="&#xc0;" u2="S" k="75" />
    <hkern u1="&#xc0;" u2="R" k="50" />
    <hkern u1="&#xc0;" u2="Q" k="225" />
    <hkern u1="&#xc0;" u2="P" k="50" />
    <hkern u1="&#xc0;" u2="O" k="225" />
    <hkern u1="&#xc0;" u2="N" k="50" />
    <hkern u1="&#xc0;" u2="M" k="50" />
    <hkern u1="&#xc0;" u2="L" k="50" />
    <hkern u1="&#xc0;" u2="K" k="50" />
    <hkern u1="&#xc0;" u2="J" k="225" />
    <hkern u1="&#xc0;" u2="I" k="50" />
    <hkern u1="&#xc0;" u2="H" k="50" />
    <hkern u1="&#xc0;" u2="G" k="225" />
    <hkern u1="&#xc0;" u2="C" k="225" />
    <hkern u1="&#xc0;" u2="B" k="50" />
    <hkern u1="&#xc0;" u2="A" k="75" />
    <hkern u1="&#xc0;" u2="&#x39;" k="350" />
    <hkern u1="&#xc0;" u2="&#x38;" k="100" />
    <hkern u1="&#xc0;" u2="&#x36;" k="100" />
    <hkern u1="&#xc0;" u2="&#x35;" k="100" />
    <hkern u1="&#xc0;" u2="&#x34;" k="150" />
    <hkern u1="&#xc0;" u2="&#x33;" k="100" />
    <hkern u1="&#xc0;" u2="&#x31;" k="75" />
    <hkern u1="&#xc0;" u2="&#x30;" k="150" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="300" />
    <hkern u1="&#xc1;" u2="&#xfc;" k="125" />
    <hkern u1="&#xc1;" u2="&#xfb;" k="125" />
    <hkern u1="&#xc1;" u2="&#xfa;" k="125" />
    <hkern u1="&#xc1;" u2="&#xf9;" k="125" />
    <hkern u1="&#xc1;" u2="&#xf8;" k="125" />
    <hkern u1="&#xc1;" u2="&#xf6;" k="125" />
    <hkern u1="&#xc1;" u2="&#xf5;" k="125" />
    <hkern u1="&#xc1;" u2="&#xf4;" k="125" />
    <hkern u1="&#xc1;" u2="&#xf3;" k="125" />
    <hkern u1="&#xc1;" u2="&#xf2;" k="125" />
    <hkern u1="&#xc1;" u2="&#xf0;" k="125" />
    <hkern u1="&#xc1;" u2="&#xeb;" k="125" />
    <hkern u1="&#xc1;" u2="&#xea;" k="125" />
    <hkern u1="&#xc1;" u2="&#xe9;" k="125" />
    <hkern u1="&#xc1;" u2="&#xe8;" k="125" />
    <hkern u1="&#xc1;" u2="&#xe7;" k="125" />
    <hkern u1="&#xc1;" u2="&#xe5;" k="75" />
    <hkern u1="&#xc1;" u2="&#xe4;" k="75" />
    <hkern u1="&#xc1;" u2="&#xe3;" k="75" />
    <hkern u1="&#xc1;" u2="&#xe2;" k="75" />
    <hkern u1="&#xc1;" u2="&#xe1;" k="75" />
    <hkern u1="&#xc1;" u2="&#xe0;" k="75" />
    <hkern u1="&#xc1;" u2="&#x178;" k="600" />
    <hkern u1="&#xc1;" u2="&#xdd;" k="600" />
    <hkern u1="&#xc1;" u2="&#xdc;" k="175" />
    <hkern u1="&#xc1;" u2="&#xdb;" k="175" />
    <hkern u1="&#xc1;" u2="&#xda;" k="175" />
    <hkern u1="&#xc1;" u2="&#xd9;" k="175" />
    <hkern u1="&#xc1;" u2="&#xd8;" k="225" />
    <hkern u1="&#xc1;" u2="&#xd6;" k="225" />
    <hkern u1="&#xc1;" u2="&#xd5;" k="225" />
    <hkern u1="&#xc1;" u2="&#xd4;" k="225" />
    <hkern u1="&#xc1;" u2="&#xd3;" k="225" />
    <hkern u1="&#xc1;" u2="&#xd2;" k="225" />
    <hkern u1="&#xc1;" u2="&#xd1;" k="50" />
    <hkern u1="&#xc1;" u2="&#xcf;" k="50" />
    <hkern u1="&#xc1;" u2="&#xce;" k="50" />
    <hkern u1="&#xc1;" u2="&#xcd;" k="50" />
    <hkern u1="&#xc1;" u2="&#xcc;" k="50" />
    <hkern u1="&#xc1;" u2="&#xc7;" k="225" />
    <hkern u1="&#xc1;" u2="&#xc5;" k="75" />
    <hkern u1="&#xc1;" u2="&#xc4;" k="75" />
    <hkern u1="&#xc1;" u2="&#xc3;" k="75" />
    <hkern u1="&#xc1;" u2="&#xc2;" k="75" />
    <hkern u1="&#xc1;" u2="&#xc1;" k="75" />
    <hkern u1="&#xc1;" u2="&#xc0;" k="75" />
    <hkern u1="&#xc1;" u2="v" k="375" />
    <hkern u1="&#xc1;" u2="u" k="125" />
    <hkern u1="&#xc1;" u2="t" k="125" />
    <hkern u1="&#xc1;" u2="q" k="125" />
    <hkern u1="&#xc1;" u2="o" k="125" />
    <hkern u1="&#xc1;" u2="j" k="62" />
    <hkern u1="&#xc1;" u2="g" k="50" />
    <hkern u1="&#xc1;" u2="f" k="125" />
    <hkern u1="&#xc1;" u2="e" k="125" />
    <hkern u1="&#xc1;" u2="d" k="125" />
    <hkern u1="&#xc1;" u2="c" k="125" />
    <hkern u1="&#xc1;" u2="a" k="75" />
    <hkern u1="&#xc1;" u2="^" k="375" />
    <hkern u1="&#xc1;" u2="\" k="700" />
    <hkern u1="&#xc1;" u2="Z" k="50" />
    <hkern u1="&#xc1;" u2="Y" k="600" />
    <hkern u1="&#xc1;" u2="X" k="75" />
    <hkern u1="&#xc1;" u2="W" k="475" />
    <hkern u1="&#xc1;" u2="V" k="600" />
    <hkern u1="&#xc1;" u2="U" k="175" />
    <hkern u1="&#xc1;" u2="T" k="475" />
    <hkern u1="&#xc1;" u2="S" k="75" />
    <hkern u1="&#xc1;" u2="R" k="50" />
    <hkern u1="&#xc1;" u2="Q" k="225" />
    <hkern u1="&#xc1;" u2="P" k="50" />
    <hkern u1="&#xc1;" u2="O" k="225" />
    <hkern u1="&#xc1;" u2="N" k="50" />
    <hkern u1="&#xc1;" u2="M" k="50" />
    <hkern u1="&#xc1;" u2="L" k="50" />
    <hkern u1="&#xc1;" u2="K" k="50" />
    <hkern u1="&#xc1;" u2="J" k="225" />
    <hkern u1="&#xc1;" u2="I" k="50" />
    <hkern u1="&#xc1;" u2="H" k="50" />
    <hkern u1="&#xc1;" u2="G" k="225" />
    <hkern u1="&#xc1;" u2="C" k="225" />
    <hkern u1="&#xc1;" u2="B" k="50" />
    <hkern u1="&#xc1;" u2="A" k="75" />
    <hkern u1="&#xc1;" u2="&#x39;" k="350" />
    <hkern u1="&#xc1;" u2="&#x38;" k="100" />
    <hkern u1="&#xc1;" u2="&#x36;" k="100" />
    <hkern u1="&#xc1;" u2="&#x35;" k="100" />
    <hkern u1="&#xc1;" u2="&#x34;" k="150" />
    <hkern u1="&#xc1;" u2="&#x33;" k="100" />
    <hkern u1="&#xc1;" u2="&#x31;" k="75" />
    <hkern u1="&#xc1;" u2="&#x30;" k="150" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="300" />
    <hkern u1="&#xc2;" u2="&#xfc;" k="125" />
    <hkern u1="&#xc2;" u2="&#xfb;" k="125" />
    <hkern u1="&#xc2;" u2="&#xfa;" k="125" />
    <hkern u1="&#xc2;" u2="&#xf9;" k="125" />
    <hkern u1="&#xc2;" u2="&#xf8;" k="125" />
    <hkern u1="&#xc2;" u2="&#xf6;" k="125" />
    <hkern u1="&#xc2;" u2="&#xf5;" k="125" />
    <hkern u1="&#xc2;" u2="&#xf4;" k="125" />
    <hkern u1="&#xc2;" u2="&#xf3;" k="125" />
    <hkern u1="&#xc2;" u2="&#xf2;" k="125" />
    <hkern u1="&#xc2;" u2="&#xf0;" k="125" />
    <hkern u1="&#xc2;" u2="&#xeb;" k="125" />
    <hkern u1="&#xc2;" u2="&#xea;" k="125" />
    <hkern u1="&#xc2;" u2="&#xe9;" k="125" />
    <hkern u1="&#xc2;" u2="&#xe8;" k="125" />
    <hkern u1="&#xc2;" u2="&#xe7;" k="125" />
    <hkern u1="&#xc2;" u2="&#xe5;" k="75" />
    <hkern u1="&#xc2;" u2="&#xe4;" k="75" />
    <hkern u1="&#xc2;" u2="&#xe3;" k="75" />
    <hkern u1="&#xc2;" u2="&#xe2;" k="75" />
    <hkern u1="&#xc2;" u2="&#xe1;" k="75" />
    <hkern u1="&#xc2;" u2="&#xe0;" k="75" />
    <hkern u1="&#xc2;" u2="&#x178;" k="600" />
    <hkern u1="&#xc2;" u2="&#xdd;" k="600" />
    <hkern u1="&#xc2;" u2="&#xdc;" k="175" />
    <hkern u1="&#xc2;" u2="&#xdb;" k="175" />
    <hkern u1="&#xc2;" u2="&#xda;" k="175" />
    <hkern u1="&#xc2;" u2="&#xd9;" k="175" />
    <hkern u1="&#xc2;" u2="&#xd8;" k="225" />
    <hkern u1="&#xc2;" u2="&#xd6;" k="225" />
    <hkern u1="&#xc2;" u2="&#xd5;" k="225" />
    <hkern u1="&#xc2;" u2="&#xd4;" k="225" />
    <hkern u1="&#xc2;" u2="&#xd3;" k="225" />
    <hkern u1="&#xc2;" u2="&#xd2;" k="225" />
    <hkern u1="&#xc2;" u2="&#xd1;" k="50" />
    <hkern u1="&#xc2;" u2="&#xcf;" k="50" />
    <hkern u1="&#xc2;" u2="&#xce;" k="50" />
    <hkern u1="&#xc2;" u2="&#xcd;" k="50" />
    <hkern u1="&#xc2;" u2="&#xcc;" k="50" />
    <hkern u1="&#xc2;" u2="&#xc7;" k="225" />
    <hkern u1="&#xc2;" u2="&#xc5;" k="75" />
    <hkern u1="&#xc2;" u2="&#xc4;" k="75" />
    <hkern u1="&#xc2;" u2="&#xc3;" k="75" />
    <hkern u1="&#xc2;" u2="&#xc2;" k="75" />
    <hkern u1="&#xc2;" u2="&#xc1;" k="75" />
    <hkern u1="&#xc2;" u2="&#xc0;" k="75" />
    <hkern u1="&#xc2;" u2="v" k="375" />
    <hkern u1="&#xc2;" u2="u" k="125" />
    <hkern u1="&#xc2;" u2="t" k="125" />
    <hkern u1="&#xc2;" u2="q" k="125" />
    <hkern u1="&#xc2;" u2="o" k="125" />
    <hkern u1="&#xc2;" u2="j" k="62" />
    <hkern u1="&#xc2;" u2="g" k="50" />
    <hkern u1="&#xc2;" u2="f" k="125" />
    <hkern u1="&#xc2;" u2="e" k="125" />
    <hkern u1="&#xc2;" u2="d" k="125" />
    <hkern u1="&#xc2;" u2="c" k="125" />
    <hkern u1="&#xc2;" u2="a" k="75" />
    <hkern u1="&#xc2;" u2="^" k="375" />
    <hkern u1="&#xc2;" u2="\" k="700" />
    <hkern u1="&#xc2;" u2="Z" k="50" />
    <hkern u1="&#xc2;" u2="Y" k="600" />
    <hkern u1="&#xc2;" u2="X" k="75" />
    <hkern u1="&#xc2;" u2="W" k="475" />
    <hkern u1="&#xc2;" u2="V" k="600" />
    <hkern u1="&#xc2;" u2="U" k="175" />
    <hkern u1="&#xc2;" u2="T" k="475" />
    <hkern u1="&#xc2;" u2="S" k="75" />
    <hkern u1="&#xc2;" u2="R" k="50" />
    <hkern u1="&#xc2;" u2="Q" k="225" />
    <hkern u1="&#xc2;" u2="P" k="50" />
    <hkern u1="&#xc2;" u2="O" k="225" />
    <hkern u1="&#xc2;" u2="N" k="50" />
    <hkern u1="&#xc2;" u2="M" k="50" />
    <hkern u1="&#xc2;" u2="L" k="50" />
    <hkern u1="&#xc2;" u2="K" k="50" />
    <hkern u1="&#xc2;" u2="J" k="225" />
    <hkern u1="&#xc2;" u2="I" k="50" />
    <hkern u1="&#xc2;" u2="H" k="50" />
    <hkern u1="&#xc2;" u2="G" k="225" />
    <hkern u1="&#xc2;" u2="C" k="225" />
    <hkern u1="&#xc2;" u2="B" k="50" />
    <hkern u1="&#xc2;" u2="A" k="75" />
    <hkern u1="&#xc2;" u2="&#x39;" k="350" />
    <hkern u1="&#xc2;" u2="&#x38;" k="100" />
    <hkern u1="&#xc2;" u2="&#x36;" k="100" />
    <hkern u1="&#xc2;" u2="&#x35;" k="100" />
    <hkern u1="&#xc2;" u2="&#x34;" k="150" />
    <hkern u1="&#xc2;" u2="&#x33;" k="100" />
    <hkern u1="&#xc2;" u2="&#x31;" k="75" />
    <hkern u1="&#xc2;" u2="&#x30;" k="150" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="300" />
    <hkern u1="&#xc3;" u2="&#xfc;" k="125" />
    <hkern u1="&#xc3;" u2="&#xfb;" k="125" />
    <hkern u1="&#xc3;" u2="&#xfa;" k="125" />
    <hkern u1="&#xc3;" u2="&#xf9;" k="125" />
    <hkern u1="&#xc3;" u2="&#xf8;" k="125" />
    <hkern u1="&#xc3;" u2="&#xf6;" k="125" />
    <hkern u1="&#xc3;" u2="&#xf5;" k="125" />
    <hkern u1="&#xc3;" u2="&#xf4;" k="125" />
    <hkern u1="&#xc3;" u2="&#xf3;" k="125" />
    <hkern u1="&#xc3;" u2="&#xf2;" k="125" />
    <hkern u1="&#xc3;" u2="&#xf0;" k="125" />
    <hkern u1="&#xc3;" u2="&#xeb;" k="125" />
    <hkern u1="&#xc3;" u2="&#xea;" k="125" />
    <hkern u1="&#xc3;" u2="&#xe9;" k="125" />
    <hkern u1="&#xc3;" u2="&#xe8;" k="125" />
    <hkern u1="&#xc3;" u2="&#xe7;" k="125" />
    <hkern u1="&#xc3;" u2="&#xe5;" k="75" />
    <hkern u1="&#xc3;" u2="&#xe4;" k="75" />
    <hkern u1="&#xc3;" u2="&#xe3;" k="75" />
    <hkern u1="&#xc3;" u2="&#xe2;" k="75" />
    <hkern u1="&#xc3;" u2="&#xe1;" k="75" />
    <hkern u1="&#xc3;" u2="&#xe0;" k="75" />
    <hkern u1="&#xc3;" u2="&#x178;" k="600" />
    <hkern u1="&#xc3;" u2="&#xdd;" k="600" />
    <hkern u1="&#xc3;" u2="&#xdc;" k="175" />
    <hkern u1="&#xc3;" u2="&#xdb;" k="175" />
    <hkern u1="&#xc3;" u2="&#xda;" k="175" />
    <hkern u1="&#xc3;" u2="&#xd9;" k="175" />
    <hkern u1="&#xc3;" u2="&#xd8;" k="225" />
    <hkern u1="&#xc3;" u2="&#xd6;" k="225" />
    <hkern u1="&#xc3;" u2="&#xd5;" k="225" />
    <hkern u1="&#xc3;" u2="&#xd4;" k="225" />
    <hkern u1="&#xc3;" u2="&#xd3;" k="225" />
    <hkern u1="&#xc3;" u2="&#xd2;" k="225" />
    <hkern u1="&#xc3;" u2="&#xd1;" k="50" />
    <hkern u1="&#xc3;" u2="&#xcf;" k="50" />
    <hkern u1="&#xc3;" u2="&#xce;" k="50" />
    <hkern u1="&#xc3;" u2="&#xcd;" k="50" />
    <hkern u1="&#xc3;" u2="&#xcc;" k="50" />
    <hkern u1="&#xc3;" u2="&#xc7;" k="225" />
    <hkern u1="&#xc3;" u2="&#xc5;" k="75" />
    <hkern u1="&#xc3;" u2="&#xc4;" k="75" />
    <hkern u1="&#xc3;" u2="&#xc3;" k="75" />
    <hkern u1="&#xc3;" u2="&#xc2;" k="75" />
    <hkern u1="&#xc3;" u2="&#xc1;" k="75" />
    <hkern u1="&#xc3;" u2="&#xc0;" k="75" />
    <hkern u1="&#xc3;" u2="v" k="375" />
    <hkern u1="&#xc3;" u2="u" k="125" />
    <hkern u1="&#xc3;" u2="t" k="125" />
    <hkern u1="&#xc3;" u2="q" k="125" />
    <hkern u1="&#xc3;" u2="o" k="125" />
    <hkern u1="&#xc3;" u2="j" k="62" />
    <hkern u1="&#xc3;" u2="g" k="50" />
    <hkern u1="&#xc3;" u2="f" k="125" />
    <hkern u1="&#xc3;" u2="e" k="125" />
    <hkern u1="&#xc3;" u2="d" k="125" />
    <hkern u1="&#xc3;" u2="c" k="125" />
    <hkern u1="&#xc3;" u2="a" k="75" />
    <hkern u1="&#xc3;" u2="^" k="375" />
    <hkern u1="&#xc3;" u2="\" k="700" />
    <hkern u1="&#xc3;" u2="Z" k="50" />
    <hkern u1="&#xc3;" u2="Y" k="600" />
    <hkern u1="&#xc3;" u2="X" k="75" />
    <hkern u1="&#xc3;" u2="W" k="475" />
    <hkern u1="&#xc3;" u2="V" k="600" />
    <hkern u1="&#xc3;" u2="U" k="175" />
    <hkern u1="&#xc3;" u2="T" k="475" />
    <hkern u1="&#xc3;" u2="S" k="75" />
    <hkern u1="&#xc3;" u2="R" k="50" />
    <hkern u1="&#xc3;" u2="Q" k="225" />
    <hkern u1="&#xc3;" u2="P" k="50" />
    <hkern u1="&#xc3;" u2="O" k="225" />
    <hkern u1="&#xc3;" u2="N" k="50" />
    <hkern u1="&#xc3;" u2="M" k="50" />
    <hkern u1="&#xc3;" u2="L" k="50" />
    <hkern u1="&#xc3;" u2="K" k="50" />
    <hkern u1="&#xc3;" u2="J" k="225" />
    <hkern u1="&#xc3;" u2="I" k="50" />
    <hkern u1="&#xc3;" u2="H" k="50" />
    <hkern u1="&#xc3;" u2="G" k="225" />
    <hkern u1="&#xc3;" u2="C" k="225" />
    <hkern u1="&#xc3;" u2="B" k="50" />
    <hkern u1="&#xc3;" u2="A" k="75" />
    <hkern u1="&#xc3;" u2="&#x39;" k="350" />
    <hkern u1="&#xc3;" u2="&#x38;" k="100" />
    <hkern u1="&#xc3;" u2="&#x36;" k="100" />
    <hkern u1="&#xc3;" u2="&#x35;" k="100" />
    <hkern u1="&#xc3;" u2="&#x34;" k="150" />
    <hkern u1="&#xc3;" u2="&#x33;" k="100" />
    <hkern u1="&#xc3;" u2="&#x31;" k="75" />
    <hkern u1="&#xc3;" u2="&#x30;" k="150" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="300" />
    <hkern u1="&#xc4;" u2="&#xfc;" k="125" />
    <hkern u1="&#xc4;" u2="&#xfb;" k="125" />
    <hkern u1="&#xc4;" u2="&#xfa;" k="125" />
    <hkern u1="&#xc4;" u2="&#xf9;" k="125" />
    <hkern u1="&#xc4;" u2="&#xf8;" k="125" />
    <hkern u1="&#xc4;" u2="&#xf6;" k="125" />
    <hkern u1="&#xc4;" u2="&#xf5;" k="125" />
    <hkern u1="&#xc4;" u2="&#xf4;" k="125" />
    <hkern u1="&#xc4;" u2="&#xf3;" k="125" />
    <hkern u1="&#xc4;" u2="&#xf2;" k="125" />
    <hkern u1="&#xc4;" u2="&#xf0;" k="125" />
    <hkern u1="&#xc4;" u2="&#xeb;" k="125" />
    <hkern u1="&#xc4;" u2="&#xea;" k="125" />
    <hkern u1="&#xc4;" u2="&#xe9;" k="125" />
    <hkern u1="&#xc4;" u2="&#xe8;" k="125" />
    <hkern u1="&#xc4;" u2="&#xe7;" k="125" />
    <hkern u1="&#xc4;" u2="&#xe5;" k="75" />
    <hkern u1="&#xc4;" u2="&#xe4;" k="75" />
    <hkern u1="&#xc4;" u2="&#xe3;" k="75" />
    <hkern u1="&#xc4;" u2="&#xe2;" k="75" />
    <hkern u1="&#xc4;" u2="&#xe1;" k="75" />
    <hkern u1="&#xc4;" u2="&#xe0;" k="75" />
    <hkern u1="&#xc4;" u2="&#x178;" k="600" />
    <hkern u1="&#xc4;" u2="&#xdd;" k="600" />
    <hkern u1="&#xc4;" u2="&#xdc;" k="175" />
    <hkern u1="&#xc4;" u2="&#xdb;" k="175" />
    <hkern u1="&#xc4;" u2="&#xda;" k="175" />
    <hkern u1="&#xc4;" u2="&#xd9;" k="175" />
    <hkern u1="&#xc4;" u2="&#xd8;" k="225" />
    <hkern u1="&#xc4;" u2="&#xd6;" k="225" />
    <hkern u1="&#xc4;" u2="&#xd5;" k="225" />
    <hkern u1="&#xc4;" u2="&#xd4;" k="225" />
    <hkern u1="&#xc4;" u2="&#xd3;" k="225" />
    <hkern u1="&#xc4;" u2="&#xd2;" k="225" />
    <hkern u1="&#xc4;" u2="&#xd1;" k="50" />
    <hkern u1="&#xc4;" u2="&#xcf;" k="50" />
    <hkern u1="&#xc4;" u2="&#xce;" k="50" />
    <hkern u1="&#xc4;" u2="&#xcd;" k="50" />
    <hkern u1="&#xc4;" u2="&#xcc;" k="50" />
    <hkern u1="&#xc4;" u2="&#xc7;" k="225" />
    <hkern u1="&#xc4;" u2="&#xc5;" k="75" />
    <hkern u1="&#xc4;" u2="&#xc4;" k="75" />
    <hkern u1="&#xc4;" u2="&#xc3;" k="75" />
    <hkern u1="&#xc4;" u2="&#xc2;" k="75" />
    <hkern u1="&#xc4;" u2="&#xc1;" k="75" />
    <hkern u1="&#xc4;" u2="&#xc0;" k="75" />
    <hkern u1="&#xc4;" u2="v" k="375" />
    <hkern u1="&#xc4;" u2="u" k="125" />
    <hkern u1="&#xc4;" u2="t" k="125" />
    <hkern u1="&#xc4;" u2="q" k="125" />
    <hkern u1="&#xc4;" u2="o" k="125" />
    <hkern u1="&#xc4;" u2="j" k="62" />
    <hkern u1="&#xc4;" u2="g" k="50" />
    <hkern u1="&#xc4;" u2="f" k="125" />
    <hkern u1="&#xc4;" u2="e" k="125" />
    <hkern u1="&#xc4;" u2="d" k="125" />
    <hkern u1="&#xc4;" u2="c" k="125" />
    <hkern u1="&#xc4;" u2="a" k="75" />
    <hkern u1="&#xc4;" u2="^" k="375" />
    <hkern u1="&#xc4;" u2="\" k="700" />
    <hkern u1="&#xc4;" u2="Z" k="50" />
    <hkern u1="&#xc4;" u2="Y" k="600" />
    <hkern u1="&#xc4;" u2="X" k="75" />
    <hkern u1="&#xc4;" u2="W" k="475" />
    <hkern u1="&#xc4;" u2="V" k="600" />
    <hkern u1="&#xc4;" u2="U" k="175" />
    <hkern u1="&#xc4;" u2="T" k="475" />
    <hkern u1="&#xc4;" u2="S" k="75" />
    <hkern u1="&#xc4;" u2="R" k="50" />
    <hkern u1="&#xc4;" u2="Q" k="225" />
    <hkern u1="&#xc4;" u2="P" k="50" />
    <hkern u1="&#xc4;" u2="O" k="225" />
    <hkern u1="&#xc4;" u2="N" k="50" />
    <hkern u1="&#xc4;" u2="M" k="50" />
    <hkern u1="&#xc4;" u2="L" k="50" />
    <hkern u1="&#xc4;" u2="K" k="50" />
    <hkern u1="&#xc4;" u2="J" k="225" />
    <hkern u1="&#xc4;" u2="I" k="50" />
    <hkern u1="&#xc4;" u2="H" k="50" />
    <hkern u1="&#xc4;" u2="G" k="225" />
    <hkern u1="&#xc4;" u2="C" k="225" />
    <hkern u1="&#xc4;" u2="B" k="50" />
    <hkern u1="&#xc4;" u2="A" k="75" />
    <hkern u1="&#xc4;" u2="&#x39;" k="350" />
    <hkern u1="&#xc4;" u2="&#x38;" k="100" />
    <hkern u1="&#xc4;" u2="&#x36;" k="100" />
    <hkern u1="&#xc4;" u2="&#x35;" k="100" />
    <hkern u1="&#xc4;" u2="&#x34;" k="150" />
    <hkern u1="&#xc4;" u2="&#x33;" k="100" />
    <hkern u1="&#xc4;" u2="&#x31;" k="75" />
    <hkern u1="&#xc4;" u2="&#x30;" k="150" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="300" />
    <hkern u1="&#xc5;" u2="&#xfc;" k="125" />
    <hkern u1="&#xc5;" u2="&#xfb;" k="125" />
    <hkern u1="&#xc5;" u2="&#xfa;" k="125" />
    <hkern u1="&#xc5;" u2="&#xf9;" k="125" />
    <hkern u1="&#xc5;" u2="&#xf8;" k="125" />
    <hkern u1="&#xc5;" u2="&#xf6;" k="125" />
    <hkern u1="&#xc5;" u2="&#xf5;" k="125" />
    <hkern u1="&#xc5;" u2="&#xf4;" k="125" />
    <hkern u1="&#xc5;" u2="&#xf3;" k="125" />
    <hkern u1="&#xc5;" u2="&#xf2;" k="125" />
    <hkern u1="&#xc5;" u2="&#xf0;" k="125" />
    <hkern u1="&#xc5;" u2="&#xeb;" k="125" />
    <hkern u1="&#xc5;" u2="&#xea;" k="125" />
    <hkern u1="&#xc5;" u2="&#xe9;" k="125" />
    <hkern u1="&#xc5;" u2="&#xe8;" k="125" />
    <hkern u1="&#xc5;" u2="&#xe7;" k="125" />
    <hkern u1="&#xc5;" u2="&#xe5;" k="75" />
    <hkern u1="&#xc5;" u2="&#xe4;" k="75" />
    <hkern u1="&#xc5;" u2="&#xe3;" k="75" />
    <hkern u1="&#xc5;" u2="&#xe2;" k="75" />
    <hkern u1="&#xc5;" u2="&#xe1;" k="75" />
    <hkern u1="&#xc5;" u2="&#xe0;" k="75" />
    <hkern u1="&#xc5;" u2="&#x178;" k="600" />
    <hkern u1="&#xc5;" u2="&#xdd;" k="600" />
    <hkern u1="&#xc5;" u2="&#xdc;" k="175" />
    <hkern u1="&#xc5;" u2="&#xdb;" k="175" />
    <hkern u1="&#xc5;" u2="&#xda;" k="175" />
    <hkern u1="&#xc5;" u2="&#xd9;" k="175" />
    <hkern u1="&#xc5;" u2="&#xd8;" k="225" />
    <hkern u1="&#xc5;" u2="&#xd6;" k="225" />
    <hkern u1="&#xc5;" u2="&#xd5;" k="225" />
    <hkern u1="&#xc5;" u2="&#xd4;" k="225" />
    <hkern u1="&#xc5;" u2="&#xd3;" k="225" />
    <hkern u1="&#xc5;" u2="&#xd2;" k="225" />
    <hkern u1="&#xc5;" u2="&#xd1;" k="50" />
    <hkern u1="&#xc5;" u2="&#xcf;" k="50" />
    <hkern u1="&#xc5;" u2="&#xce;" k="50" />
    <hkern u1="&#xc5;" u2="&#xcd;" k="50" />
    <hkern u1="&#xc5;" u2="&#xcc;" k="50" />
    <hkern u1="&#xc5;" u2="&#xc7;" k="225" />
    <hkern u1="&#xc5;" u2="&#xc5;" k="75" />
    <hkern u1="&#xc5;" u2="&#xc4;" k="75" />
    <hkern u1="&#xc5;" u2="&#xc3;" k="75" />
    <hkern u1="&#xc5;" u2="&#xc2;" k="75" />
    <hkern u1="&#xc5;" u2="&#xc1;" k="75" />
    <hkern u1="&#xc5;" u2="&#xc0;" k="75" />
    <hkern u1="&#xc5;" u2="v" k="375" />
    <hkern u1="&#xc5;" u2="u" k="125" />
    <hkern u1="&#xc5;" u2="t" k="125" />
    <hkern u1="&#xc5;" u2="q" k="125" />
    <hkern u1="&#xc5;" u2="o" k="125" />
    <hkern u1="&#xc5;" u2="j" k="62" />
    <hkern u1="&#xc5;" u2="g" k="50" />
    <hkern u1="&#xc5;" u2="f" k="125" />
    <hkern u1="&#xc5;" u2="e" k="125" />
    <hkern u1="&#xc5;" u2="d" k="125" />
    <hkern u1="&#xc5;" u2="c" k="125" />
    <hkern u1="&#xc5;" u2="a" k="75" />
    <hkern u1="&#xc5;" u2="^" k="375" />
    <hkern u1="&#xc5;" u2="\" k="700" />
    <hkern u1="&#xc5;" u2="Z" k="50" />
    <hkern u1="&#xc5;" u2="Y" k="600" />
    <hkern u1="&#xc5;" u2="X" k="75" />
    <hkern u1="&#xc5;" u2="W" k="475" />
    <hkern u1="&#xc5;" u2="V" k="600" />
    <hkern u1="&#xc5;" u2="U" k="175" />
    <hkern u1="&#xc5;" u2="T" k="475" />
    <hkern u1="&#xc5;" u2="S" k="75" />
    <hkern u1="&#xc5;" u2="R" k="50" />
    <hkern u1="&#xc5;" u2="Q" k="225" />
    <hkern u1="&#xc5;" u2="P" k="50" />
    <hkern u1="&#xc5;" u2="O" k="225" />
    <hkern u1="&#xc5;" u2="N" k="50" />
    <hkern u1="&#xc5;" u2="M" k="50" />
    <hkern u1="&#xc5;" u2="L" k="50" />
    <hkern u1="&#xc5;" u2="K" k="50" />
    <hkern u1="&#xc5;" u2="J" k="225" />
    <hkern u1="&#xc5;" u2="I" k="50" />
    <hkern u1="&#xc5;" u2="H" k="50" />
    <hkern u1="&#xc5;" u2="G" k="225" />
    <hkern u1="&#xc5;" u2="C" k="225" />
    <hkern u1="&#xc5;" u2="B" k="50" />
    <hkern u1="&#xc5;" u2="A" k="75" />
    <hkern u1="&#xc5;" u2="&#x39;" k="350" />
    <hkern u1="&#xc5;" u2="&#x38;" k="100" />
    <hkern u1="&#xc5;" u2="&#x36;" k="100" />
    <hkern u1="&#xc5;" u2="&#x35;" k="100" />
    <hkern u1="&#xc5;" u2="&#x34;" k="150" />
    <hkern u1="&#xc5;" u2="&#x33;" k="100" />
    <hkern u1="&#xc5;" u2="&#x31;" k="75" />
    <hkern u1="&#xc5;" u2="&#x30;" k="150" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="300" />
    <hkern u1="&#xc7;" u2="&#xff;" k="125" />
    <hkern u1="&#xc7;" u2="&#xfd;" k="125" />
    <hkern u1="&#xc7;" u2="&#xfc;" k="75" />
    <hkern u1="&#xc7;" u2="&#xfb;" k="75" />
    <hkern u1="&#xc7;" u2="&#xfa;" k="75" />
    <hkern u1="&#xc7;" u2="&#xf9;" k="75" />
    <hkern u1="&#xc7;" u2="&#xf8;" k="75" />
    <hkern u1="&#xc7;" u2="&#xf6;" k="75" />
    <hkern u1="&#xc7;" u2="&#xf5;" k="75" />
    <hkern u1="&#xc7;" u2="&#xf4;" k="75" />
    <hkern u1="&#xc7;" u2="&#xf3;" k="75" />
    <hkern u1="&#xc7;" u2="&#xf2;" k="75" />
    <hkern u1="&#xc7;" u2="&#xf0;" k="75" />
    <hkern u1="&#xc7;" u2="&#xeb;" k="75" />
    <hkern u1="&#xc7;" u2="&#xea;" k="75" />
    <hkern u1="&#xc7;" u2="&#xe9;" k="75" />
    <hkern u1="&#xc7;" u2="&#xe8;" k="75" />
    <hkern u1="&#xc7;" u2="&#xe7;" k="75" />
    <hkern u1="&#xc7;" u2="&#xd8;" k="25" />
    <hkern u1="&#xc7;" u2="&#xd6;" k="25" />
    <hkern u1="&#xc7;" u2="&#xd5;" k="25" />
    <hkern u1="&#xc7;" u2="&#xd4;" k="25" />
    <hkern u1="&#xc7;" u2="&#xd3;" k="25" />
    <hkern u1="&#xc7;" u2="&#xd2;" k="25" />
    <hkern u1="&#xc7;" u2="&#xc7;" k="50" />
    <hkern u1="&#xc7;" u2="&#xc5;" k="100" />
    <hkern u1="&#xc7;" u2="&#xc4;" k="100" />
    <hkern u1="&#xc7;" u2="&#xc3;" k="100" />
    <hkern u1="&#xc7;" u2="&#xc2;" k="100" />
    <hkern u1="&#xc7;" u2="&#xc1;" k="100" />
    <hkern u1="&#xc7;" u2="&#xc0;" k="100" />
    <hkern u1="&#xc7;" u2="z" k="75" />
    <hkern u1="&#xc7;" u2="y" k="125" />
    <hkern u1="&#xc7;" u2="x" k="75" />
    <hkern u1="&#xc7;" u2="w" k="125" />
    <hkern u1="&#xc7;" u2="v" k="125" />
    <hkern u1="&#xc7;" u2="u" k="75" />
    <hkern u1="&#xc7;" u2="q" k="75" />
    <hkern u1="&#xc7;" u2="o" k="75" />
    <hkern u1="&#xc7;" u2="e" k="75" />
    <hkern u1="&#xc7;" u2="d" k="75" />
    <hkern u1="&#xc7;" u2="c" k="75" />
    <hkern u1="&#xc7;" u2="Z" k="75" />
    <hkern u1="&#xc7;" u2="X" k="75" />
    <hkern u1="&#xc7;" u2="W" k="50" />
    <hkern u1="&#xc7;" u2="V" k="50" />
    <hkern u1="&#xc7;" u2="Q" k="25" />
    <hkern u1="&#xc7;" u2="O" k="25" />
    <hkern u1="&#xc7;" u2="J" k="125" />
    <hkern u1="&#xc7;" u2="C" k="50" />
    <hkern u1="&#xc7;" u2="A" k="100" />
    <hkern u1="&#xc7;" u2="&#x34;" k="200" />
    <hkern u1="&#xc8;" u2="&#xff;" k="150" />
    <hkern u1="&#xc8;" u2="&#xfd;" k="150" />
    <hkern u1="&#xc8;" u2="&#xfc;" k="50" />
    <hkern u1="&#xc8;" u2="&#xfb;" k="50" />
    <hkern u1="&#xc8;" u2="&#xfa;" k="50" />
    <hkern u1="&#xc8;" u2="&#xf9;" k="50" />
    <hkern u1="&#xc8;" u2="&#xf8;" k="75" />
    <hkern u1="&#xc8;" u2="&#xf6;" k="75" />
    <hkern u1="&#xc8;" u2="&#xf5;" k="75" />
    <hkern u1="&#xc8;" u2="&#xf4;" k="75" />
    <hkern u1="&#xc8;" u2="&#xf3;" k="75" />
    <hkern u1="&#xc8;" u2="&#xf2;" k="75" />
    <hkern u1="&#xc8;" u2="&#xf0;" k="50" />
    <hkern u1="&#xc8;" u2="&#xeb;" k="75" />
    <hkern u1="&#xc8;" u2="&#xea;" k="75" />
    <hkern u1="&#xc8;" u2="&#xe9;" k="75" />
    <hkern u1="&#xc8;" u2="&#xe8;" k="75" />
    <hkern u1="&#xc8;" u2="&#xe7;" k="50" />
    <hkern u1="&#xc8;" u2="&#xd8;" k="75" />
    <hkern u1="&#xc8;" u2="&#xd6;" k="75" />
    <hkern u1="&#xc8;" u2="&#xd5;" k="75" />
    <hkern u1="&#xc8;" u2="&#xd4;" k="75" />
    <hkern u1="&#xc8;" u2="&#xd3;" k="75" />
    <hkern u1="&#xc8;" u2="&#xd2;" k="75" />
    <hkern u1="&#xc8;" u2="&#xc7;" k="75" />
    <hkern u1="&#xc8;" u2="&#xc5;" k="75" />
    <hkern u1="&#xc8;" u2="&#xc4;" k="75" />
    <hkern u1="&#xc8;" u2="&#xc3;" k="75" />
    <hkern u1="&#xc8;" u2="&#xc2;" k="75" />
    <hkern u1="&#xc8;" u2="&#xc1;" k="75" />
    <hkern u1="&#xc8;" u2="&#xc0;" k="75" />
    <hkern u1="&#xc8;" u2="y" k="150" />
    <hkern u1="&#xc8;" u2="x" k="50" />
    <hkern u1="&#xc8;" u2="w" k="150" />
    <hkern u1="&#xc8;" u2="v" k="150" />
    <hkern u1="&#xc8;" u2="u" k="50" />
    <hkern u1="&#xc8;" u2="t" k="50" />
    <hkern u1="&#xc8;" u2="q" k="50" />
    <hkern u1="&#xc8;" u2="o" k="75" />
    <hkern u1="&#xc8;" u2="j" k="75" />
    <hkern u1="&#xc8;" u2="f" k="125" />
    <hkern u1="&#xc8;" u2="e" k="75" />
    <hkern u1="&#xc8;" u2="d" k="50" />
    <hkern u1="&#xc8;" u2="c" k="50" />
    <hkern u1="&#xc8;" u2="Z" k="50" />
    <hkern u1="&#xc8;" u2="X" k="75" />
    <hkern u1="&#xc8;" u2="S" k="50" />
    <hkern u1="&#xc8;" u2="Q" k="75" />
    <hkern u1="&#xc8;" u2="O" k="75" />
    <hkern u1="&#xc8;" u2="J" k="175" />
    <hkern u1="&#xc8;" u2="G" k="75" />
    <hkern u1="&#xc8;" u2="C" k="75" />
    <hkern u1="&#xc8;" u2="A" k="75" />
    <hkern u1="&#xc8;" u2="&#x34;" k="175" />
    <hkern u1="&#xc8;" u2="&#x30;" k="100" />
    <hkern u1="&#xc9;" u2="&#xff;" k="150" />
    <hkern u1="&#xc9;" u2="&#xfd;" k="150" />
    <hkern u1="&#xc9;" u2="&#xfc;" k="50" />
    <hkern u1="&#xc9;" u2="&#xfb;" k="50" />
    <hkern u1="&#xc9;" u2="&#xfa;" k="50" />
    <hkern u1="&#xc9;" u2="&#xf9;" k="50" />
    <hkern u1="&#xc9;" u2="&#xf8;" k="75" />
    <hkern u1="&#xc9;" u2="&#xf6;" k="75" />
    <hkern u1="&#xc9;" u2="&#xf5;" k="75" />
    <hkern u1="&#xc9;" u2="&#xf4;" k="75" />
    <hkern u1="&#xc9;" u2="&#xf3;" k="75" />
    <hkern u1="&#xc9;" u2="&#xf2;" k="75" />
    <hkern u1="&#xc9;" u2="&#xf0;" k="50" />
    <hkern u1="&#xc9;" u2="&#xeb;" k="75" />
    <hkern u1="&#xc9;" u2="&#xea;" k="75" />
    <hkern u1="&#xc9;" u2="&#xe9;" k="75" />
    <hkern u1="&#xc9;" u2="&#xe8;" k="75" />
    <hkern u1="&#xc9;" u2="&#xe7;" k="50" />
    <hkern u1="&#xc9;" u2="&#xd8;" k="75" />
    <hkern u1="&#xc9;" u2="&#xd6;" k="75" />
    <hkern u1="&#xc9;" u2="&#xd5;" k="75" />
    <hkern u1="&#xc9;" u2="&#xd4;" k="75" />
    <hkern u1="&#xc9;" u2="&#xd3;" k="75" />
    <hkern u1="&#xc9;" u2="&#xd2;" k="75" />
    <hkern u1="&#xc9;" u2="&#xc7;" k="75" />
    <hkern u1="&#xc9;" u2="&#xc5;" k="75" />
    <hkern u1="&#xc9;" u2="&#xc4;" k="75" />
    <hkern u1="&#xc9;" u2="&#xc3;" k="75" />
    <hkern u1="&#xc9;" u2="&#xc2;" k="75" />
    <hkern u1="&#xc9;" u2="&#xc1;" k="75" />
    <hkern u1="&#xc9;" u2="&#xc0;" k="75" />
    <hkern u1="&#xc9;" u2="y" k="150" />
    <hkern u1="&#xc9;" u2="x" k="50" />
    <hkern u1="&#xc9;" u2="w" k="150" />
    <hkern u1="&#xc9;" u2="v" k="150" />
    <hkern u1="&#xc9;" u2="u" k="50" />
    <hkern u1="&#xc9;" u2="t" k="50" />
    <hkern u1="&#xc9;" u2="q" k="50" />
    <hkern u1="&#xc9;" u2="o" k="75" />
    <hkern u1="&#xc9;" u2="j" k="75" />
    <hkern u1="&#xc9;" u2="f" k="125" />
    <hkern u1="&#xc9;" u2="e" k="75" />
    <hkern u1="&#xc9;" u2="d" k="50" />
    <hkern u1="&#xc9;" u2="c" k="50" />
    <hkern u1="&#xc9;" u2="Z" k="50" />
    <hkern u1="&#xc9;" u2="X" k="75" />
    <hkern u1="&#xc9;" u2="S" k="50" />
    <hkern u1="&#xc9;" u2="Q" k="75" />
    <hkern u1="&#xc9;" u2="O" k="75" />
    <hkern u1="&#xc9;" u2="J" k="175" />
    <hkern u1="&#xc9;" u2="G" k="75" />
    <hkern u1="&#xc9;" u2="C" k="75" />
    <hkern u1="&#xc9;" u2="A" k="75" />
    <hkern u1="&#xc9;" u2="&#x34;" k="175" />
    <hkern u1="&#xc9;" u2="&#x30;" k="100" />
    <hkern u1="&#xca;" u2="&#xff;" k="150" />
    <hkern u1="&#xca;" u2="&#xfd;" k="150" />
    <hkern u1="&#xca;" u2="&#xfc;" k="50" />
    <hkern u1="&#xca;" u2="&#xfb;" k="50" />
    <hkern u1="&#xca;" u2="&#xfa;" k="50" />
    <hkern u1="&#xca;" u2="&#xf9;" k="50" />
    <hkern u1="&#xca;" u2="&#xf8;" k="75" />
    <hkern u1="&#xca;" u2="&#xf6;" k="75" />
    <hkern u1="&#xca;" u2="&#xf5;" k="75" />
    <hkern u1="&#xca;" u2="&#xf4;" k="75" />
    <hkern u1="&#xca;" u2="&#xf3;" k="75" />
    <hkern u1="&#xca;" u2="&#xf2;" k="75" />
    <hkern u1="&#xca;" u2="&#xf0;" k="50" />
    <hkern u1="&#xca;" u2="&#xeb;" k="75" />
    <hkern u1="&#xca;" u2="&#xea;" k="75" />
    <hkern u1="&#xca;" u2="&#xe9;" k="75" />
    <hkern u1="&#xca;" u2="&#xe8;" k="75" />
    <hkern u1="&#xca;" u2="&#xe7;" k="50" />
    <hkern u1="&#xca;" u2="&#xd8;" k="75" />
    <hkern u1="&#xca;" u2="&#xd6;" k="75" />
    <hkern u1="&#xca;" u2="&#xd5;" k="75" />
    <hkern u1="&#xca;" u2="&#xd4;" k="75" />
    <hkern u1="&#xca;" u2="&#xd3;" k="75" />
    <hkern u1="&#xca;" u2="&#xd2;" k="75" />
    <hkern u1="&#xca;" u2="&#xc7;" k="75" />
    <hkern u1="&#xca;" u2="&#xc5;" k="75" />
    <hkern u1="&#xca;" u2="&#xc4;" k="75" />
    <hkern u1="&#xca;" u2="&#xc3;" k="75" />
    <hkern u1="&#xca;" u2="&#xc2;" k="75" />
    <hkern u1="&#xca;" u2="&#xc1;" k="75" />
    <hkern u1="&#xca;" u2="&#xc0;" k="75" />
    <hkern u1="&#xca;" u2="y" k="150" />
    <hkern u1="&#xca;" u2="x" k="50" />
    <hkern u1="&#xca;" u2="w" k="150" />
    <hkern u1="&#xca;" u2="v" k="150" />
    <hkern u1="&#xca;" u2="u" k="50" />
    <hkern u1="&#xca;" u2="t" k="50" />
    <hkern u1="&#xca;" u2="q" k="50" />
    <hkern u1="&#xca;" u2="o" k="75" />
    <hkern u1="&#xca;" u2="j" k="75" />
    <hkern u1="&#xca;" u2="f" k="125" />
    <hkern u1="&#xca;" u2="e" k="75" />
    <hkern u1="&#xca;" u2="d" k="50" />
    <hkern u1="&#xca;" u2="c" k="50" />
    <hkern u1="&#xca;" u2="Z" k="50" />
    <hkern u1="&#xca;" u2="X" k="75" />
    <hkern u1="&#xca;" u2="S" k="50" />
    <hkern u1="&#xca;" u2="Q" k="75" />
    <hkern u1="&#xca;" u2="O" k="75" />
    <hkern u1="&#xca;" u2="J" k="175" />
    <hkern u1="&#xca;" u2="G" k="75" />
    <hkern u1="&#xca;" u2="C" k="75" />
    <hkern u1="&#xca;" u2="A" k="75" />
    <hkern u1="&#xca;" u2="&#x34;" k="175" />
    <hkern u1="&#xca;" u2="&#x30;" k="100" />
    <hkern u1="&#xcb;" u2="&#xff;" k="150" />
    <hkern u1="&#xcb;" u2="&#xfd;" k="150" />
    <hkern u1="&#xcb;" u2="&#xfc;" k="50" />
    <hkern u1="&#xcb;" u2="&#xfb;" k="50" />
    <hkern u1="&#xcb;" u2="&#xfa;" k="50" />
    <hkern u1="&#xcb;" u2="&#xf9;" k="50" />
    <hkern u1="&#xcb;" u2="&#xf8;" k="75" />
    <hkern u1="&#xcb;" u2="&#xf6;" k="75" />
    <hkern u1="&#xcb;" u2="&#xf5;" k="75" />
    <hkern u1="&#xcb;" u2="&#xf4;" k="75" />
    <hkern u1="&#xcb;" u2="&#xf3;" k="75" />
    <hkern u1="&#xcb;" u2="&#xf2;" k="75" />
    <hkern u1="&#xcb;" u2="&#xf0;" k="50" />
    <hkern u1="&#xcb;" u2="&#xeb;" k="75" />
    <hkern u1="&#xcb;" u2="&#xea;" k="75" />
    <hkern u1="&#xcb;" u2="&#xe9;" k="75" />
    <hkern u1="&#xcb;" u2="&#xe8;" k="75" />
    <hkern u1="&#xcb;" u2="&#xe7;" k="50" />
    <hkern u1="&#xcb;" u2="&#xd8;" k="75" />
    <hkern u1="&#xcb;" u2="&#xd6;" k="75" />
    <hkern u1="&#xcb;" u2="&#xd5;" k="75" />
    <hkern u1="&#xcb;" u2="&#xd4;" k="75" />
    <hkern u1="&#xcb;" u2="&#xd3;" k="75" />
    <hkern u1="&#xcb;" u2="&#xd2;" k="75" />
    <hkern u1="&#xcb;" u2="&#xc7;" k="75" />
    <hkern u1="&#xcb;" u2="&#xc5;" k="75" />
    <hkern u1="&#xcb;" u2="&#xc4;" k="75" />
    <hkern u1="&#xcb;" u2="&#xc3;" k="75" />
    <hkern u1="&#xcb;" u2="&#xc2;" k="75" />
    <hkern u1="&#xcb;" u2="&#xc1;" k="75" />
    <hkern u1="&#xcb;" u2="&#xc0;" k="75" />
    <hkern u1="&#xcb;" u2="y" k="150" />
    <hkern u1="&#xcb;" u2="x" k="50" />
    <hkern u1="&#xcb;" u2="w" k="150" />
    <hkern u1="&#xcb;" u2="v" k="150" />
    <hkern u1="&#xcb;" u2="u" k="50" />
    <hkern u1="&#xcb;" u2="t" k="50" />
    <hkern u1="&#xcb;" u2="q" k="50" />
    <hkern u1="&#xcb;" u2="o" k="75" />
    <hkern u1="&#xcb;" u2="j" k="75" />
    <hkern u1="&#xcb;" u2="f" k="125" />
    <hkern u1="&#xcb;" u2="e" k="75" />
    <hkern u1="&#xcb;" u2="d" k="50" />
    <hkern u1="&#xcb;" u2="c" k="50" />
    <hkern u1="&#xcb;" u2="Z" k="50" />
    <hkern u1="&#xcb;" u2="X" k="75" />
    <hkern u1="&#xcb;" u2="S" k="50" />
    <hkern u1="&#xcb;" u2="Q" k="75" />
    <hkern u1="&#xcb;" u2="O" k="75" />
    <hkern u1="&#xcb;" u2="J" k="175" />
    <hkern u1="&#xcb;" u2="G" k="75" />
    <hkern u1="&#xcb;" u2="C" k="75" />
    <hkern u1="&#xcb;" u2="A" k="75" />
    <hkern u1="&#xcb;" u2="&#x34;" k="175" />
    <hkern u1="&#xcb;" u2="&#x30;" k="100" />
    <hkern u1="&#xcc;" u2="&#xff;" k="75" />
    <hkern u1="&#xcc;" u2="&#xfd;" k="75" />
    <hkern u1="&#xcc;" u2="&#xfc;" k="50" />
    <hkern u1="&#xcc;" u2="&#xfb;" k="50" />
    <hkern u1="&#xcc;" u2="&#xfa;" k="50" />
    <hkern u1="&#xcc;" u2="&#xf9;" k="50" />
    <hkern u1="&#xcc;" u2="&#xc5;" k="50" />
    <hkern u1="&#xcc;" u2="&#xc4;" k="50" />
    <hkern u1="&#xcc;" u2="&#xc3;" k="50" />
    <hkern u1="&#xcc;" u2="&#xc2;" k="50" />
    <hkern u1="&#xcc;" u2="&#xc1;" k="50" />
    <hkern u1="&#xcc;" u2="&#xc0;" k="50" />
    <hkern u1="&#xcc;" u2="y" k="75" />
    <hkern u1="&#xcc;" u2="w" k="75" />
    <hkern u1="&#xcc;" u2="v" k="75" />
    <hkern u1="&#xcc;" u2="u" k="50" />
    <hkern u1="&#xcc;" u2="t" k="50" />
    <hkern u1="&#xcc;" u2="f" k="75" />
    <hkern u1="&#xcc;" u2="T" k="50" />
    <hkern u1="&#xcc;" u2="J" k="75" />
    <hkern u1="&#xcc;" u2="A" k="50" />
    <hkern u1="&#xcd;" u2="&#xff;" k="75" />
    <hkern u1="&#xcd;" u2="&#xfd;" k="75" />
    <hkern u1="&#xcd;" u2="&#xfc;" k="50" />
    <hkern u1="&#xcd;" u2="&#xfb;" k="50" />
    <hkern u1="&#xcd;" u2="&#xfa;" k="50" />
    <hkern u1="&#xcd;" u2="&#xf9;" k="50" />
    <hkern u1="&#xcd;" u2="&#xc5;" k="50" />
    <hkern u1="&#xcd;" u2="&#xc4;" k="50" />
    <hkern u1="&#xcd;" u2="&#xc3;" k="50" />
    <hkern u1="&#xcd;" u2="&#xc2;" k="50" />
    <hkern u1="&#xcd;" u2="&#xc1;" k="50" />
    <hkern u1="&#xcd;" u2="&#xc0;" k="50" />
    <hkern u1="&#xcd;" u2="y" k="75" />
    <hkern u1="&#xcd;" u2="w" k="75" />
    <hkern u1="&#xcd;" u2="v" k="75" />
    <hkern u1="&#xcd;" u2="u" k="50" />
    <hkern u1="&#xcd;" u2="t" k="50" />
    <hkern u1="&#xcd;" u2="f" k="75" />
    <hkern u1="&#xcd;" u2="T" k="50" />
    <hkern u1="&#xcd;" u2="J" k="75" />
    <hkern u1="&#xcd;" u2="A" k="50" />
    <hkern u1="&#xce;" u2="&#xff;" k="75" />
    <hkern u1="&#xce;" u2="&#xfd;" k="75" />
    <hkern u1="&#xce;" u2="&#xfc;" k="50" />
    <hkern u1="&#xce;" u2="&#xfb;" k="50" />
    <hkern u1="&#xce;" u2="&#xfa;" k="50" />
    <hkern u1="&#xce;" u2="&#xf9;" k="50" />
    <hkern u1="&#xce;" u2="&#xc5;" k="50" />
    <hkern u1="&#xce;" u2="&#xc4;" k="50" />
    <hkern u1="&#xce;" u2="&#xc3;" k="50" />
    <hkern u1="&#xce;" u2="&#xc2;" k="50" />
    <hkern u1="&#xce;" u2="&#xc1;" k="50" />
    <hkern u1="&#xce;" u2="&#xc0;" k="50" />
    <hkern u1="&#xce;" u2="y" k="75" />
    <hkern u1="&#xce;" u2="w" k="75" />
    <hkern u1="&#xce;" u2="v" k="75" />
    <hkern u1="&#xce;" u2="u" k="50" />
    <hkern u1="&#xce;" u2="t" k="50" />
    <hkern u1="&#xce;" u2="f" k="75" />
    <hkern u1="&#xce;" u2="T" k="50" />
    <hkern u1="&#xce;" u2="J" k="75" />
    <hkern u1="&#xce;" u2="A" k="50" />
    <hkern u1="&#xcf;" u2="&#xff;" k="75" />
    <hkern u1="&#xcf;" u2="&#xfd;" k="75" />
    <hkern u1="&#xcf;" u2="&#xfc;" k="50" />
    <hkern u1="&#xcf;" u2="&#xfb;" k="50" />
    <hkern u1="&#xcf;" u2="&#xfa;" k="50" />
    <hkern u1="&#xcf;" u2="&#xf9;" k="50" />
    <hkern u1="&#xcf;" u2="&#xc5;" k="50" />
    <hkern u1="&#xcf;" u2="&#xc4;" k="50" />
    <hkern u1="&#xcf;" u2="&#xc3;" k="50" />
    <hkern u1="&#xcf;" u2="&#xc2;" k="50" />
    <hkern u1="&#xcf;" u2="&#xc1;" k="50" />
    <hkern u1="&#xcf;" u2="&#xc0;" k="50" />
    <hkern u1="&#xcf;" u2="y" k="75" />
    <hkern u1="&#xcf;" u2="w" k="75" />
    <hkern u1="&#xcf;" u2="v" k="75" />
    <hkern u1="&#xcf;" u2="u" k="50" />
    <hkern u1="&#xcf;" u2="t" k="50" />
    <hkern u1="&#xcf;" u2="f" k="75" />
    <hkern u1="&#xcf;" u2="T" k="50" />
    <hkern u1="&#xcf;" u2="J" k="75" />
    <hkern u1="&#xcf;" u2="A" k="50" />
    <hkern u1="&#xd0;" u2="&#xff;" k="50" />
    <hkern u1="&#xd0;" u2="&#xfd;" k="50" />
    <hkern u1="&#xd0;" u2="&#xf8;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xf6;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xf5;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xf4;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xf3;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xf2;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xf0;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xeb;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xea;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xe9;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xe8;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xe7;" k="-25" />
    <hkern u1="&#xd0;" u2="&#xe5;" k="50" />
    <hkern u1="&#xd0;" u2="&#xe4;" k="50" />
    <hkern u1="&#xd0;" u2="&#xe3;" k="50" />
    <hkern u1="&#xd0;" u2="&#xe2;" k="50" />
    <hkern u1="&#xd0;" u2="&#xe1;" k="50" />
    <hkern u1="&#xd0;" u2="&#xe0;" k="50" />
    <hkern u1="&#xd0;" u2="&#x178;" k="275" />
    <hkern u1="&#xd0;" u2="&#xdd;" k="275" />
    <hkern u1="&#xd0;" u2="&#xd8;" k="-50" />
    <hkern u1="&#xd0;" u2="&#xd6;" k="-50" />
    <hkern u1="&#xd0;" u2="&#xd5;" k="-50" />
    <hkern u1="&#xd0;" u2="&#xd4;" k="-50" />
    <hkern u1="&#xd0;" u2="&#xd3;" k="-50" />
    <hkern u1="&#xd0;" u2="&#xd2;" k="-50" />
    <hkern u1="&#xd0;" u2="&#xc7;" k="-50" />
    <hkern u1="&#xd0;" u2="&#xc5;" k="225" />
    <hkern u1="&#xd0;" u2="&#xc4;" k="225" />
    <hkern u1="&#xd0;" u2="&#xc3;" k="225" />
    <hkern u1="&#xd0;" u2="&#xc2;" k="225" />
    <hkern u1="&#xd0;" u2="&#xc1;" k="225" />
    <hkern u1="&#xd0;" u2="&#xc0;" k="225" />
    <hkern u1="&#xd0;" u2="z" k="125" />
    <hkern u1="&#xd0;" u2="y" k="50" />
    <hkern u1="&#xd0;" u2="x" k="75" />
    <hkern u1="&#xd0;" u2="w" k="75" />
    <hkern u1="&#xd0;" u2="v" k="75" />
    <hkern u1="&#xd0;" u2="q" k="-25" />
    <hkern u1="&#xd0;" u2="o" k="-25" />
    <hkern u1="&#xd0;" u2="j" k="25" />
    <hkern u1="&#xd0;" u2="e" k="-25" />
    <hkern u1="&#xd0;" u2="d" k="-25" />
    <hkern u1="&#xd0;" u2="c" k="-25" />
    <hkern u1="&#xd0;" u2="a" k="50" />
    <hkern u1="&#xd0;" u2="\" k="250" />
    <hkern u1="&#xd0;" u2="Z" k="150" />
    <hkern u1="&#xd0;" u2="Y" k="275" />
    <hkern u1="&#xd0;" u2="X" k="250" />
    <hkern u1="&#xd0;" u2="W" k="175" />
    <hkern u1="&#xd0;" u2="V" k="225" />
    <hkern u1="&#xd0;" u2="T" k="225" />
    <hkern u1="&#xd0;" u2="S" k="75" />
    <hkern u1="&#xd0;" u2="Q" k="-50" />
    <hkern u1="&#xd0;" u2="O" k="-50" />
    <hkern u1="&#xd0;" u2="J" k="225" />
    <hkern u1="&#xd0;" u2="C" k="-50" />
    <hkern u1="&#xd0;" u2="A" k="225" />
    <hkern u1="&#xd0;" u2="&#x37;" k="150" />
    <hkern u1="&#xd1;" u2="&#xff;" k="75" />
    <hkern u1="&#xd1;" u2="&#xfd;" k="75" />
    <hkern u1="&#xd1;" u2="&#xfc;" k="50" />
    <hkern u1="&#xd1;" u2="&#xfb;" k="50" />
    <hkern u1="&#xd1;" u2="&#xfa;" k="50" />
    <hkern u1="&#xd1;" u2="&#xf9;" k="50" />
    <hkern u1="&#xd1;" u2="&#xc5;" k="50" />
    <hkern u1="&#xd1;" u2="&#xc4;" k="50" />
    <hkern u1="&#xd1;" u2="&#xc3;" k="50" />
    <hkern u1="&#xd1;" u2="&#xc2;" k="50" />
    <hkern u1="&#xd1;" u2="&#xc1;" k="50" />
    <hkern u1="&#xd1;" u2="&#xc0;" k="50" />
    <hkern u1="&#xd1;" u2="y" k="75" />
    <hkern u1="&#xd1;" u2="x" k="75" />
    <hkern u1="&#xd1;" u2="w" k="75" />
    <hkern u1="&#xd1;" u2="v" k="75" />
    <hkern u1="&#xd1;" u2="u" k="50" />
    <hkern u1="&#xd1;" u2="t" k="50" />
    <hkern u1="&#xd1;" u2="j" k="75" />
    <hkern u1="&#xd1;" u2="f" k="75" />
    <hkern u1="&#xd1;" u2="J" k="75" />
    <hkern u1="&#xd1;" u2="A" k="50" />
    <hkern u1="&#xd2;" u2="&#xff;" k="50" />
    <hkern u1="&#xd2;" u2="&#xfd;" k="50" />
    <hkern u1="&#xd2;" u2="&#xf8;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xf6;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xf5;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xf4;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xf3;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xf2;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xf0;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xeb;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xea;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xe9;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xe8;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xe7;" k="-50" />
    <hkern u1="&#xd2;" u2="&#x178;" k="200" />
    <hkern u1="&#xd2;" u2="&#xdd;" k="200" />
    <hkern u1="&#xd2;" u2="&#xd8;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xd6;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xd5;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xd4;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xd3;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xd2;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xc7;" k="-50" />
    <hkern u1="&#xd2;" u2="&#xc5;" k="225" />
    <hkern u1="&#xd2;" u2="&#xc4;" k="225" />
    <hkern u1="&#xd2;" u2="&#xc3;" k="225" />
    <hkern u1="&#xd2;" u2="&#xc2;" k="225" />
    <hkern u1="&#xd2;" u2="&#xc1;" k="225" />
    <hkern u1="&#xd2;" u2="&#xc0;" k="225" />
    <hkern u1="&#xd2;" u2="z" k="75" />
    <hkern u1="&#xd2;" u2="y" k="50" />
    <hkern u1="&#xd2;" u2="x" k="50" />
    <hkern u1="&#xd2;" u2="w" k="50" />
    <hkern u1="&#xd2;" u2="v" k="50" />
    <hkern u1="&#xd2;" u2="s" k="-50" />
    <hkern u1="&#xd2;" u2="q" k="-50" />
    <hkern u1="&#xd2;" u2="o" k="-50" />
    <hkern u1="&#xd2;" u2="j" k="50" />
    <hkern u1="&#xd2;" u2="f" k="50" />
    <hkern u1="&#xd2;" u2="e" k="-50" />
    <hkern u1="&#xd2;" u2="d" k="-50" />
    <hkern u1="&#xd2;" u2="c" k="-50" />
    <hkern u1="&#xd2;" u2="\" k="250" />
    <hkern u1="&#xd2;" u2="Z" k="75" />
    <hkern u1="&#xd2;" u2="Y" k="200" />
    <hkern u1="&#xd2;" u2="X" k="125" />
    <hkern u1="&#xd2;" u2="W" k="100" />
    <hkern u1="&#xd2;" u2="V" k="125" />
    <hkern u1="&#xd2;" u2="T" k="100" />
    <hkern u1="&#xd2;" u2="S" k="50" />
    <hkern u1="&#xd2;" u2="Q" k="-50" />
    <hkern u1="&#xd2;" u2="O" k="-50" />
    <hkern u1="&#xd2;" u2="J" k="125" />
    <hkern u1="&#xd2;" u2="G" k="-50" />
    <hkern u1="&#xd2;" u2="C" k="-50" />
    <hkern u1="&#xd2;" u2="A" k="225" />
    <hkern u1="&#xd2;" u2="&#x37;" k="150" />
    <hkern u1="&#xd2;" u2="&#x32;" k="125" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="150" />
    <hkern u1="&#xd3;" u2="&#xff;" k="50" />
    <hkern u1="&#xd3;" u2="&#xfd;" k="50" />
    <hkern u1="&#xd3;" u2="&#xf8;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xf6;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xf5;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xf4;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xf3;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xf2;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xf0;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xeb;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xea;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xe9;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xe8;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xe7;" k="-50" />
    <hkern u1="&#xd3;" u2="&#x178;" k="200" />
    <hkern u1="&#xd3;" u2="&#xdd;" k="200" />
    <hkern u1="&#xd3;" u2="&#xd8;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xd6;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xd5;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xd4;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xd3;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xd2;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xc7;" k="-50" />
    <hkern u1="&#xd3;" u2="&#xc5;" k="225" />
    <hkern u1="&#xd3;" u2="&#xc4;" k="225" />
    <hkern u1="&#xd3;" u2="&#xc3;" k="225" />
    <hkern u1="&#xd3;" u2="&#xc2;" k="225" />
    <hkern u1="&#xd3;" u2="&#xc1;" k="225" />
    <hkern u1="&#xd3;" u2="&#xc0;" k="225" />
    <hkern u1="&#xd3;" u2="z" k="75" />
    <hkern u1="&#xd3;" u2="y" k="50" />
    <hkern u1="&#xd3;" u2="x" k="50" />
    <hkern u1="&#xd3;" u2="w" k="50" />
    <hkern u1="&#xd3;" u2="v" k="50" />
    <hkern u1="&#xd3;" u2="s" k="-50" />
    <hkern u1="&#xd3;" u2="q" k="-50" />
    <hkern u1="&#xd3;" u2="o" k="-50" />
    <hkern u1="&#xd3;" u2="j" k="50" />
    <hkern u1="&#xd3;" u2="f" k="50" />
    <hkern u1="&#xd3;" u2="e" k="-50" />
    <hkern u1="&#xd3;" u2="d" k="-50" />
    <hkern u1="&#xd3;" u2="c" k="-50" />
    <hkern u1="&#xd3;" u2="\" k="250" />
    <hkern u1="&#xd3;" u2="Z" k="75" />
    <hkern u1="&#xd3;" u2="Y" k="200" />
    <hkern u1="&#xd3;" u2="X" k="125" />
    <hkern u1="&#xd3;" u2="W" k="100" />
    <hkern u1="&#xd3;" u2="V" k="125" />
    <hkern u1="&#xd3;" u2="T" k="100" />
    <hkern u1="&#xd3;" u2="S" k="50" />
    <hkern u1="&#xd3;" u2="Q" k="-50" />
    <hkern u1="&#xd3;" u2="O" k="-50" />
    <hkern u1="&#xd3;" u2="J" k="125" />
    <hkern u1="&#xd3;" u2="G" k="-50" />
    <hkern u1="&#xd3;" u2="C" k="-50" />
    <hkern u1="&#xd3;" u2="A" k="225" />
    <hkern u1="&#xd3;" u2="&#x37;" k="150" />
    <hkern u1="&#xd3;" u2="&#x32;" k="125" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="150" />
    <hkern u1="&#xd4;" u2="&#xff;" k="50" />
    <hkern u1="&#xd4;" u2="&#xfd;" k="50" />
    <hkern u1="&#xd4;" u2="&#xf8;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xf6;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xf5;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xf4;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xf3;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xf2;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xf0;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xeb;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xea;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xe9;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xe8;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xe7;" k="-50" />
    <hkern u1="&#xd4;" u2="&#x178;" k="200" />
    <hkern u1="&#xd4;" u2="&#xdd;" k="200" />
    <hkern u1="&#xd4;" u2="&#xd8;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xd6;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xd5;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xd4;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xd3;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xd2;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xc7;" k="-50" />
    <hkern u1="&#xd4;" u2="&#xc5;" k="225" />
    <hkern u1="&#xd4;" u2="&#xc4;" k="225" />
    <hkern u1="&#xd4;" u2="&#xc3;" k="225" />
    <hkern u1="&#xd4;" u2="&#xc2;" k="225" />
    <hkern u1="&#xd4;" u2="&#xc1;" k="225" />
    <hkern u1="&#xd4;" u2="&#xc0;" k="225" />
    <hkern u1="&#xd4;" u2="z" k="75" />
    <hkern u1="&#xd4;" u2="y" k="50" />
    <hkern u1="&#xd4;" u2="x" k="50" />
    <hkern u1="&#xd4;" u2="w" k="50" />
    <hkern u1="&#xd4;" u2="v" k="50" />
    <hkern u1="&#xd4;" u2="s" k="-50" />
    <hkern u1="&#xd4;" u2="q" k="-50" />
    <hkern u1="&#xd4;" u2="o" k="-50" />
    <hkern u1="&#xd4;" u2="j" k="50" />
    <hkern u1="&#xd4;" u2="f" k="50" />
    <hkern u1="&#xd4;" u2="e" k="-50" />
    <hkern u1="&#xd4;" u2="d" k="-50" />
    <hkern u1="&#xd4;" u2="c" k="-50" />
    <hkern u1="&#xd4;" u2="\" k="250" />
    <hkern u1="&#xd4;" u2="Z" k="75" />
    <hkern u1="&#xd4;" u2="Y" k="200" />
    <hkern u1="&#xd4;" u2="X" k="125" />
    <hkern u1="&#xd4;" u2="W" k="100" />
    <hkern u1="&#xd4;" u2="V" k="125" />
    <hkern u1="&#xd4;" u2="T" k="100" />
    <hkern u1="&#xd4;" u2="S" k="50" />
    <hkern u1="&#xd4;" u2="Q" k="-50" />
    <hkern u1="&#xd4;" u2="O" k="-50" />
    <hkern u1="&#xd4;" u2="J" k="125" />
    <hkern u1="&#xd4;" u2="G" k="-50" />
    <hkern u1="&#xd4;" u2="C" k="-50" />
    <hkern u1="&#xd4;" u2="A" k="225" />
    <hkern u1="&#xd4;" u2="&#x37;" k="150" />
    <hkern u1="&#xd4;" u2="&#x32;" k="125" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="150" />
    <hkern u1="&#xd5;" u2="&#xff;" k="50" />
    <hkern u1="&#xd5;" u2="&#xfd;" k="50" />
    <hkern u1="&#xd5;" u2="&#xf8;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xf6;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xf5;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xf4;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xf3;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xf2;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xf0;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xeb;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xea;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xe9;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xe8;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xe7;" k="-50" />
    <hkern u1="&#xd5;" u2="&#x178;" k="200" />
    <hkern u1="&#xd5;" u2="&#xdd;" k="200" />
    <hkern u1="&#xd5;" u2="&#xd8;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xd6;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xd5;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xd4;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xd3;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xd2;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xc7;" k="-50" />
    <hkern u1="&#xd5;" u2="&#xc5;" k="225" />
    <hkern u1="&#xd5;" u2="&#xc4;" k="225" />
    <hkern u1="&#xd5;" u2="&#xc3;" k="225" />
    <hkern u1="&#xd5;" u2="&#xc2;" k="225" />
    <hkern u1="&#xd5;" u2="&#xc1;" k="225" />
    <hkern u1="&#xd5;" u2="&#xc0;" k="225" />
    <hkern u1="&#xd5;" u2="z" k="75" />
    <hkern u1="&#xd5;" u2="y" k="50" />
    <hkern u1="&#xd5;" u2="x" k="50" />
    <hkern u1="&#xd5;" u2="w" k="50" />
    <hkern u1="&#xd5;" u2="v" k="50" />
    <hkern u1="&#xd5;" u2="s" k="-50" />
    <hkern u1="&#xd5;" u2="q" k="-50" />
    <hkern u1="&#xd5;" u2="o" k="-50" />
    <hkern u1="&#xd5;" u2="j" k="50" />
    <hkern u1="&#xd5;" u2="f" k="50" />
    <hkern u1="&#xd5;" u2="e" k="-50" />
    <hkern u1="&#xd5;" u2="d" k="-50" />
    <hkern u1="&#xd5;" u2="c" k="-50" />
    <hkern u1="&#xd5;" u2="\" k="250" />
    <hkern u1="&#xd5;" u2="Z" k="75" />
    <hkern u1="&#xd5;" u2="Y" k="200" />
    <hkern u1="&#xd5;" u2="X" k="125" />
    <hkern u1="&#xd5;" u2="W" k="100" />
    <hkern u1="&#xd5;" u2="V" k="125" />
    <hkern u1="&#xd5;" u2="T" k="100" />
    <hkern u1="&#xd5;" u2="S" k="50" />
    <hkern u1="&#xd5;" u2="Q" k="-50" />
    <hkern u1="&#xd5;" u2="O" k="-50" />
    <hkern u1="&#xd5;" u2="J" k="125" />
    <hkern u1="&#xd5;" u2="G" k="-50" />
    <hkern u1="&#xd5;" u2="C" k="-50" />
    <hkern u1="&#xd5;" u2="A" k="225" />
    <hkern u1="&#xd5;" u2="&#x37;" k="150" />
    <hkern u1="&#xd5;" u2="&#x32;" k="125" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="150" />
    <hkern u1="&#xd6;" u2="&#xff;" k="50" />
    <hkern u1="&#xd6;" u2="&#xfd;" k="50" />
    <hkern u1="&#xd6;" u2="&#xf8;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xf6;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xf5;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xf4;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xf3;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xf2;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xf0;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xeb;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xea;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xe9;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xe8;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xe7;" k="-50" />
    <hkern u1="&#xd6;" u2="&#x178;" k="200" />
    <hkern u1="&#xd6;" u2="&#xdd;" k="200" />
    <hkern u1="&#xd6;" u2="&#xd8;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xd6;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xd5;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xd4;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xd3;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xd2;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xc7;" k="-50" />
    <hkern u1="&#xd6;" u2="&#xc5;" k="225" />
    <hkern u1="&#xd6;" u2="&#xc4;" k="225" />
    <hkern u1="&#xd6;" u2="&#xc3;" k="225" />
    <hkern u1="&#xd6;" u2="&#xc2;" k="225" />
    <hkern u1="&#xd6;" u2="&#xc1;" k="225" />
    <hkern u1="&#xd6;" u2="&#xc0;" k="225" />
    <hkern u1="&#xd6;" u2="z" k="75" />
    <hkern u1="&#xd6;" u2="y" k="50" />
    <hkern u1="&#xd6;" u2="x" k="50" />
    <hkern u1="&#xd6;" u2="w" k="50" />
    <hkern u1="&#xd6;" u2="v" k="50" />
    <hkern u1="&#xd6;" u2="s" k="-50" />
    <hkern u1="&#xd6;" u2="q" k="-50" />
    <hkern u1="&#xd6;" u2="o" k="-50" />
    <hkern u1="&#xd6;" u2="j" k="50" />
    <hkern u1="&#xd6;" u2="f" k="50" />
    <hkern u1="&#xd6;" u2="e" k="-50" />
    <hkern u1="&#xd6;" u2="d" k="-50" />
    <hkern u1="&#xd6;" u2="c" k="-50" />
    <hkern u1="&#xd6;" u2="\" k="250" />
    <hkern u1="&#xd6;" u2="Z" k="75" />
    <hkern u1="&#xd6;" u2="Y" k="200" />
    <hkern u1="&#xd6;" u2="X" k="125" />
    <hkern u1="&#xd6;" u2="W" k="100" />
    <hkern u1="&#xd6;" u2="V" k="125" />
    <hkern u1="&#xd6;" u2="T" k="100" />
    <hkern u1="&#xd6;" u2="S" k="50" />
    <hkern u1="&#xd6;" u2="Q" k="-50" />
    <hkern u1="&#xd6;" u2="O" k="-50" />
    <hkern u1="&#xd6;" u2="J" k="125" />
    <hkern u1="&#xd6;" u2="G" k="-50" />
    <hkern u1="&#xd6;" u2="C" k="-50" />
    <hkern u1="&#xd6;" u2="A" k="225" />
    <hkern u1="&#xd6;" u2="&#x37;" k="150" />
    <hkern u1="&#xd6;" u2="&#x32;" k="125" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="150" />
    <hkern u1="&#xd8;" u2="&#xff;" k="50" />
    <hkern u1="&#xd8;" u2="&#xfd;" k="50" />
    <hkern u1="&#xd8;" u2="&#xf8;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xf6;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xf5;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xf4;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xf3;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xf2;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xf0;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xeb;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xea;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xe9;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xe8;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xe7;" k="-50" />
    <hkern u1="&#xd8;" u2="&#x178;" k="200" />
    <hkern u1="&#xd8;" u2="&#xdd;" k="200" />
    <hkern u1="&#xd8;" u2="&#xd8;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xd6;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xd5;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xd4;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xd3;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xd2;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xc7;" k="-50" />
    <hkern u1="&#xd8;" u2="&#xc5;" k="225" />
    <hkern u1="&#xd8;" u2="&#xc4;" k="225" />
    <hkern u1="&#xd8;" u2="&#xc3;" k="225" />
    <hkern u1="&#xd8;" u2="&#xc2;" k="225" />
    <hkern u1="&#xd8;" u2="&#xc1;" k="225" />
    <hkern u1="&#xd8;" u2="&#xc0;" k="225" />
    <hkern u1="&#xd8;" u2="z" k="75" />
    <hkern u1="&#xd8;" u2="y" k="50" />
    <hkern u1="&#xd8;" u2="x" k="50" />
    <hkern u1="&#xd8;" u2="w" k="50" />
    <hkern u1="&#xd8;" u2="v" k="50" />
    <hkern u1="&#xd8;" u2="s" k="-50" />
    <hkern u1="&#xd8;" u2="q" k="-50" />
    <hkern u1="&#xd8;" u2="o" k="-50" />
    <hkern u1="&#xd8;" u2="j" k="50" />
    <hkern u1="&#xd8;" u2="f" k="50" />
    <hkern u1="&#xd8;" u2="e" k="-50" />
    <hkern u1="&#xd8;" u2="d" k="-50" />
    <hkern u1="&#xd8;" u2="c" k="-50" />
    <hkern u1="&#xd8;" u2="\" k="250" />
    <hkern u1="&#xd8;" u2="Z" k="75" />
    <hkern u1="&#xd8;" u2="Y" k="200" />
    <hkern u1="&#xd8;" u2="X" k="125" />
    <hkern u1="&#xd8;" u2="W" k="100" />
    <hkern u1="&#xd8;" u2="V" k="125" />
    <hkern u1="&#xd8;" u2="T" k="100" />
    <hkern u1="&#xd8;" u2="S" k="50" />
    <hkern u1="&#xd8;" u2="Q" k="-50" />
    <hkern u1="&#xd8;" u2="O" k="-50" />
    <hkern u1="&#xd8;" u2="J" k="125" />
    <hkern u1="&#xd8;" u2="G" k="-50" />
    <hkern u1="&#xd8;" u2="C" k="-50" />
    <hkern u1="&#xd8;" u2="A" k="225" />
    <hkern u1="&#xd8;" u2="&#x37;" k="150" />
    <hkern u1="&#xd8;" u2="&#x32;" k="125" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="150" />
    <hkern u1="&#xd9;" u2="&#xff;" k="75" />
    <hkern u1="&#xd9;" u2="&#xfd;" k="75" />
    <hkern u1="&#xd9;" u2="&#xfc;" k="50" />
    <hkern u1="&#xd9;" u2="&#xfb;" k="50" />
    <hkern u1="&#xd9;" u2="&#xfa;" k="50" />
    <hkern u1="&#xd9;" u2="&#xf9;" k="50" />
    <hkern u1="&#xd9;" u2="&#xe5;" k="50" />
    <hkern u1="&#xd9;" u2="&#xe4;" k="50" />
    <hkern u1="&#xd9;" u2="&#xe3;" k="50" />
    <hkern u1="&#xd9;" u2="&#xe2;" k="50" />
    <hkern u1="&#xd9;" u2="&#xe1;" k="50" />
    <hkern u1="&#xd9;" u2="&#xe0;" k="50" />
    <hkern u1="&#xd9;" u2="&#x178;" k="50" />
    <hkern u1="&#xd9;" u2="&#xdd;" k="50" />
    <hkern u1="&#xd9;" u2="&#xc5;" k="175" />
    <hkern u1="&#xd9;" u2="&#xc4;" k="175" />
    <hkern u1="&#xd9;" u2="&#xc3;" k="175" />
    <hkern u1="&#xd9;" u2="&#xc2;" k="175" />
    <hkern u1="&#xd9;" u2="&#xc1;" k="175" />
    <hkern u1="&#xd9;" u2="&#xc0;" k="175" />
    <hkern u1="&#xd9;" u2="z" k="75" />
    <hkern u1="&#xd9;" u2="y" k="75" />
    <hkern u1="&#xd9;" u2="x" k="75" />
    <hkern u1="&#xd9;" u2="w" k="75" />
    <hkern u1="&#xd9;" u2="v" k="75" />
    <hkern u1="&#xd9;" u2="u" k="50" />
    <hkern u1="&#xd9;" u2="t" k="50" />
    <hkern u1="&#xd9;" u2="j" k="75" />
    <hkern u1="&#xd9;" u2="f" k="75" />
    <hkern u1="&#xd9;" u2="a" k="50" />
    <hkern u1="&#xd9;" u2="Z" k="50" />
    <hkern u1="&#xd9;" u2="Y" k="50" />
    <hkern u1="&#xd9;" u2="X" k="50" />
    <hkern u1="&#xd9;" u2="W" k="50" />
    <hkern u1="&#xd9;" u2="V" k="50" />
    <hkern u1="&#xd9;" u2="J" k="150" />
    <hkern u1="&#xd9;" u2="A" k="175" />
    <hkern u1="&#xd9;" u2="&#x37;" k="100" />
    <hkern u1="&#xd9;" u2="&#x32;" k="100" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="125" />
    <hkern u1="&#xda;" u2="&#xff;" k="75" />
    <hkern u1="&#xda;" u2="&#xfd;" k="75" />
    <hkern u1="&#xda;" u2="&#xfc;" k="50" />
    <hkern u1="&#xda;" u2="&#xfb;" k="50" />
    <hkern u1="&#xda;" u2="&#xfa;" k="50" />
    <hkern u1="&#xda;" u2="&#xf9;" k="50" />
    <hkern u1="&#xda;" u2="&#xe5;" k="50" />
    <hkern u1="&#xda;" u2="&#xe4;" k="50" />
    <hkern u1="&#xda;" u2="&#xe3;" k="50" />
    <hkern u1="&#xda;" u2="&#xe2;" k="50" />
    <hkern u1="&#xda;" u2="&#xe1;" k="50" />
    <hkern u1="&#xda;" u2="&#xe0;" k="50" />
    <hkern u1="&#xda;" u2="&#x178;" k="50" />
    <hkern u1="&#xda;" u2="&#xdd;" k="50" />
    <hkern u1="&#xda;" u2="&#xc5;" k="175" />
    <hkern u1="&#xda;" u2="&#xc4;" k="175" />
    <hkern u1="&#xda;" u2="&#xc3;" k="175" />
    <hkern u1="&#xda;" u2="&#xc2;" k="175" />
    <hkern u1="&#xda;" u2="&#xc1;" k="175" />
    <hkern u1="&#xda;" u2="&#xc0;" k="175" />
    <hkern u1="&#xda;" u2="z" k="75" />
    <hkern u1="&#xda;" u2="y" k="75" />
    <hkern u1="&#xda;" u2="x" k="75" />
    <hkern u1="&#xda;" u2="w" k="75" />
    <hkern u1="&#xda;" u2="v" k="75" />
    <hkern u1="&#xda;" u2="u" k="50" />
    <hkern u1="&#xda;" u2="t" k="50" />
    <hkern u1="&#xda;" u2="j" k="75" />
    <hkern u1="&#xda;" u2="f" k="75" />
    <hkern u1="&#xda;" u2="a" k="50" />
    <hkern u1="&#xda;" u2="Z" k="50" />
    <hkern u1="&#xda;" u2="Y" k="50" />
    <hkern u1="&#xda;" u2="X" k="50" />
    <hkern u1="&#xda;" u2="W" k="50" />
    <hkern u1="&#xda;" u2="V" k="50" />
    <hkern u1="&#xda;" u2="J" k="150" />
    <hkern u1="&#xda;" u2="A" k="175" />
    <hkern u1="&#xda;" u2="&#x37;" k="100" />
    <hkern u1="&#xda;" u2="&#x32;" k="100" />
    <hkern u1="&#xda;" u2="&#x2f;" k="125" />
    <hkern u1="&#xdb;" u2="&#xff;" k="75" />
    <hkern u1="&#xdb;" u2="&#xfd;" k="75" />
    <hkern u1="&#xdb;" u2="&#xfc;" k="50" />
    <hkern u1="&#xdb;" u2="&#xfb;" k="50" />
    <hkern u1="&#xdb;" u2="&#xfa;" k="50" />
    <hkern u1="&#xdb;" u2="&#xf9;" k="50" />
    <hkern u1="&#xdb;" u2="&#xe5;" k="50" />
    <hkern u1="&#xdb;" u2="&#xe4;" k="50" />
    <hkern u1="&#xdb;" u2="&#xe3;" k="50" />
    <hkern u1="&#xdb;" u2="&#xe2;" k="50" />
    <hkern u1="&#xdb;" u2="&#xe1;" k="50" />
    <hkern u1="&#xdb;" u2="&#xe0;" k="50" />
    <hkern u1="&#xdb;" u2="&#x178;" k="50" />
    <hkern u1="&#xdb;" u2="&#xdd;" k="50" />
    <hkern u1="&#xdb;" u2="&#xc5;" k="175" />
    <hkern u1="&#xdb;" u2="&#xc4;" k="175" />
    <hkern u1="&#xdb;" u2="&#xc3;" k="175" />
    <hkern u1="&#xdb;" u2="&#xc2;" k="175" />
    <hkern u1="&#xdb;" u2="&#xc1;" k="175" />
    <hkern u1="&#xdb;" u2="&#xc0;" k="175" />
    <hkern u1="&#xdb;" u2="z" k="75" />
    <hkern u1="&#xdb;" u2="y" k="75" />
    <hkern u1="&#xdb;" u2="x" k="75" />
    <hkern u1="&#xdb;" u2="w" k="75" />
    <hkern u1="&#xdb;" u2="v" k="75" />
    <hkern u1="&#xdb;" u2="u" k="50" />
    <hkern u1="&#xdb;" u2="t" k="50" />
    <hkern u1="&#xdb;" u2="j" k="75" />
    <hkern u1="&#xdb;" u2="f" k="75" />
    <hkern u1="&#xdb;" u2="a" k="50" />
    <hkern u1="&#xdb;" u2="Z" k="50" />
    <hkern u1="&#xdb;" u2="Y" k="50" />
    <hkern u1="&#xdb;" u2="X" k="50" />
    <hkern u1="&#xdb;" u2="W" k="50" />
    <hkern u1="&#xdb;" u2="V" k="50" />
    <hkern u1="&#xdb;" u2="J" k="150" />
    <hkern u1="&#xdb;" u2="A" k="175" />
    <hkern u1="&#xdb;" u2="&#x37;" k="100" />
    <hkern u1="&#xdb;" u2="&#x32;" k="100" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="125" />
    <hkern u1="&#xdc;" u2="&#xff;" k="75" />
    <hkern u1="&#xdc;" u2="&#xfd;" k="75" />
    <hkern u1="&#xdc;" u2="&#xfc;" k="50" />
    <hkern u1="&#xdc;" u2="&#xfb;" k="50" />
    <hkern u1="&#xdc;" u2="&#xfa;" k="50" />
    <hkern u1="&#xdc;" u2="&#xf9;" k="50" />
    <hkern u1="&#xdc;" u2="&#xe5;" k="50" />
    <hkern u1="&#xdc;" u2="&#xe4;" k="50" />
    <hkern u1="&#xdc;" u2="&#xe3;" k="50" />
    <hkern u1="&#xdc;" u2="&#xe2;" k="50" />
    <hkern u1="&#xdc;" u2="&#xe1;" k="50" />
    <hkern u1="&#xdc;" u2="&#xe0;" k="50" />
    <hkern u1="&#xdc;" u2="&#x178;" k="50" />
    <hkern u1="&#xdc;" u2="&#xdd;" k="50" />
    <hkern u1="&#xdc;" u2="&#xc5;" k="175" />
    <hkern u1="&#xdc;" u2="&#xc4;" k="175" />
    <hkern u1="&#xdc;" u2="&#xc3;" k="175" />
    <hkern u1="&#xdc;" u2="&#xc2;" k="175" />
    <hkern u1="&#xdc;" u2="&#xc1;" k="175" />
    <hkern u1="&#xdc;" u2="&#xc0;" k="175" />
    <hkern u1="&#xdc;" u2="z" k="75" />
    <hkern u1="&#xdc;" u2="y" k="75" />
    <hkern u1="&#xdc;" u2="x" k="75" />
    <hkern u1="&#xdc;" u2="w" k="75" />
    <hkern u1="&#xdc;" u2="v" k="75" />
    <hkern u1="&#xdc;" u2="u" k="50" />
    <hkern u1="&#xdc;" u2="t" k="50" />
    <hkern u1="&#xdc;" u2="j" k="75" />
    <hkern u1="&#xdc;" u2="f" k="75" />
    <hkern u1="&#xdc;" u2="a" k="50" />
    <hkern u1="&#xdc;" u2="Z" k="50" />
    <hkern u1="&#xdc;" u2="Y" k="50" />
    <hkern u1="&#xdc;" u2="X" k="50" />
    <hkern u1="&#xdc;" u2="W" k="50" />
    <hkern u1="&#xdc;" u2="V" k="50" />
    <hkern u1="&#xdc;" u2="J" k="150" />
    <hkern u1="&#xdc;" u2="A" k="175" />
    <hkern u1="&#xdc;" u2="&#x37;" k="100" />
    <hkern u1="&#xdc;" u2="&#x32;" k="100" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="125" />
    <hkern u1="&#xdd;" u2="&#xff;" k="475" />
    <hkern u1="&#xdd;" u2="&#xfd;" k="475" />
    <hkern u1="&#xdd;" u2="&#xfc;" k="400" />
    <hkern u1="&#xdd;" u2="&#xfb;" k="400" />
    <hkern u1="&#xdd;" u2="&#xfa;" k="400" />
    <hkern u1="&#xdd;" u2="&#xf9;" k="400" />
    <hkern u1="&#xdd;" u2="&#xf8;" k="525" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="525" />
    <hkern u1="&#xdd;" u2="&#xf5;" k="525" />
    <hkern u1="&#xdd;" u2="&#xf4;" k="525" />
    <hkern u1="&#xdd;" u2="&#xf3;" k="525" />
    <hkern u1="&#xdd;" u2="&#xf2;" k="525" />
    <hkern u1="&#xdd;" u2="&#xf1;" k="400" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="525" />
    <hkern u1="&#xdd;" u2="&#xef;" k="300" />
    <hkern u1="&#xdd;" u2="&#xee;" k="300" />
    <hkern u1="&#xdd;" u2="&#xed;" k="300" />
    <hkern u1="&#xdd;" u2="&#xec;" k="300" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="525" />
    <hkern u1="&#xdd;" u2="&#xea;" k="525" />
    <hkern u1="&#xdd;" u2="&#xe9;" k="525" />
    <hkern u1="&#xdd;" u2="&#xe8;" k="525" />
    <hkern u1="&#xdd;" u2="&#xe7;" k="525" />
    <hkern u1="&#xdd;" u2="&#xe5;" k="525" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="525" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="525" />
    <hkern u1="&#xdd;" u2="&#xe2;" k="525" />
    <hkern u1="&#xdd;" u2="&#xe1;" k="525" />
    <hkern u1="&#xdd;" u2="&#xe0;" k="525" />
    <hkern u1="&#xdd;" u2="&#x178;" k="50" />
    <hkern u1="&#xdd;" u2="&#xdd;" k="50" />
    <hkern u1="&#xdd;" u2="&#xdc;" k="50" />
    <hkern u1="&#xdd;" u2="&#xdb;" k="50" />
    <hkern u1="&#xdd;" u2="&#xda;" k="50" />
    <hkern u1="&#xdd;" u2="&#xd9;" k="50" />
    <hkern u1="&#xdd;" u2="&#xd8;" k="200" />
    <hkern u1="&#xdd;" u2="&#xd6;" k="200" />
    <hkern u1="&#xdd;" u2="&#xd5;" k="200" />
    <hkern u1="&#xdd;" u2="&#xd4;" k="200" />
    <hkern u1="&#xdd;" u2="&#xd3;" k="200" />
    <hkern u1="&#xdd;" u2="&#xd2;" k="200" />
    <hkern u1="&#xdd;" u2="&#xd0;" k="50" />
    <hkern u1="&#xdd;" u2="&#xcb;" k="50" />
    <hkern u1="&#xdd;" u2="&#xca;" k="50" />
    <hkern u1="&#xdd;" u2="&#xc9;" k="50" />
    <hkern u1="&#xdd;" u2="&#xc8;" k="50" />
    <hkern u1="&#xdd;" u2="&#xc7;" k="250" />
    <hkern u1="&#xdd;" u2="&#xc5;" k="600" />
    <hkern u1="&#xdd;" u2="&#xc4;" k="600" />
    <hkern u1="&#xdd;" u2="&#xc3;" k="600" />
    <hkern u1="&#xdd;" u2="&#xc2;" k="600" />
    <hkern u1="&#xdd;" u2="&#xc1;" k="600" />
    <hkern u1="&#xdd;" u2="&#xc0;" k="600" />
    <hkern u1="&#xdd;" u2="z" k="450" />
    <hkern u1="&#xdd;" u2="y" k="475" />
    <hkern u1="&#xdd;" u2="x" k="450" />
    <hkern u1="&#xdd;" u2="w" k="450" />
    <hkern u1="&#xdd;" u2="v" k="450" />
    <hkern u1="&#xdd;" u2="u" k="400" />
    <hkern u1="&#xdd;" u2="t" k="400" />
    <hkern u1="&#xdd;" u2="s" k="400" />
    <hkern u1="&#xdd;" u2="r" k="400" />
    <hkern u1="&#xdd;" u2="q" k="525" />
    <hkern u1="&#xdd;" u2="p" k="400" />
    <hkern u1="&#xdd;" u2="o" k="525" />
    <hkern u1="&#xdd;" u2="n" k="400" />
    <hkern u1="&#xdd;" u2="m" k="400" />
    <hkern u1="&#xdd;" u2="l" k="75" />
    <hkern u1="&#xdd;" u2="k" k="75" />
    <hkern u1="&#xdd;" u2="j" k="375" />
    <hkern u1="&#xdd;" u2="i" k="300" />
    <hkern u1="&#xdd;" u2="h" k="75" />
    <hkern u1="&#xdd;" u2="g" k="525" />
    <hkern u1="&#xdd;" u2="f" k="375" />
    <hkern u1="&#xdd;" u2="e" k="525" />
    <hkern u1="&#xdd;" u2="d" k="525" />
    <hkern u1="&#xdd;" u2="c" k="525" />
    <hkern u1="&#xdd;" u2="b" k="75" />
    <hkern u1="&#xdd;" u2="a" k="525" />
    <hkern u1="&#xdd;" u2="Z" k="125" />
    <hkern u1="&#xdd;" u2="Y" k="50" />
    <hkern u1="&#xdd;" u2="X" k="50" />
    <hkern u1="&#xdd;" u2="W" k="50" />
    <hkern u1="&#xdd;" u2="V" k="50" />
    <hkern u1="&#xdd;" u2="U" k="50" />
    <hkern u1="&#xdd;" u2="T" k="50" />
    <hkern u1="&#xdd;" u2="S" k="125" />
    <hkern u1="&#xdd;" u2="R" k="50" />
    <hkern u1="&#xdd;" u2="Q" k="200" />
    <hkern u1="&#xdd;" u2="P" k="50" />
    <hkern u1="&#xdd;" u2="O" k="200" />
    <hkern u1="&#xdd;" u2="K" k="50" />
    <hkern u1="&#xdd;" u2="J" k="600" />
    <hkern u1="&#xdd;" u2="H" k="50" />
    <hkern u1="&#xdd;" u2="G" k="300" />
    <hkern u1="&#xdd;" u2="F" k="50" />
    <hkern u1="&#xdd;" u2="E" k="50" />
    <hkern u1="&#xdd;" u2="D" k="50" />
    <hkern u1="&#xdd;" u2="C" k="250" />
    <hkern u1="&#xdd;" u2="B" k="50" />
    <hkern u1="&#xdd;" u2="A" k="600" />
    <hkern u1="&#xdd;" u2="&#x39;" k="225" />
    <hkern u1="&#xdd;" u2="&#x38;" k="225" />
    <hkern u1="&#xdd;" u2="&#x37;" k="150" />
    <hkern u1="&#xdd;" u2="&#x36;" k="275" />
    <hkern u1="&#xdd;" u2="&#x35;" k="225" />
    <hkern u1="&#xdd;" u2="&#x34;" k="650" />
    <hkern u1="&#xdd;" u2="&#x33;" k="225" />
    <hkern u1="&#xdd;" u2="&#x32;" k="225" />
    <hkern u1="&#xdd;" u2="&#x31;" k="100" />
    <hkern u1="&#xdd;" u2="&#x30;" k="225" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="600" />
    <hkern u1="&#xdd;" u2="&#x26;" k="325" />
    <hkern u1="&#x178;" u2="&#xff;" k="475" />
    <hkern u1="&#x178;" u2="&#xfd;" k="475" />
    <hkern u1="&#x178;" u2="&#xfc;" k="400" />
    <hkern u1="&#x178;" u2="&#xfb;" k="400" />
    <hkern u1="&#x178;" u2="&#xfa;" k="400" />
    <hkern u1="&#x178;" u2="&#xf9;" k="400" />
    <hkern u1="&#x178;" u2="&#xf8;" k="525" />
    <hkern u1="&#x178;" u2="&#xf6;" k="525" />
    <hkern u1="&#x178;" u2="&#xf5;" k="525" />
    <hkern u1="&#x178;" u2="&#xf4;" k="525" />
    <hkern u1="&#x178;" u2="&#xf3;" k="525" />
    <hkern u1="&#x178;" u2="&#xf2;" k="525" />
    <hkern u1="&#x178;" u2="&#xf1;" k="400" />
    <hkern u1="&#x178;" u2="&#xf0;" k="525" />
    <hkern u1="&#x178;" u2="&#xef;" k="300" />
    <hkern u1="&#x178;" u2="&#xee;" k="300" />
    <hkern u1="&#x178;" u2="&#xed;" k="300" />
    <hkern u1="&#x178;" u2="&#xec;" k="300" />
    <hkern u1="&#x178;" u2="&#xeb;" k="525" />
    <hkern u1="&#x178;" u2="&#xea;" k="525" />
    <hkern u1="&#x178;" u2="&#xe9;" k="525" />
    <hkern u1="&#x178;" u2="&#xe8;" k="525" />
    <hkern u1="&#x178;" u2="&#xe7;" k="525" />
    <hkern u1="&#x178;" u2="&#xe5;" k="525" />
    <hkern u1="&#x178;" u2="&#xe4;" k="525" />
    <hkern u1="&#x178;" u2="&#xe3;" k="525" />
    <hkern u1="&#x178;" u2="&#xe2;" k="525" />
    <hkern u1="&#x178;" u2="&#xe1;" k="525" />
    <hkern u1="&#x178;" u2="&#xe0;" k="525" />
    <hkern u1="&#x178;" u2="&#x178;" k="50" />
    <hkern u1="&#x178;" u2="&#xdd;" k="50" />
    <hkern u1="&#x178;" u2="&#xdc;" k="50" />
    <hkern u1="&#x178;" u2="&#xdb;" k="50" />
    <hkern u1="&#x178;" u2="&#xda;" k="50" />
    <hkern u1="&#x178;" u2="&#xd9;" k="50" />
    <hkern u1="&#x178;" u2="&#xd8;" k="200" />
    <hkern u1="&#x178;" u2="&#xd6;" k="200" />
    <hkern u1="&#x178;" u2="&#xd5;" k="200" />
    <hkern u1="&#x178;" u2="&#xd4;" k="200" />
    <hkern u1="&#x178;" u2="&#xd3;" k="200" />
    <hkern u1="&#x178;" u2="&#xd2;" k="200" />
    <hkern u1="&#x178;" u2="&#xd0;" k="50" />
    <hkern u1="&#x178;" u2="&#xcb;" k="50" />
    <hkern u1="&#x178;" u2="&#xca;" k="50" />
    <hkern u1="&#x178;" u2="&#xc9;" k="50" />
    <hkern u1="&#x178;" u2="&#xc8;" k="50" />
    <hkern u1="&#x178;" u2="&#xc7;" k="250" />
    <hkern u1="&#x178;" u2="&#xc5;" k="600" />
    <hkern u1="&#x178;" u2="&#xc4;" k="600" />
    <hkern u1="&#x178;" u2="&#xc3;" k="600" />
    <hkern u1="&#x178;" u2="&#xc2;" k="600" />
    <hkern u1="&#x178;" u2="&#xc1;" k="600" />
    <hkern u1="&#x178;" u2="&#xc0;" k="600" />
    <hkern u1="&#x178;" u2="z" k="450" />
    <hkern u1="&#x178;" u2="y" k="475" />
    <hkern u1="&#x178;" u2="x" k="450" />
    <hkern u1="&#x178;" u2="w" k="450" />
    <hkern u1="&#x178;" u2="v" k="450" />
    <hkern u1="&#x178;" u2="u" k="400" />
    <hkern u1="&#x178;" u2="t" k="400" />
    <hkern u1="&#x178;" u2="s" k="400" />
    <hkern u1="&#x178;" u2="r" k="400" />
    <hkern u1="&#x178;" u2="q" k="525" />
    <hkern u1="&#x178;" u2="p" k="400" />
    <hkern u1="&#x178;" u2="o" k="525" />
    <hkern u1="&#x178;" u2="n" k="400" />
    <hkern u1="&#x178;" u2="m" k="400" />
    <hkern u1="&#x178;" u2="l" k="75" />
    <hkern u1="&#x178;" u2="k" k="75" />
    <hkern u1="&#x178;" u2="j" k="375" />
    <hkern u1="&#x178;" u2="i" k="300" />
    <hkern u1="&#x178;" u2="h" k="75" />
    <hkern u1="&#x178;" u2="g" k="525" />
    <hkern u1="&#x178;" u2="f" k="375" />
    <hkern u1="&#x178;" u2="e" k="525" />
    <hkern u1="&#x178;" u2="d" k="525" />
    <hkern u1="&#x178;" u2="c" k="525" />
    <hkern u1="&#x178;" u2="b" k="75" />
    <hkern u1="&#x178;" u2="a" k="525" />
    <hkern u1="&#x178;" u2="Z" k="125" />
    <hkern u1="&#x178;" u2="Y" k="50" />
    <hkern u1="&#x178;" u2="X" k="50" />
    <hkern u1="&#x178;" u2="W" k="50" />
    <hkern u1="&#x178;" u2="V" k="50" />
    <hkern u1="&#x178;" u2="U" k="50" />
    <hkern u1="&#x178;" u2="T" k="50" />
    <hkern u1="&#x178;" u2="S" k="125" />
    <hkern u1="&#x178;" u2="R" k="50" />
    <hkern u1="&#x178;" u2="Q" k="200" />
    <hkern u1="&#x178;" u2="P" k="50" />
    <hkern u1="&#x178;" u2="O" k="200" />
    <hkern u1="&#x178;" u2="K" k="50" />
    <hkern u1="&#x178;" u2="J" k="600" />
    <hkern u1="&#x178;" u2="H" k="50" />
    <hkern u1="&#x178;" u2="G" k="300" />
    <hkern u1="&#x178;" u2="F" k="50" />
    <hkern u1="&#x178;" u2="E" k="50" />
    <hkern u1="&#x178;" u2="D" k="50" />
    <hkern u1="&#x178;" u2="C" k="250" />
    <hkern u1="&#x178;" u2="B" k="50" />
    <hkern u1="&#x178;" u2="A" k="600" />
    <hkern u1="&#x178;" u2="&#x39;" k="225" />
    <hkern u1="&#x178;" u2="&#x38;" k="225" />
    <hkern u1="&#x178;" u2="&#x37;" k="150" />
    <hkern u1="&#x178;" u2="&#x36;" k="275" />
    <hkern u1="&#x178;" u2="&#x35;" k="225" />
    <hkern u1="&#x178;" u2="&#x34;" k="650" />
    <hkern u1="&#x178;" u2="&#x33;" k="225" />
    <hkern u1="&#x178;" u2="&#x32;" k="225" />
    <hkern u1="&#x178;" u2="&#x31;" k="100" />
    <hkern u1="&#x178;" u2="&#x30;" k="225" />
    <hkern u1="&#x178;" u2="&#x2f;" k="600" />
    <hkern u1="&#x178;" u2="&#x26;" k="325" />
    <hkern u1="&#xe0;" u2="&#xff;" k="150" />
    <hkern u1="&#xe0;" u2="&#xfd;" k="150" />
    <hkern u1="&#xe0;" u2="y" k="150" />
    <hkern u1="&#xe0;" u2="x" k="50" />
    <hkern u1="&#xe0;" u2="w" k="150" />
    <hkern u1="&#xe0;" u2="v" k="125" />
    <hkern u1="&#xe0;" u2="t" k="50" />
    <hkern u1="&#xe0;" u2="j" k="100" />
    <hkern u1="&#xe0;" u2="f" k="50" />
    <hkern u1="&#xe0;" u2="\" k="350" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="175" />
    <hkern u1="&#xe0;" u2="&#x39;" k="75" />
    <hkern u1="&#xe1;" u2="&#xff;" k="150" />
    <hkern u1="&#xe1;" u2="&#xfd;" k="150" />
    <hkern u1="&#xe1;" u2="y" k="150" />
    <hkern u1="&#xe1;" u2="x" k="50" />
    <hkern u1="&#xe1;" u2="w" k="150" />
    <hkern u1="&#xe1;" u2="v" k="125" />
    <hkern u1="&#xe1;" u2="t" k="50" />
    <hkern u1="&#xe1;" u2="j" k="100" />
    <hkern u1="&#xe1;" u2="f" k="50" />
    <hkern u1="&#xe1;" u2="\" k="350" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="175" />
    <hkern u1="&#xe1;" u2="&#x39;" k="75" />
    <hkern u1="&#xe2;" u2="&#xff;" k="150" />
    <hkern u1="&#xe2;" u2="&#xfd;" k="150" />
    <hkern u1="&#xe2;" u2="y" k="150" />
    <hkern u1="&#xe2;" u2="x" k="50" />
    <hkern u1="&#xe2;" u2="w" k="150" />
    <hkern u1="&#xe2;" u2="v" k="125" />
    <hkern u1="&#xe2;" u2="t" k="50" />
    <hkern u1="&#xe2;" u2="j" k="100" />
    <hkern u1="&#xe2;" u2="f" k="50" />
    <hkern u1="&#xe2;" u2="\" k="350" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="175" />
    <hkern u1="&#xe2;" u2="&#x39;" k="75" />
    <hkern u1="&#xe3;" u2="&#xff;" k="150" />
    <hkern u1="&#xe3;" u2="&#xfd;" k="150" />
    <hkern u1="&#xe3;" u2="y" k="150" />
    <hkern u1="&#xe3;" u2="x" k="50" />
    <hkern u1="&#xe3;" u2="w" k="150" />
    <hkern u1="&#xe3;" u2="v" k="125" />
    <hkern u1="&#xe3;" u2="t" k="50" />
    <hkern u1="&#xe3;" u2="j" k="100" />
    <hkern u1="&#xe3;" u2="f" k="50" />
    <hkern u1="&#xe3;" u2="\" k="350" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="175" />
    <hkern u1="&#xe3;" u2="&#x39;" k="75" />
    <hkern u1="&#xe4;" u2="&#xff;" k="150" />
    <hkern u1="&#xe4;" u2="&#xfd;" k="150" />
    <hkern u1="&#xe4;" u2="y" k="150" />
    <hkern u1="&#xe4;" u2="x" k="50" />
    <hkern u1="&#xe4;" u2="w" k="150" />
    <hkern u1="&#xe4;" u2="v" k="125" />
    <hkern u1="&#xe4;" u2="t" k="50" />
    <hkern u1="&#xe4;" u2="j" k="100" />
    <hkern u1="&#xe4;" u2="f" k="50" />
    <hkern u1="&#xe4;" u2="\" k="350" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="175" />
    <hkern u1="&#xe4;" u2="&#x39;" k="75" />
    <hkern u1="&#xe5;" u2="&#xff;" k="150" />
    <hkern u1="&#xe5;" u2="&#xfd;" k="150" />
    <hkern u1="&#xe5;" u2="y" k="150" />
    <hkern u1="&#xe5;" u2="x" k="50" />
    <hkern u1="&#xe5;" u2="w" k="150" />
    <hkern u1="&#xe5;" u2="v" k="125" />
    <hkern u1="&#xe5;" u2="t" k="50" />
    <hkern u1="&#xe5;" u2="j" k="100" />
    <hkern u1="&#xe5;" u2="f" k="50" />
    <hkern u1="&#xe5;" u2="\" k="350" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="175" />
    <hkern u1="&#xe5;" u2="&#x39;" k="75" />
    <hkern u1="&#xe7;" u2="&#xff;" k="50" />
    <hkern u1="&#xe7;" u2="&#xfd;" k="50" />
    <hkern u1="&#xe7;" u2="&#xf8;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf6;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf5;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf4;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf3;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf2;" k="25" />
    <hkern u1="&#xe7;" u2="&#xf0;" k="25" />
    <hkern u1="&#xe7;" u2="&#xeb;" k="25" />
    <hkern u1="&#xe7;" u2="&#xea;" k="25" />
    <hkern u1="&#xe7;" u2="&#xe9;" k="25" />
    <hkern u1="&#xe7;" u2="&#xe8;" k="25" />
    <hkern u1="&#xe7;" u2="&#xe7;" k="25" />
    <hkern u1="&#xe7;" u2="z" k="25" />
    <hkern u1="&#xe7;" u2="y" k="50" />
    <hkern u1="&#xe7;" u2="x" k="50" />
    <hkern u1="&#xe7;" u2="w" k="50" />
    <hkern u1="&#xe7;" u2="v" k="50" />
    <hkern u1="&#xe7;" u2="q" k="25" />
    <hkern u1="&#xe7;" u2="o" k="25" />
    <hkern u1="&#xe7;" u2="j" k="75" />
    <hkern u1="&#xe7;" u2="e" k="25" />
    <hkern u1="&#xe7;" u2="d" k="25" />
    <hkern u1="&#xe7;" u2="c" k="25" />
    <hkern u1="&#xe7;" u2="\" k="225" />
    <hkern u1="&#xe7;" u2="&#x3f;" k="175" />
    <hkern u1="&#xe7;" u2="&#x37;" k="50" />
    <hkern u1="&#xe7;" u2="&#x34;" k="175" />
    <hkern u1="&#xe7;" u2="&#x26;" k="50" />
    <hkern u1="&#xe8;" u2="&#xff;" k="125" />
    <hkern u1="&#xe8;" u2="&#xfd;" k="125" />
    <hkern u1="&#xe8;" u2="&#xe5;" k="75" />
    <hkern u1="&#xe8;" u2="&#xe4;" k="75" />
    <hkern u1="&#xe8;" u2="&#xe3;" k="75" />
    <hkern u1="&#xe8;" u2="&#xe2;" k="75" />
    <hkern u1="&#xe8;" u2="&#xe1;" k="75" />
    <hkern u1="&#xe8;" u2="&#xe0;" k="75" />
    <hkern u1="&#xe8;" u2="z" k="125" />
    <hkern u1="&#xe8;" u2="y" k="125" />
    <hkern u1="&#xe8;" u2="x" k="150" />
    <hkern u1="&#xe8;" u2="w" k="125" />
    <hkern u1="&#xe8;" u2="v" k="125" />
    <hkern u1="&#xe8;" u2="j" k="100" />
    <hkern u1="&#xe8;" u2="a" k="75" />
    <hkern u1="&#xe8;" u2="\" k="350" />
    <hkern u1="&#xe8;" u2="&#x3f;" k="175" />
    <hkern u1="&#xe8;" u2="&#x37;" k="125" />
    <hkern u1="&#xe8;" u2="&#x32;" k="125" />
    <hkern u1="&#xe8;" u2="&#x2f;" k="150" />
    <hkern u1="&#xe9;" u2="&#xff;" k="125" />
    <hkern u1="&#xe9;" u2="&#xfd;" k="125" />
    <hkern u1="&#xe9;" u2="&#xe5;" k="75" />
    <hkern u1="&#xe9;" u2="&#xe4;" k="75" />
    <hkern u1="&#xe9;" u2="&#xe3;" k="75" />
    <hkern u1="&#xe9;" u2="&#xe2;" k="75" />
    <hkern u1="&#xe9;" u2="&#xe1;" k="75" />
    <hkern u1="&#xe9;" u2="&#xe0;" k="75" />
    <hkern u1="&#xe9;" u2="z" k="125" />
    <hkern u1="&#xe9;" u2="y" k="125" />
    <hkern u1="&#xe9;" u2="x" k="150" />
    <hkern u1="&#xe9;" u2="w" k="125" />
    <hkern u1="&#xe9;" u2="v" k="125" />
    <hkern u1="&#xe9;" u2="j" k="100" />
    <hkern u1="&#xe9;" u2="a" k="75" />
    <hkern u1="&#xe9;" u2="\" k="350" />
    <hkern u1="&#xe9;" u2="&#x3f;" k="175" />
    <hkern u1="&#xe9;" u2="&#x37;" k="125" />
    <hkern u1="&#xe9;" u2="&#x32;" k="125" />
    <hkern u1="&#xe9;" u2="&#x2f;" k="150" />
    <hkern u1="&#xea;" u2="&#xff;" k="125" />
    <hkern u1="&#xea;" u2="&#xfd;" k="125" />
    <hkern u1="&#xea;" u2="&#xe5;" k="75" />
    <hkern u1="&#xea;" u2="&#xe4;" k="75" />
    <hkern u1="&#xea;" u2="&#xe3;" k="75" />
    <hkern u1="&#xea;" u2="&#xe2;" k="75" />
    <hkern u1="&#xea;" u2="&#xe1;" k="75" />
    <hkern u1="&#xea;" u2="&#xe0;" k="75" />
    <hkern u1="&#xea;" u2="z" k="125" />
    <hkern u1="&#xea;" u2="y" k="125" />
    <hkern u1="&#xea;" u2="x" k="150" />
    <hkern u1="&#xea;" u2="w" k="125" />
    <hkern u1="&#xea;" u2="v" k="125" />
    <hkern u1="&#xea;" u2="j" k="100" />
    <hkern u1="&#xea;" u2="a" k="75" />
    <hkern u1="&#xea;" u2="\" k="350" />
    <hkern u1="&#xea;" u2="&#x3f;" k="175" />
    <hkern u1="&#xea;" u2="&#x37;" k="125" />
    <hkern u1="&#xea;" u2="&#x32;" k="125" />
    <hkern u1="&#xea;" u2="&#x2f;" k="150" />
    <hkern u1="&#xeb;" u2="&#xff;" k="125" />
    <hkern u1="&#xeb;" u2="&#xfd;" k="125" />
    <hkern u1="&#xeb;" u2="&#xe5;" k="75" />
    <hkern u1="&#xeb;" u2="&#xe4;" k="75" />
    <hkern u1="&#xeb;" u2="&#xe3;" k="75" />
    <hkern u1="&#xeb;" u2="&#xe2;" k="75" />
    <hkern u1="&#xeb;" u2="&#xe1;" k="75" />
    <hkern u1="&#xeb;" u2="&#xe0;" k="75" />
    <hkern u1="&#xeb;" u2="z" k="125" />
    <hkern u1="&#xeb;" u2="y" k="125" />
    <hkern u1="&#xeb;" u2="x" k="150" />
    <hkern u1="&#xeb;" u2="w" k="125" />
    <hkern u1="&#xeb;" u2="v" k="125" />
    <hkern u1="&#xeb;" u2="j" k="100" />
    <hkern u1="&#xeb;" u2="a" k="75" />
    <hkern u1="&#xeb;" u2="\" k="350" />
    <hkern u1="&#xeb;" u2="&#x3f;" k="175" />
    <hkern u1="&#xeb;" u2="&#x37;" k="125" />
    <hkern u1="&#xeb;" u2="&#x32;" k="125" />
    <hkern u1="&#xeb;" u2="&#x2f;" k="150" />
    <hkern u1="&#xec;" u2="&#xff;" k="75" />
    <hkern u1="&#xec;" u2="&#xfd;" k="75" />
    <hkern u1="&#xec;" u2="y" k="75" />
    <hkern u1="&#xec;" u2="x" k="75" />
    <hkern u1="&#xec;" u2="w" k="75" />
    <hkern u1="&#xec;" u2="v" k="75" />
    <hkern u1="&#xec;" u2="t" k="50" />
    <hkern u1="&#xec;" u2="j" k="75" />
    <hkern u1="&#xec;" u2="&#x3f;" k="125" />
    <hkern u1="&#xed;" u2="&#xff;" k="75" />
    <hkern u1="&#xed;" u2="&#xfd;" k="75" />
    <hkern u1="&#xed;" u2="y" k="75" />
    <hkern u1="&#xed;" u2="x" k="75" />
    <hkern u1="&#xed;" u2="w" k="75" />
    <hkern u1="&#xed;" u2="v" k="75" />
    <hkern u1="&#xed;" u2="t" k="50" />
    <hkern u1="&#xed;" u2="j" k="75" />
    <hkern u1="&#xed;" u2="&#x3f;" k="125" />
    <hkern u1="&#xee;" u2="&#xff;" k="75" />
    <hkern u1="&#xee;" u2="&#xfd;" k="75" />
    <hkern u1="&#xee;" u2="y" k="75" />
    <hkern u1="&#xee;" u2="x" k="75" />
    <hkern u1="&#xee;" u2="w" k="75" />
    <hkern u1="&#xee;" u2="v" k="75" />
    <hkern u1="&#xee;" u2="t" k="50" />
    <hkern u1="&#xee;" u2="j" k="75" />
    <hkern u1="&#xee;" u2="&#x3f;" k="125" />
    <hkern u1="&#xef;" u2="&#xff;" k="75" />
    <hkern u1="&#xef;" u2="&#xfd;" k="75" />
    <hkern u1="&#xef;" u2="y" k="75" />
    <hkern u1="&#xef;" u2="x" k="75" />
    <hkern u1="&#xef;" u2="w" k="75" />
    <hkern u1="&#xef;" u2="v" k="75" />
    <hkern u1="&#xef;" u2="t" k="50" />
    <hkern u1="&#xef;" u2="j" k="75" />
    <hkern u1="&#xef;" u2="&#x3f;" k="125" />
    <hkern u1="&#xf0;" u2="&#xff;" k="50" />
    <hkern u1="&#xf0;" u2="&#xfd;" k="50" />
    <hkern u1="&#xf0;" u2="y" k="50" />
    <hkern u1="&#xf0;" u2="w" k="50" />
    <hkern u1="&#xf0;" u2="v" k="50" />
    <hkern u1="&#xf0;" u2="j" k="100" />
    <hkern u1="&#xf1;" u2="&#xff;" k="175" />
    <hkern u1="&#xf1;" u2="&#xfd;" k="175" />
    <hkern u1="&#xf1;" u2="&#xfc;" k="25" />
    <hkern u1="&#xf1;" u2="&#xfb;" k="25" />
    <hkern u1="&#xf1;" u2="&#xfa;" k="25" />
    <hkern u1="&#xf1;" u2="&#xf9;" k="25" />
    <hkern u1="&#xf1;" u2="y" k="175" />
    <hkern u1="&#xf1;" u2="x" k="75" />
    <hkern u1="&#xf1;" u2="w" k="175" />
    <hkern u1="&#xf1;" u2="v" k="175" />
    <hkern u1="&#xf1;" u2="u" k="25" />
    <hkern u1="&#xf1;" u2="t" k="50" />
    <hkern u1="&#xf1;" u2="j" k="100" />
    <hkern u1="&#xf1;" u2="\" k="400" />
    <hkern u1="&#xf1;" u2="&#x3f;" k="175" />
    <hkern u1="&#xf2;" u2="&#xff;" k="150" />
    <hkern u1="&#xf2;" u2="&#xfd;" k="150" />
    <hkern u1="&#xf2;" u2="&#xf8;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xf6;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xf5;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xf4;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xf3;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xf2;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xf0;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xeb;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xea;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xe9;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xe8;" k="-25" />
    <hkern u1="&#xf2;" u2="&#xe7;" k="-25" />
    <hkern u1="&#xf2;" u2="z" k="100" />
    <hkern u1="&#xf2;" u2="y" k="150" />
    <hkern u1="&#xf2;" u2="x" k="150" />
    <hkern u1="&#xf2;" u2="w" k="150" />
    <hkern u1="&#xf2;" u2="v" k="150" />
    <hkern u1="&#xf2;" u2="q" k="-25" />
    <hkern u1="&#xf2;" u2="o" k="-25" />
    <hkern u1="&#xf2;" u2="j" k="100" />
    <hkern u1="&#xf2;" u2="e" k="-25" />
    <hkern u1="&#xf2;" u2="d" k="-25" />
    <hkern u1="&#xf2;" u2="c" k="-25" />
    <hkern u1="&#xf2;" u2="\" k="425" />
    <hkern u1="&#xf2;" u2="&#x3f;" k="175" />
    <hkern u1="&#xf2;" u2="&#x37;" k="125" />
    <hkern u1="&#xf2;" u2="&#x2f;" k="150" />
    <hkern u1="&#xf3;" u2="&#xff;" k="150" />
    <hkern u1="&#xf3;" u2="&#xfd;" k="150" />
    <hkern u1="&#xf3;" u2="&#xf8;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xf6;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xf5;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xf4;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xf3;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xf2;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xf0;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xeb;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xea;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xe9;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xe8;" k="-25" />
    <hkern u1="&#xf3;" u2="&#xe7;" k="-25" />
    <hkern u1="&#xf3;" u2="z" k="100" />
    <hkern u1="&#xf3;" u2="y" k="150" />
    <hkern u1="&#xf3;" u2="x" k="150" />
    <hkern u1="&#xf3;" u2="w" k="150" />
    <hkern u1="&#xf3;" u2="v" k="150" />
    <hkern u1="&#xf3;" u2="q" k="-25" />
    <hkern u1="&#xf3;" u2="o" k="-25" />
    <hkern u1="&#xf3;" u2="j" k="100" />
    <hkern u1="&#xf3;" u2="e" k="-25" />
    <hkern u1="&#xf3;" u2="d" k="-25" />
    <hkern u1="&#xf3;" u2="c" k="-25" />
    <hkern u1="&#xf3;" u2="\" k="425" />
    <hkern u1="&#xf3;" u2="&#x3f;" k="175" />
    <hkern u1="&#xf3;" u2="&#x37;" k="125" />
    <hkern u1="&#xf3;" u2="&#x2f;" k="150" />
    <hkern u1="&#xf4;" u2="&#xff;" k="150" />
    <hkern u1="&#xf4;" u2="&#xfd;" k="150" />
    <hkern u1="&#xf4;" u2="&#xf8;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xf6;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xf5;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xf4;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xf3;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xf2;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xf0;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xeb;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xea;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xe9;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xe8;" k="-25" />
    <hkern u1="&#xf4;" u2="&#xe7;" k="-25" />
    <hkern u1="&#xf4;" u2="z" k="100" />
    <hkern u1="&#xf4;" u2="y" k="150" />
    <hkern u1="&#xf4;" u2="x" k="150" />
    <hkern u1="&#xf4;" u2="w" k="150" />
    <hkern u1="&#xf4;" u2="v" k="150" />
    <hkern u1="&#xf4;" u2="q" k="-25" />
    <hkern u1="&#xf4;" u2="o" k="-25" />
    <hkern u1="&#xf4;" u2="j" k="100" />
    <hkern u1="&#xf4;" u2="e" k="-25" />
    <hkern u1="&#xf4;" u2="d" k="-25" />
    <hkern u1="&#xf4;" u2="c" k="-25" />
    <hkern u1="&#xf4;" u2="\" k="425" />
    <hkern u1="&#xf4;" u2="&#x3f;" k="175" />
    <hkern u1="&#xf4;" u2="&#x37;" k="125" />
    <hkern u1="&#xf4;" u2="&#x2f;" k="150" />
    <hkern u1="&#xf5;" u2="&#xff;" k="150" />
    <hkern u1="&#xf5;" u2="&#xfd;" k="150" />
    <hkern u1="&#xf5;" u2="&#xf8;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xf6;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xf5;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xf4;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xf3;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xf2;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xf0;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xeb;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xea;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xe9;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xe8;" k="-25" />
    <hkern u1="&#xf5;" u2="&#xe7;" k="-25" />
    <hkern u1="&#xf5;" u2="z" k="100" />
    <hkern u1="&#xf5;" u2="y" k="150" />
    <hkern u1="&#xf5;" u2="x" k="150" />
    <hkern u1="&#xf5;" u2="w" k="150" />
    <hkern u1="&#xf5;" u2="v" k="150" />
    <hkern u1="&#xf5;" u2="q" k="-25" />
    <hkern u1="&#xf5;" u2="o" k="-25" />
    <hkern u1="&#xf5;" u2="j" k="100" />
    <hkern u1="&#xf5;" u2="e" k="-25" />
    <hkern u1="&#xf5;" u2="d" k="-25" />
    <hkern u1="&#xf5;" u2="c" k="-25" />
    <hkern u1="&#xf5;" u2="\" k="425" />
    <hkern u1="&#xf5;" u2="&#x3f;" k="175" />
    <hkern u1="&#xf5;" u2="&#x37;" k="125" />
    <hkern u1="&#xf5;" u2="&#x2f;" k="150" />
    <hkern u1="&#xf6;" u2="&#xff;" k="150" />
    <hkern u1="&#xf6;" u2="&#xfd;" k="150" />
    <hkern u1="&#xf6;" u2="&#xf8;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xf6;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xf5;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xf4;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xf3;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xf2;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xf0;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xeb;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xea;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xe9;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xe8;" k="-25" />
    <hkern u1="&#xf6;" u2="&#xe7;" k="-25" />
    <hkern u1="&#xf6;" u2="z" k="100" />
    <hkern u1="&#xf6;" u2="y" k="150" />
    <hkern u1="&#xf6;" u2="x" k="150" />
    <hkern u1="&#xf6;" u2="w" k="150" />
    <hkern u1="&#xf6;" u2="v" k="150" />
    <hkern u1="&#xf6;" u2="q" k="-25" />
    <hkern u1="&#xf6;" u2="o" k="-25" />
    <hkern u1="&#xf6;" u2="j" k="100" />
    <hkern u1="&#xf6;" u2="e" k="-25" />
    <hkern u1="&#xf6;" u2="d" k="-25" />
    <hkern u1="&#xf6;" u2="c" k="-25" />
    <hkern u1="&#xf6;" u2="\" k="425" />
    <hkern u1="&#xf6;" u2="&#x3f;" k="175" />
    <hkern u1="&#xf6;" u2="&#x37;" k="125" />
    <hkern u1="&#xf6;" u2="&#x2f;" k="150" />
    <hkern u1="&#xf8;" u2="&#xff;" k="150" />
    <hkern u1="&#xf8;" u2="&#xfd;" k="150" />
    <hkern u1="&#xf8;" u2="&#xf8;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xf6;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xf5;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xf4;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xf3;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xf2;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xf0;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xeb;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xea;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xe9;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xe8;" k="-25" />
    <hkern u1="&#xf8;" u2="&#xe7;" k="-25" />
    <hkern u1="&#xf8;" u2="z" k="100" />
    <hkern u1="&#xf8;" u2="y" k="150" />
    <hkern u1="&#xf8;" u2="x" k="150" />
    <hkern u1="&#xf8;" u2="w" k="150" />
    <hkern u1="&#xf8;" u2="v" k="150" />
    <hkern u1="&#xf8;" u2="q" k="-25" />
    <hkern u1="&#xf8;" u2="o" k="-25" />
    <hkern u1="&#xf8;" u2="j" k="100" />
    <hkern u1="&#xf8;" u2="e" k="-25" />
    <hkern u1="&#xf8;" u2="d" k="-25" />
    <hkern u1="&#xf8;" u2="c" k="-25" />
    <hkern u1="&#xf8;" u2="\" k="425" />
    <hkern u1="&#xf8;" u2="&#x3f;" k="175" />
    <hkern u1="&#xf8;" u2="&#x37;" k="125" />
    <hkern u1="&#xf8;" u2="&#x2f;" k="150" />
    <hkern u1="&#xf9;" u2="&#xff;" k="100" />
    <hkern u1="&#xf9;" u2="&#xfd;" k="100" />
    <hkern u1="&#xf9;" u2="z" k="50" />
    <hkern u1="&#xf9;" u2="y" k="100" />
    <hkern u1="&#xf9;" u2="x" k="75" />
    <hkern u1="&#xf9;" u2="w" k="100" />
    <hkern u1="&#xf9;" u2="v" k="100" />
    <hkern u1="&#xf9;" u2="j" k="100" />
    <hkern u1="&#xf9;" u2="\" k="275" />
    <hkern u1="&#xf9;" u2="V" k="325" />
    <hkern u1="&#xf9;" u2="&#x3f;" k="175" />
    <hkern u1="&#xfa;" u2="&#xff;" k="100" />
    <hkern u1="&#xfa;" u2="&#xfd;" k="100" />
    <hkern u1="&#xfa;" u2="z" k="50" />
    <hkern u1="&#xfa;" u2="y" k="100" />
    <hkern u1="&#xfa;" u2="x" k="75" />
    <hkern u1="&#xfa;" u2="w" k="100" />
    <hkern u1="&#xfa;" u2="v" k="100" />
    <hkern u1="&#xfa;" u2="j" k="100" />
    <hkern u1="&#xfa;" u2="\" k="275" />
    <hkern u1="&#xfa;" u2="V" k="325" />
    <hkern u1="&#xfa;" u2="&#x3f;" k="175" />
    <hkern u1="&#xfb;" u2="&#xff;" k="100" />
    <hkern u1="&#xfb;" u2="&#xfd;" k="100" />
    <hkern u1="&#xfb;" u2="z" k="50" />
    <hkern u1="&#xfb;" u2="y" k="100" />
    <hkern u1="&#xfb;" u2="x" k="75" />
    <hkern u1="&#xfb;" u2="w" k="100" />
    <hkern u1="&#xfb;" u2="v" k="100" />
    <hkern u1="&#xfb;" u2="j" k="100" />
    <hkern u1="&#xfb;" u2="\" k="275" />
    <hkern u1="&#xfb;" u2="V" k="325" />
    <hkern u1="&#xfb;" u2="&#x3f;" k="175" />
    <hkern u1="&#xfc;" u2="&#xff;" k="100" />
    <hkern u1="&#xfc;" u2="&#xfd;" k="100" />
    <hkern u1="&#xfc;" u2="z" k="50" />
    <hkern u1="&#xfc;" u2="y" k="100" />
    <hkern u1="&#xfc;" u2="x" k="75" />
    <hkern u1="&#xfc;" u2="w" k="100" />
    <hkern u1="&#xfc;" u2="v" k="100" />
    <hkern u1="&#xfc;" u2="j" k="100" />
    <hkern u1="&#xfc;" u2="\" k="275" />
    <hkern u1="&#xfc;" u2="V" k="325" />
    <hkern u1="&#xfc;" u2="&#x3f;" k="175" />
    <hkern u1="&#xfd;" u2="&#xff;" k="75" />
    <hkern u1="&#xfd;" u2="&#xfd;" k="75" />
    <hkern u1="&#xfd;" u2="&#xfc;" k="75" />
    <hkern u1="&#xfd;" u2="&#xfb;" k="75" />
    <hkern u1="&#xfd;" u2="&#xfa;" k="75" />
    <hkern u1="&#xfd;" u2="&#xf9;" k="75" />
    <hkern u1="&#xfd;" u2="&#xf8;" k="150" />
    <hkern u1="&#xfd;" u2="&#xf6;" k="150" />
    <hkern u1="&#xfd;" u2="&#xf5;" k="150" />
    <hkern u1="&#xfd;" u2="&#xf4;" k="150" />
    <hkern u1="&#xfd;" u2="&#xf3;" k="150" />
    <hkern u1="&#xfd;" u2="&#xf2;" k="150" />
    <hkern u1="&#xfd;" u2="&#xf1;" k="50" />
    <hkern u1="&#xfd;" u2="&#xf0;" k="150" />
    <hkern u1="&#xfd;" u2="&#xef;" k="50" />
    <hkern u1="&#xfd;" u2="&#xee;" k="50" />
    <hkern u1="&#xfd;" u2="&#xed;" k="50" />
    <hkern u1="&#xfd;" u2="&#xec;" k="50" />
    <hkern u1="&#xfd;" u2="&#xeb;" k="150" />
    <hkern u1="&#xfd;" u2="&#xea;" k="150" />
    <hkern u1="&#xfd;" u2="&#xe9;" k="150" />
    <hkern u1="&#xfd;" u2="&#xe8;" k="150" />
    <hkern u1="&#xfd;" u2="&#xe7;" k="150" />
    <hkern u1="&#xfd;" u2="&#xe5;" k="200" />
    <hkern u1="&#xfd;" u2="&#xe4;" k="200" />
    <hkern u1="&#xfd;" u2="&#xe3;" k="200" />
    <hkern u1="&#xfd;" u2="&#xe2;" k="200" />
    <hkern u1="&#xfd;" u2="&#xe1;" k="200" />
    <hkern u1="&#xfd;" u2="&#xe0;" k="200" />
    <hkern u1="&#xfd;" u2="z" k="150" />
    <hkern u1="&#xfd;" u2="y" k="75" />
    <hkern u1="&#xfd;" u2="x" k="75" />
    <hkern u1="&#xfd;" u2="w" k="75" />
    <hkern u1="&#xfd;" u2="v" k="75" />
    <hkern u1="&#xfd;" u2="u" k="75" />
    <hkern u1="&#xfd;" u2="t" k="75" />
    <hkern u1="&#xfd;" u2="s" k="125" />
    <hkern u1="&#xfd;" u2="r" k="50" />
    <hkern u1="&#xfd;" u2="q" k="150" />
    <hkern u1="&#xfd;" u2="p" k="50" />
    <hkern u1="&#xfd;" u2="o" k="150" />
    <hkern u1="&#xfd;" u2="n" k="50" />
    <hkern u1="&#xfd;" u2="m" k="50" />
    <hkern u1="&#xfd;" u2="l" k="75" />
    <hkern u1="&#xfd;" u2="k" k="50" />
    <hkern u1="&#xfd;" u2="j" k="150" />
    <hkern u1="&#xfd;" u2="i" k="50" />
    <hkern u1="&#xfd;" u2="h" k="50" />
    <hkern u1="&#xfd;" u2="g" k="125" />
    <hkern u1="&#xfd;" u2="f" k="50" />
    <hkern u1="&#xfd;" u2="e" k="150" />
    <hkern u1="&#xfd;" u2="d" k="150" />
    <hkern u1="&#xfd;" u2="c" k="150" />
    <hkern u1="&#xfd;" u2="b" k="50" />
    <hkern u1="&#xfd;" u2="a" k="200" />
    <hkern u1="&#xfd;" u2="\" k="350" />
    <hkern u1="&#xfd;" u2="Z" k="325" />
    <hkern u1="&#xfd;" u2="&#x3f;" k="175" />
    <hkern u1="&#xfd;" u2="&#x38;" k="100" />
    <hkern u1="&#xfd;" u2="&#x37;" k="300" />
    <hkern u1="&#xfd;" u2="&#x36;" k="75" />
    <hkern u1="&#xfd;" u2="&#x35;" k="175" />
    <hkern u1="&#xfd;" u2="&#x34;" k="200" />
    <hkern u1="&#xfd;" u2="&#x33;" k="175" />
    <hkern u1="&#xfd;" u2="&#x32;" k="200" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="500" />
    <hkern u1="&#xfd;" u2="&#x26;" k="150" />
    <hkern u1="&#xff;" u2="&#xff;" k="75" />
    <hkern u1="&#xff;" u2="&#xfd;" k="75" />
    <hkern u1="&#xff;" u2="&#xfc;" k="75" />
    <hkern u1="&#xff;" u2="&#xfb;" k="75" />
    <hkern u1="&#xff;" u2="&#xfa;" k="75" />
    <hkern u1="&#xff;" u2="&#xf9;" k="75" />
    <hkern u1="&#xff;" u2="&#xf8;" k="150" />
    <hkern u1="&#xff;" u2="&#xf6;" k="150" />
    <hkern u1="&#xff;" u2="&#xf5;" k="150" />
    <hkern u1="&#xff;" u2="&#xf4;" k="150" />
    <hkern u1="&#xff;" u2="&#xf3;" k="150" />
    <hkern u1="&#xff;" u2="&#xf2;" k="150" />
    <hkern u1="&#xff;" u2="&#xf1;" k="50" />
    <hkern u1="&#xff;" u2="&#xf0;" k="150" />
    <hkern u1="&#xff;" u2="&#xef;" k="50" />
    <hkern u1="&#xff;" u2="&#xee;" k="50" />
    <hkern u1="&#xff;" u2="&#xed;" k="50" />
    <hkern u1="&#xff;" u2="&#xec;" k="50" />
    <hkern u1="&#xff;" u2="&#xeb;" k="150" />
    <hkern u1="&#xff;" u2="&#xea;" k="150" />
    <hkern u1="&#xff;" u2="&#xe9;" k="150" />
    <hkern u1="&#xff;" u2="&#xe8;" k="150" />
    <hkern u1="&#xff;" u2="&#xe7;" k="150" />
    <hkern u1="&#xff;" u2="&#xe5;" k="200" />
    <hkern u1="&#xff;" u2="&#xe4;" k="200" />
    <hkern u1="&#xff;" u2="&#xe3;" k="200" />
    <hkern u1="&#xff;" u2="&#xe2;" k="200" />
    <hkern u1="&#xff;" u2="&#xe1;" k="200" />
    <hkern u1="&#xff;" u2="&#xe0;" k="200" />
    <hkern u1="&#xff;" u2="z" k="150" />
    <hkern u1="&#xff;" u2="y" k="75" />
    <hkern u1="&#xff;" u2="x" k="75" />
    <hkern u1="&#xff;" u2="w" k="75" />
    <hkern u1="&#xff;" u2="v" k="75" />
    <hkern u1="&#xff;" u2="u" k="75" />
    <hkern u1="&#xff;" u2="t" k="75" />
    <hkern u1="&#xff;" u2="s" k="125" />
    <hkern u1="&#xff;" u2="r" k="50" />
    <hkern u1="&#xff;" u2="q" k="150" />
    <hkern u1="&#xff;" u2="p" k="50" />
    <hkern u1="&#xff;" u2="o" k="150" />
    <hkern u1="&#xff;" u2="n" k="50" />
    <hkern u1="&#xff;" u2="m" k="50" />
    <hkern u1="&#xff;" u2="l" k="75" />
    <hkern u1="&#xff;" u2="k" k="50" />
    <hkern u1="&#xff;" u2="j" k="150" />
    <hkern u1="&#xff;" u2="i" k="50" />
    <hkern u1="&#xff;" u2="h" k="50" />
    <hkern u1="&#xff;" u2="g" k="125" />
    <hkern u1="&#xff;" u2="f" k="50" />
    <hkern u1="&#xff;" u2="e" k="150" />
    <hkern u1="&#xff;" u2="d" k="150" />
    <hkern u1="&#xff;" u2="c" k="150" />
    <hkern u1="&#xff;" u2="b" k="50" />
    <hkern u1="&#xff;" u2="a" k="200" />
    <hkern u1="&#xff;" u2="\" k="350" />
    <hkern u1="&#xff;" u2="Z" k="325" />
    <hkern u1="&#xff;" u2="&#x3f;" k="175" />
    <hkern u1="&#xff;" u2="&#x38;" k="100" />
    <hkern u1="&#xff;" u2="&#x37;" k="300" />
    <hkern u1="&#xff;" u2="&#x36;" k="75" />
    <hkern u1="&#xff;" u2="&#x35;" k="175" />
    <hkern u1="&#xff;" u2="&#x34;" k="200" />
    <hkern u1="&#xff;" u2="&#x33;" k="175" />
    <hkern u1="&#xff;" u2="&#x32;" k="200" />
    <hkern u1="&#xff;" u2="&#x2f;" k="500" />
    <hkern u1="&#xff;" u2="&#x26;" k="150" />
  </font>
</defs></svg>
