// BLock Feature One
.block-feature-one {
    z-index: 1;
    &:before {
        content: '';
        position: absolute;
        right: 0;
        left: 0;
        top:0;
        bottom: 17%;
        background: $light-bg-one;
        z-index: -1;
    }
    .upper-wrapper {
        border-bottom: 1px dashed rgba($color: #000000, $alpha: 0.5);
    }
    .section-subheading {
        position: absolute;
        left: 0;
        top:0;
        width: 37%;
    }
    .shape_01 {
        width: 2.3%;
        top:10%;
        left: 10%;
    }
    .shape_02 {
        width: 1.5%;
        bottom:6%;
        right: 9%;
    }
}
// BLock Feature Two
.block-feature-two {
    .wrapper {
        border-top: 1px dashed #CACACA;
        border-bottom: 1px dashed #CACACA;
    }
    .border-line {
        border-left: 1px dashed #CACACA;
        border-right: 1px dashed #CACACA;  
    }
    .numb {
        display: inline-block;
        font-size: 90px;
        letter-spacing: -1px;
        color: $heading;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            width: 85px;
            height: 85px;
            border-radius: 50%;
            background: $color-one;
            right: 8px;
            top:27px;
            z-index: -1;
        }
    }
}
// BLock Feature Three
.block-feature-three {
    z-index: 1;
    &:before  {
        content: '';
        position: absolute;
        right: 0;
        left: 0;
        top:34%;
        bottom: 0;
        background: $light-bg-one;
        z-index: -1;
    }
    &.no-bg:before {
        display: none;
    }
    .section-btn {
        position: absolute;
        left: 0;
        top:68px;
    }
    .block-title a {
        font-size: 32px;
        font-weight: 500;
        line-height: 1.25em;
        color: $heading;
        &:hover {
            text-decoration: underline;
        }
    }
    .round-btn {
        font-size: 18px;
        font-weight: 900;
        color: $color-two;
        width: 38px;
        height: 38px;
        border: 1px solid $color-two;
        &:hover {
            background: $color-one;
            border-color: $color-one;
            color: $heading;
        }
    }
    .block-one {
        background: url(../images/media/img_05.jpg) no-repeat center;
        background-size: cover;
        height: 470px;
        .tag {
            font-size: 14px;
            letter-spacing: 1px;
            color: $text-dark;
            line-height: 25px;
            border: 1px solid #000;
            padding: 0 10px;
        }
        .block-title {
            background: url(../images/shape/shape_02.svg) no-repeat right top;
            background-size: cover;
            border-radius: 25px;
            padding: 12px 18px 18px 0;
        }
    }
    .block-two {
        overflow: hidden;
        .img-wrapper {
            background: url(../images/media/img_06.jpg) no-repeat center;
            background-size: cover;
            width: 42%;
        }
        .text-wrapper {
            width: 58%;
            padding: 30px 60px 35px 30px;
        }
        .tag {
            font-size: 13px;
            letter-spacing: 1px;
            color: #ffff;
            line-height: 27px;
            border-radius: 17px;
            background: $color-two;
            padding: 0 17px;
        }
    }
    .block-three {
        background: #D3FF76;
        .tag {
            font-size: 13px;
            letter-spacing: 1px;
            color: #ffff;
            line-height: 27px;
            border-radius: 17px;
            background: $color-two;
            padding: 0 17px;
        }
        .round-btn {
            background: #fff;
            border-color: #fff;
            &:hover {
                background: $color-two;
                border-color: $color-two;
                color: #fff;
            }
        }
    }
    .block-four {
        background: url(../images/media/img_07.jpg) no-repeat center;
        background-size: cover;
        .block-title {
            background: url(../images/shape/shape_03.svg) no-repeat right top;
            background-size: cover;
            border-radius: 18px;
            padding: 12px 16px 12px 18px;
            a {
                font-size: 28px;
            }
        }
        .tag {
            font-size: 13px;
            letter-spacing: 1px;
            color: $color-two;
            line-height: 27px;
            border-radius: 17px;
            background: #fff;
            padding: 0 17px;
        }
        .round-btn {
            background: transparent;
            border-color: #fff;
            color: #fff;
            &:hover {
                background: $color-one;
                border-color: $color-one;
                color: #000;
            }
        }
    }
    .block-five {
        background: url(../images/blog/blog_img_05.jpg) no-repeat center;
        background-size: cover;
        height: 358px;
        .tag {
            font-size: 14px;
            letter-spacing: 1px;
            color: $text-dark;
            line-height: 25px;
            border: 1px solid #000;
            padding: 0 10px;
        }
        .block-title {
            background: url(../images/shape/shape_29.svg) no-repeat right top;
            background-size: cover;
            border-radius: 15px;
            padding: 12px 10px 18px 18px;
        }
    }
}
// BLock Feature Four
.block-feature-four {
    z-index: 1;

    .shape_01 {
        top:0;
        left: 7%;
        width: 2.52%;
        min-width: 28px;
        animation: rotated 50s infinite linear;
    }
    .shape_02 {
        bottom:2%;
        right: 7%;
        width: 1.8%;
        min-width: 22px;
        animation: rotated 48s infinite linear;
    }
}
// BLock Feature Five
.block-feature-five {
    z-index: 1;
    &:before {
        content: '';
        position: absolute;
        right: 0;
        top:0;
        bottom: 0;
        width: 45%;
        background: url(../images/shape/shape_09.svg) no-repeat right bottom;
        background-size: cover;
        z-index: -1;
    }
    .section-btn {
        position: absolute;
        left: 0;
        top:75px;
    }
    .shape_01 {
        right: 6%;
        bottom: 5%;
        width: 1%;
        animation: rotated 50s infinite linear;
    }
}
// BLock Feature Six 
.block-feature-six {
    background: $cyan;
    z-index: 1;
    &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top:0;
        right: 0;
        z-index: -1;
        background: url(../images/shape/shape_10.svg) no-repeat center;
        background-size: cover;
        -webkit-transform: scaleX(-1);
        transform: scaleX(-1);
    }
    &.bg-two {
        background: $light-bg-one;
        &:before {
            display: none;
        }
    }
    .shape_01 {
        bottom: 12%;
        right: 8%;
        width: 10%;
        -webkit-transform: scaleX(-1);
        transform: scaleX(-1);
    }
    .shape_02 {
        bottom: 39%;
        right: 30%;
        width: 2.5%;
    }
}
// BLock Feature Seven
.block-feature-seven {
    z-index: 1;
    .shape_01 {
        top:2%;
        left: 0;
        animation: rotated 50s infinite linear;
    }
}
// BLock Feature Eight
.block-feature-eight {
    background: url(../images/assets/bg_02.svg) no-repeat center;
    background-size: cover;
    z-index: 1;
    .section-btn {
        position: absolute;
        left: 0;
        top:72px;
    }
    .shape_01 {
        width: 40px;
        bottom:-20px;
        right: 25%;
        animation: rotated 50s infinite linear;
    }
}
// BLock Feature Nine
.block-feature-nine {
    z-index: 1;
    background: #000;
    border-radius: 30px 30px 100px 100px;
    &:after {
        content: '';
        position: absolute;
        width: 100%;
        height: 39%;
        left: 0;
        top:0;
        background: #EDF8EB;
        border-radius: 30px 30px 0 0;
        z-index: -1;
    }
    &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 50px;
        left: 0;
        top:0;
        background: #000;
        z-index: -1;
    }
    .heading {
        font-size: 85px;
        line-height: 1.411em;
        span {
            color: $color-six;
            text-decoration-line: underline;
            text-decoration-thickness: 4px;
        }
        a {
            width: 110px;
            height: 110px;
            display: inline-flex;
            align-items: center;
            background: $color-six;
            border-radius: 50%;
            position: relative;
            margin-right: 100px;
            @include transition(0.2s);
            &:before {
                content: '';
                position: absolute;
                width: 85px;
                height: 3px;
                right: -85px;
                top:calc(50% - 3px);
                background: #fff;
            }
            &:hover {
                background: $color-four;
            }
        }
    }
    .shape_01 {
        left: 0;
        top:5%;
        width: 4%;
    }
    .shape_02 {
        right: 7%;
        bottom:0;
        width: 8.4%;
    }
    .shape_03 {
        left:5%;
        bottom: 5%;
    }
}
// BLock Feature Ten
.block-feature-ten {
    .shape_01 {
        top:1%;
        left: 5%;
        width: 2%;
    }
    .line-wrapper:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 1px;
        border-bottom: 1px dashed #e5e5e5;
        left: 0;
        top:50%;
    }
}
// BLock Feature Eleven
.block-feature-eleven {
    z-index: 1;
    .slider-wrapper {
        width: 78vw;
        .slick-dots {
            margin: 0;
            padding: 0;
            position: absolute;
            bottom: 0;
            right: -140px;
            li button {
                width: 10px;
                height: 10px;
                background: #fff;
                border: 1px solid $color-eight;
            }
            .slick-active button {
                background: $color-eight;
            }
        }
    }
    .shape_01 {
        right: 0;
        bottom: 0;
        max-width: 13.4%;
        -webkit-transform: scaleX(-1);
        transform: scaleX(-1);
    }
}
// BLock Feature Twelve
.block-feature-twelve {
    background: $light-bg-two;
    z-index: 1;
    
    .shape_01 {
        right: 0;
        top: 12%;
        max-width: 13.4%;
    }
}
// BLock Feature Thirteen
.block-feature-thirteen {
    .upper-wrapper {
        border-bottom: 1px solid #E6E6E6;
        .shape_01 {
            left: 6%;
            bottom: 16%;
        }
    }
    .line-loop:before {
        content: '';
        position: absolute;
        height: 1px;
        width: 130%;
        left: 0;
        top:57%;
        background: #E6E6E6;
        z-index: -1;
    }
    .graph-panel {
        border-left: 1px solid #E6E6E6;
        .main-count {
            font-size: 120px;
        }
        .chart-box {
            padding: 0 15px;
            .chart-inner {
                width: 120px;
                background: #196164;
            }
        }
    }
}
// BLock Feature Fourteen
.block-feature-fourteen {
    .section-subheading {
        position: absolute;
        left: 0;
        top:0;
        width: 37%;
    }
    .shape_01 {
        max-width: 4%;
        top: 13%;
        right: 48%;
        animation: rotated 48s infinite linear;
    }
}
// BLock Feature Fifteen
.block-feature-fifteen {
    .shape_01 {
        max-width: 4%;
        top: 3%;
        left: 2%;
        animation: rotated 48s infinite linear;
    }
    .line-btn {
        &:before,&:after {
            content: '';
            position: absolute;
            width: calc(50% - 150px);
            height: 1px;
            background: #E3E3E3;
            top:32px;
            left: 0;
        }
        &:after {
            left: auto;
            right: 0;
        }
    }
}
// BLock Feature Sixteen
.block-feature-sixteen { 
    .media-img {
        border-radius: 30px;
    }
    .shape_01 {
        top:31%;
        left: 50%;
    }
}
// BLock Feature Seventeen
.block-feature-seventeen { 
    .section-subheading {
        position: absolute;
        left: 0;
        top:0;
        width: 37%;
    }
    .shape_01 {
        max-width: 8%;
        top: 12%;
        right: 43%;
    }
}
// BLock Feature Eighteen
.block-feature-eighteen { 
    background: url(../images/media/img_52.jpg) no-repeat left top;
    background-size: cover;
    &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(0, 0, 0, 0.44) 24.81%, rgba(0, 0, 0, 0) 94.81%);
        top:0;
        left: 0;
        z-index: -1;
    }
    .video-icon {
        width: 170px;
        height: 170px;
        color: #000;
        padding: 20px;
        background: $color-eleven;
        &:hover {
            background: $color-six;
        }
    }
    .bg-wrapper {
        padding: 48px 60px 60px;
        border-radius: 20px;
        background: $color-ten;

        ul {
            position: relative;
            z-index: 1;
            &:before {
                content: '';
                position: absolute;
                width: 1px;
                height: 96%;
                border-left: 1px dashed rgba($color: #fff, $alpha: 0.3);
                right: 20px;
                top:2%;
                z-index: -1;
            }
            li {
                position: relative;
                background: #fff;
                border-radius: 45px;
                padding: 9px 25px 9px 45px;
                font-weight: 500;
                font-size: 20px;
                line-height: 1.2em;
                color: $color-eight;
                display: inline-block;
                margin-bottom: 35px;
                margin-right: 52px;
                &:last-child {
                    margin-bottom: 0;
                }
                &:before {
                    content: url(../images/icon/icon_111.svg);
                    position: absolute;
                    right: -52px;
                    top:0;
                }
            }
        }
        .more-btn {
            border-radius: 30px;
            padding: 5px 25px 5px 5px;
            color: #fff;
            font-weight: 500;
            font-size: 18px;
            border: 1px solid #fff;
            .icon {
                width: 40px;
                height: 40px;
                background: $color-eleven;
            }
            &:hover {
                background: #fff;
                color: $color-eight;
            }
        }
    }
}
// Text Feature One
.text-feature-one {
    .line-wrapper {
        border-top: 1px dashed #d7d7d7;
        border-bottom: 1px dashed #d7d7d7;
        .shape_01 {
            width: 48px;
            bottom: 12%;
            right: 32%;
            animation: rotated 48s infinite linear;
        }
    }
    .card-style-three {
        border-right: 1px dashed #d7d7d7;
        border-bottom: 1px dashed #d7d7d7;
        padding-right: 100px;
        &:last-child {
            border-bottom: none;
        }
    }
    .media-list-item {
        height: 600px;
        width: 590px;
        background: url(../images/media/img_04.jpg) no-repeat center;
        background-size: cover;
        border-radius: 30px;
        position: relative;
        z-index: 1;
        &:before {
            content: '';
            position: absolute;
            width: 100%;
            height: 50%;
            bottom: 0;
            left: 0;
            z-index: -1;
            border-radius: 0 0 30px 30px;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 3.1%, #000000 100%);
            mix-blend-mode: overlay;
        }
        li {
            display: inline-block;
            font-weight: 500;
            color: $text-dark;
            padding: 6px 50px 6px 50px;
            border-radius: 45px;
            background: #fff;
            margin: 7px 0;
            position: relative;
            &:before {
                content: '';
                position: absolute;
                width: 28px;
                height: 28px;
                border-radius: 50%;
                background: $color-one;
                right: 10px;
                top:7px;
                @include transition(0.2s);
            }
            &:after {
                content: '\F633';
                position: absolute;
                font-family: $bootstrapFont;
                top:6px;
                right: 14px;
                color: $color-two;
                @include transition(0.2s);
            }
            &:hover:before {
                background: $color-two;
            }
            &:hover:after {
                color:#fff;
            }
        }
    }
}
// Text Feature Two
.text-feature-two {
    background: $color-three;
    z-index: 1;
    .shape_01 {
        top:16%;
        left: 3%;
        width: 2.3%;
        min-width: 28px;
        animation: rotated 50s infinite linear;
    }
    .shape_02 {
        bottom:5%;
        right: 2%;
        width: 1.5%;
        min-width: 22px;
        animation: rotated 48s infinite linear;
    }
}
// Text Feature Three
.text-feature-three {
    z-index: 1;
   .counter-wrapper {
    border-top: 1px solid rgba($color: #000000, $alpha: 0.1);
   }
   .media-wrapper {
    background: url(../images/media/img_13.jpg) no-repeat center;
    background-size: cover;
    border-radius: 30px;
    z-index: 1;
    max-width: 590px;
    .screen_01 {
        left:-9%;
        top:12%;
        width: 37.3%;
        border-radius: 10px;
        box-shadow: 10px 30px 50px rgba(0, 0, 0, 0.06);
    }
    .screen_02 {
        right:-11%;
        bottom:-9%;
        width: 65.5%;
    }
    .screen_03 {
        right:-13%;
        bottom:-11%;
        width: 50.85%;
        box-shadow: 0px 35px 70px rgba(0, 0, 0, 0.06);
    }
   }
   .shape_01 {
        bottom:2%;
        left: 39%;
        width: 2.3%;
        min-width: 28px;
        animation: rotated 50s infinite linear;
    }
}
// Text Feature Four
.text-feature-four {
    z-index: 1;
    .avatar {
       width: 65px;
       height: 65px; 
    }
    .name {
        font-size: 28px;
    }
    .quote-text {
        font-size: 50px;
        line-height: 1.4em;
    }
    .shape_01 {
        top:56%;
        right: 22%;
        animation: rotated 60s infinite linear;
    }
    .shape_02 {
        top:12%;
        left: 9%;
        width: 16px;
        animation: rotated 50s infinite linear;
    }
}
// Text Feature Five
.text-feature-five {
    .bg-wrapper {
        background: #FFF2AC;
    }
    li {
        display: inline-block;
        font-weight: 500;
        color: $text-dark;
        padding: 6px 50px 6px 50px;
        border-radius: 45px;
        background: #fff;
        margin: 7px 0;
        position: relative;

        &:before {
            content: '';
            position: absolute;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: #FFF2AC;
            right: 10px;
            top:7px;
            @include transition(0.2s);
        }
        &:after {
            content: '\F633';
            position: absolute;
            font-family: $bootstrapFont;
            top:6px;
            right: 14px;
            color: $color-two;
            @include transition(0.2s);
        }
        &:hover:before {
            background: $color-two;
        }
        &:hover:after {
            color:#fff;
        }
    }
    .media-wrapper {
        background: url(../images/media/img_20.jpg) no-repeat center;
        background-size: cover;
        z-index: 1;
        .video-icon {
            width: 90px;
            height: 90px;
            background: #fff;
            &:hover {
                background: $color-one;
            }
        }
        .screen_01 {
            right: -9%;
            bottom:-9%;
            width: 46.3%;
            animation: jumpThree 5s infinite linear;
            box-shadow: 0px 35px 70px rgba(0, 0, 0, 0.06);
        }
    }
}
// Text Feature Six
.text-feature-six {
    z-index: 1;
    .media-wrapper {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 63.64%;
        z-index: -1;
        .screen_01 {
            width: 61.1%;
            bottom:32%;
            left: -4%;
            z-index: 1;
            animation: jumpThree 5s infinite linear;
        }
    }
    .shape_01 {
        right: 0;
        bottom: 23%;
        width: 12.3%;
    }
    .shape_02 {
        top:1%;
        left: 39%;
    }
}
// Text Feature Seven
.text-feature-seven {
    ul li {
        font-weight: 500;
        color: $color-five;
        position: relative;
        padding-right: 33px;
        margin-bottom: 22px;
        &:after {
            content: '\F633';
            position: absolute;
            font-family: $bootstrapFont;
            top:1px;
            right: 0;
        }
    }
}
// Text Feature Nine
.text-feature-nine {
    background: url(../images/assets/bg_03.svg) no-repeat center bottom;
    background-size: cover;
    z-index: 1;
    .block {
        .icon {
            height: 50px;
        }
    }
    .vertical-text-wrapper {
        position: absolute;
        left: 0;
        top:0;
        bottom: 0;
        width: 45%;
        border-right: 1px solid rgba($color: #FEFFFC, $alpha: 0.17);
        .text-list {
            padding: 0 35px;
            font-weight: 700;
            font-size: 125px;
            letter-spacing: -2px;
            height: 20%;
            width: 100%;
            border-top: 1px solid rgba($color: #FEFFFC, $alpha: 0.17);
            display: flex;
            align-items: center;
            justify-content: end;
            color: #FFFFFF;
            mix-blend-mode: overlay;
            opacity: 0.8;
            &:hover {
                color: #C5FF4A;
                opacity: 1;
                mix-blend-mode: normal;
            }
        }
        .shape_01 {
            right: -83px;
            bottom: 21%;
            animation: jumpThree 6s infinite linear;
        }
        .shape_02 {
            right: 10%;
            top: 25%;
            animation: jumpFour 6s infinite linear;
        }
    }
    .shape_03 {
        right: 5%;
        top: 10%;
        animation: jumpFour 6s infinite linear;
    }
    .shape_04 {
        bottom: 0;
        right: 13%;
        width: 29%;
    }
    
}
// Team Section One
.team-section-one {
    .section-btn {
        position: absolute;
        left: 0;
        top:25px;
    }
}
// Team Section Two
.team-section-two {
    background: #fff;
    border: 1px solid #000;
    .section-btn {
        position: absolute;
        left: 0;
        top:30px;
    }
}
// Team Section Three
.team-section-three {
    z-index: 1;
    &:before {
        content: '';
        position: absolute;
        left: 0;
        top:0;
        bottom: 0;
        width: 45%;
        background: url(../images/shape/shape_09.svg) no-repeat right bottom;
        background-size: cover;
        z-index: -1;
        -webkit-transform: scaleX(-1);
        transform: scaleX(-1);
    }
    .section-btn {
        position: absolute;
        left: 0;
        top:15px;
    }
    .shape_01 {
        width: 2.3%;
        top:17%;
        right: 39%;
    }
    .shape_02 {
        width: 1.5%;
        bottom:6%;
        left: 9%;
    }
    .shape_03 {
        max-width: 22%;
        bottom:0;
        right: 0;
    }
}
// Team Details
.team-details {
    .bg-wrapper {
        background: #fff;
        border-radius: 30px;
        overflow: hidden;
        .border-right {
            border-left: 1px solid #e9e9e9;
        }
        .member-img {
            width: 100%;
            height: 100%;
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
        }
        .name {
            font-size: 32px;
        }
        .post {
            font-size: 18px;
            color: rgba($color: #000000, $alpha: 0.3);
        }
        h6 {
            font-size: 20px;
            padding: 42px 0 10px;
        }
        p {
            font-size: 18px;
        }
        .social-share {
            li {
              a {
                font-size: 20px;
                color: $heading;
                margin-right: 20px;
                &:hover {
                    color: $color-two;
                }
              }
            }
        }
        h3 {
            font-size: 28px;
        }
        table {
            font-size: 18px;
            tr td:first-child {
                color: rgba($color: #244034, $alpha: 0.6);
                padding: 13px 0;
            }
            tr td:last-child {
                font-weight: 500;
                color: $heading;
            }
        }
    }
}
// FAQ Section Two
.faq-section-two {
    z-index: 1;
    .section-btn {
        position: absolute;
        right: 0;
        top:13px;
    }
    .shape_01 {
        top:1%;
        right: 33%;
        width: 2.1%;
        min-width: 28px;
        animation: rotated 50s infinite linear;
    }
    .shape_02 {
        bottom:3%;
        left: 5%;
        width: 2.1%;
        min-width: 28px;
        animation: rotated 50s infinite linear;
    }
}
// FAQ Section Three
.faq-section-three {
    .tab-content {
        background: #fff;
        border-radius: 30px;
        padding: 0 60px;
        .accordion-style-one .accordion-item:last-child {
            border-bottom: none;
        }
        .accordion-style-one .accordion-item:first-child {
            border-top: none;
        }
    }
    nav {
        .nav {
            border: none;
            .nav-link {
                background: transparent;
                border: 0;
                border-radius: 30px;
                height: 36px;
                font-size: 20px;
                color: rgba($color: #000000, $alpha: 0.5);
                padding: 0 20px;
                margin: 0 2px 10px;
                &.active {
                    background: $color-two;
                    color: #fff;
                }
            }
        }
    }
}
// Pricng
.pricing-section {
    .contact-banner {
        background: #fff;
        border-radius: 30px;
        z-index: 1;
        padding: 48px 45px;
        h2 {
            font-size: 42px;
            line-height: 1.238em;
            margin: 0;
            span {
                font-weight: 700;
                font-style: italic;
                text-decoration-line: underline;
                text-decoration-thickness: 2px;
            }
        }
        .screen_01 {
            right: 3%;
            top:13%;
            width: 12.4%;
        }
    }
}
.pricing-nav {
    .nav {
        border: 2px solid #186560;
        border-radius: 10px;
        overflow: hidden;
        .nav-link {
            width: 150px;
            height: 61px;
            font-weight: 500;
            color: #186560;
            background: transparent;
            padding: 0;
            margin: 0;
            border: 0;
            border-radius: 0;
            &.active {
                background: #186560;
                color: #fff;
            }
        }
    }
    
}
// Error Page
.error-page {
    min-height: 100vh;
    padding: 200px 12px 50px;
    z-index: 1;
    h1 {
        font-size: 140px;
        line-height: 1em;
    }
    h2 {
        font-size: 85px;
        padding: 32px 0 36px;
    }
    .shape_01 {
        right:1%;
        bottom: 20%;
        width: 14.52%;
    }
    .shape_02 {
        left:1%;
        bottom: 30%;
        width: 18.64%;
    }
}
