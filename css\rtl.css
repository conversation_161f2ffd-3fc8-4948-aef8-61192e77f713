/**
 * RTL Styles
 */
@charset "UTF-8";

.horizontal-flip {
  transform: scaleX(-1);
}

body, .theme-main-menu .nav-item .nav-link, .title-four h2,
.h1, h1, .h2, h2, .h3, h3, .h4, h4, .h5, h5, .h6, h6{
  font-family: "IBM Plex Sans Arabic", sans-serif !important;
  font-weight: 700;
}

.h1, h1, .h2, h2, .h3, h3, .h4, h4, .h5, h5, .h6, h6,
.card-style-nineteen .counter-block-one .main-count, .color-deep{
  color:#000;
}
.bsolid-1{
  border: 1px solid #000;
}

.fancy-banner-five.no-bg .bg-wrapper {
    background: url(../images/bg-1.jpg) no-repeat center;
    background-size: cover;
    border-radius: 100px;
}

.card-style-fifteen .media {
    aspect-ratio: 4 / 3;
    overflow: hidden;
    position: relative;
}

.card-style-fifteen .media img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.hero-banner-five .bg-wrapper{
  overflow: hidden;
}

.br-30{
  border-radius: 30px;
}