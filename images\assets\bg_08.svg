<svg width="865" height="441" viewBox="0 0 865 441" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_0_349" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="865" height="441">
<path d="M0 20.9856C0 9.93991 8.9543 0.985596 20 0.985596H845C856.046 0.985596 865 9.9399 865 20.9856V420.986C865 432.031 856.046 440.986 845 440.986H20C8.95433 440.986 0 432.031 0 420.986V20.9856Z" fill="#1F5E59"/>
</mask>
<g mask="url(#mask0_0_349)">
<g filter="url(#filter0_f_0_349)">
<circle cx="1.89193" cy="498.878" r="279.154" transform="rotate(-15 1.89193 498.878)" fill="url(#paint0_linear_0_349)" fill-opacity="0.8"/>
</g>
<path d="M131.773 103.756H317L162 448.986H-41.9999L131.773 103.756Z" fill="url(#paint1_linear_0_349)" fill-opacity="0.54"/>
<path d="M-61.0279 104.986H87L-0.0628281 287.986H-154L-61.0279 104.986Z" fill="url(#paint2_linear_0_349)" fill-opacity="0.37"/>
</g>
<defs>
<filter id="filter0_f_0_349" x="-777.331" y="-280.345" width="1558.45" height="1558.45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_0_349"/>
</filter>
<linearGradient id="paint0_linear_0_349" x1="1.89194" y1="219.724" x2="1.89194" y2="479.036" gradientUnits="userSpaceOnUse">
<stop stop-color="#11342A" stop-opacity="0"/>
<stop offset="1" stop-color="#EBFF6D"/>
</linearGradient>
<linearGradient id="paint1_linear_0_349" x1="231" y1="98.9858" x2="39" y2="440.986" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_0_349" x1="34" y1="110.986" x2="-33.5" y2="287.986" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
