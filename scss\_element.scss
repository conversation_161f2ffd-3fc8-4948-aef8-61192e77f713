// Accordion One
.accordion-style-one {
    .accordion-item {
        border: none;
        border-top: 1px solid #E0E0E0;
        border-radius: 0;
        .accordion-button {
            font-weight: 500;
            font-size: 25px;
            line-height: 1.55em;
            color: $heading;
            padding: 33px 0;
            background: transparent;
            border-radius: 0;
            box-shadow: none;
            &:not(.collapsed) {
                padding-bottom: 20px;
                &:after {
                    content: '-';
                    font-size: 40px;
                }
            }
            &:after {
                content: '+';
                font-weight: 300;
                font-size: 32px;
                background: none;
                width: auto;
                height: auto;
            }
        }
        .accordion-body {
            padding: 0 0 20px 35px;
            p {
                margin: 0;
            }
        }
        &:last-child {
            border-bottom: 1px solid #E0E0E0;
        }
    }
}
// Accordion Two
.accordion-style-two {
    .accordion-item {
        border: none;
        background: #FFFAEB;
        border-radius: 0;
        .accordion-button {
            font-weight: 500;
            font-size: 32px;
            line-height: 1.55em;
            color: #AEA78F;
            padding: 28px 50px;
            background: transparent;
            border-radius: 40px 40px 0 0;
            box-shadow: none;
            &:not(.collapsed) {
                color: #000;
                border-bottom-left-radius: 0 !important;
                border-bottom-right-radius: 0 !important;
                &:after {
                    content: '-';
                    font-size: 1.5em;
                    line-height: 36px;
                }
            }
            &:after {
                content: '+';
                font-weight: 400;
                text-align: center;
                color: #000;
                line-height: 48px;
                font-size: 32px;
                background: none;
                width: 48px;
                height: 48px;
                border-radius: 50%;
                background: #fff;
            }
        }
        .accordion-body {
            padding: 20px 50px 55px;
            h6 {
                font-size: 16px;
                text-transform: uppercase;
                letter-spacing: 0.96px;
                margin-bottom: 15px;
                font-weight: 700;
            }
            ul li {
                display: inline-block;
                color: #272727;
                font-weight: 500;
                background: #fff;
                border-radius: 45px;
                padding: 6px 40px 6px 16px;
                margin-bottom: 13px;
                position: relative;
                z-index: 1;
                &:before {
                    content: '\F633';
                    position: absolute;
                    font-family: $bootstrapFont;
                    font-size: 18px;
                    top:6px;
                    right: 14px;
                    color: #212121;
                }
            }
            .media-wrapper {
                background-position: center;
                background-size: cover;
                border-radius: 30px;
                max-width: 610px;
                .video-icon {
                    width: 90px;
                    height: 90px;
                    background: #fff;
                    &:hover {
                        background: $color-one;
                    }
                }
            }
        }
        &:nth-child(1) {
            border-radius: 40px 40px 0 0;
        }
        &:nth-child(2) {
            background: #FFFAEB;
            .accordion-button {
                background: #FBF4DC;
            }
            .accordion-body {
                background: #FBF4DC;
            }
        }
        &:nth-child(3) {
            background: #f9efcf;
            border-radius: 0 0 40px 40px;
            .accordion-button {
                background: #F9EFCF;
                border-radius: 0 0 40px 40px;
            }
            .accordion-body {
                background: #F9EFCF;
                border-radius: 0 0 40px 40px;
            }
        }
    }
}
// Accordion Three
.accordion-style-three {
    .accordion-item {
        border: none;
        border-bottom: 1px solid #E0E0E0;
        border-radius: 0;
        .accordion-button {
            font-weight: 500;
            font-size: 24px;
            line-height: 1.55em;
            color: $color-five;
            padding: 24px 0;
            background: transparent;
            border-radius: 0;
            box-shadow: none;
            &:after {
                content: '\F286';
                font-family: $bootstrapFont;
                font-size: 20px;
                background: none;
                width: auto;
                height: auto;
            }
        }
        .accordion-body {
            padding: 0 0 8px 35px;
        }
    }
}

// Accordion Four
.accordion-style-four {
    .accordion-item {
        border: none;
        border-radius: 10px;
        overflow: hidden;
        background: transparent;
        margin-bottom: 30px;
        .accordion-button {
            font-weight: 500;
            font-size: 26px;
            line-height: 1.55em;
            color: $color-eight;
            padding: 24px 50px;
            background: #fff;
            border-radius: 0;
            box-shadow: none;
            transition: none;
            span {
                font-weight: normal;
                font-size: 18px;
                display: inline-block;
                margin-left: 22px;
                min-width: 30px;
            }
            &:not(.collapsed) {
                padding-bottom: 15px;
                background: $color-ten;
                color: #fff;
            }
            &:after {
                content: '\F286';
                font-family: $bootstrapFont;
                font-size: 20px;
                background: none;
                width: auto;
                height: auto;
            }
        }
        .accordion-body {
            background: #fff;
            padding: 0 102px 20px 15px;
            background: $color-ten;
            p {
                margin: 0;
                color: rgba($color: #fff, $alpha: 0.7);
            }
        }
    }
}

// Partner Logo  One
.partner-logo-two {
    background: #000;
    border-bottom: 2px solid #000;
    .bg-wrapper {
        background: $color-six;
        border-radius: 30px;
        .wrapper {
            max-width: 1660px;
            padding: 22px 12px;
        }
        .title {
            color: #000;
            font-size: 30px;
        }
        .logo-wrapper {
            font-weight: 700;
            color: #000;
            font-size: 30px;
            letter-spacing: -0.5px;
        }
        .logo-wrapper .br-name {
            margin: 0 10px;
            img {
                width: 15px;
            }
        }
    }
}
// Partner Logo Two
.partner-logo-one {
    .logo {
        width: 100%;
        height: 60px;
    }
}
// Login Modal
.modal .modal-dialog {
    height: auto;
    .modal-content {
        margin: 40px auto;
    }
}
.user-data-form {
    background: #fff;
    margin: 0 auto;
    max-width: 720px;
    border-radius: 20px !important;
    padding: 50px 15px 40px;
    h2 {
        font-size: 42px;
        font-weight: 500;
        margin-bottom: 10px;
    }
    p a {
        color: #31795A;
        &:hover {
            text-decoration: underline;
        }
    }
    .form-wrapper {
        max-width: 565px;
        .input-group-meta {
            input {
                width: 100%;
                height: 55px;
                font-size: 16px;
                border:1px solid rgba($color: #254035, $alpha: 0.06);
                border-radius: 8px;
                padding: 0 20px 0 52px;
                color: #000;
                background: rgba($color: #000, $alpha: 0.04);
            }
            label {
                font-size: 16px;
                font-weight: normal;
                color: rgba(0, 0, 0, 0.5);
                display: block;
                margin-bottom: 6px;
            }
            .placeholder_icon {
                position: absolute;
                height: 55px;
                top:30px;
                left:0;
                bottom: 0;
                width: 50px;
                text-align: center;
                z-index: 1;
                color: rgba(0,0,0,0.45);
                font-size: 17px;
            }
            .placeholder_icon img {
                position: relative;
                top:50%;
                margin: 0 auto;
                transform: translateY(-50%);
            }
            .placeholder_icon span {
                width: 100%;
                height: 100%; 
                cursor: pointer;
                display: block;
                position: relative;
            }
            .placeholder_icon span:before {
                content: '';
                width: 2px;
                height: 26px;
                background: #000;
                position: absolute;
                top:14px;
                left: 24px;
                transform: rotate(45deg);
                z-index: 5;
                transition: all 0.2s ease-in-out;
            }
            .placeholder_icon span.eye-slash:before {opacity: 0;}
        }
    }
    .agreement-checkbox label {
        position: relative;
        font-size: 16px;
        font-weight: 500;
        color: $heading;
        cursor: pointer;
        padding-right: 22px;
        transition: all 0.1s ease-in-out;
    }
    .agreement-checkbox label a {
        color: $color-five;
        text-decoration: underline;
    }
    .agreement-checkbox input[type="checkbox"] {display: none;}
    .agreement-checkbox label:before {
        content: '';
        width: 14px;
        height: 14px;
        line-height: 11px;
        border-radius: 2px;
        border: 2px solid #B3B3B3;
        font-size: 12px;
        text-align: center;
        position: absolute;
        right:0;
        top:4px;
        transition: all 0.1s ease-in-out;
    }
    .agreement-checkbox input[type="checkbox"]:checked + label:before {
        content: "\f633";
        font-family: bootstrap-icons !important;
        background: #000;
        color: #fff;
        border-color:  #000;
    }
    .agreement-checkbox a {
        position: relative;
        font-size: 16px;
        color: rgba($color: #000000, $alpha: 0.5);
    }
    .agreement-checkbox a:hover {
        text-decoration: underline; 
        color: $color-five;
    }
    .line {
        height: 1px;
        width: 100%;
        background: rgba($color: #000000, $alpha: 0.2);
    }
    .social-use-btn {
        font-size: 16px;
        color: #000;
        height: 55px;
        border: 1px solid #E5E5E5;
        border-radius: 7px;
        &:hover {
            background: rgba($color: #000000, $alpha: 0.06);
        }
        img {
            width: 20px;
        }
    }
    .btn-close {
        position: absolute;
        right: 15px;
        top:15px;
        box-shadow: none;
        z-index: 1;
    }
    .nav {
        background: #F0F5F3;
        border-radius: 40px;
        .nav-item {
            width: 50%;
            .nav-link {
                font-weight: 500;
                display: block;
                width: 100%;
                border: 1px solid transparent;
                border-radius: 40px;
                font-size: 20px;
                color: #839B8F;
                &.active {
                    color: $color-two;
                    border-color: $color-two;
                }
            }
        }
    }
}
// Counter One
.counter-block-one .main-count {
    font-size: 64px;
    color: $heading;
}
// Counter Two
.counter-block-two {
    .main-count {
        font-size: 68px;
        color: $heading;
        margin-bottom: -5px;
    }
}
// Counter Three
.counter-block-three {
    .main-count {
        font-size: 70px;
        color: $color-eight;
        margin-bottom: -5px;
    }
}
// Counter Four
.counter-block-four { 
    border: 1px solid $color-eight;
    padding: 30px 15px 20px;
    border-radius: 20px;
    .main-count {
        font-size: 90px;
        margin-bottom: -12px;
    }
}
// Pagination
.pagination-one {
    ul {
        margin: 0 -5;
        li {
            padding: 0 5px;
            font-size: 20px;
            font-weight: 500;
            color: $heading;
            &:not(:last-child) {
                a {
                    width: 40px;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    border-radius: 50%;
                    @include transition(0.2s);
                    &.active,&:hover,&:focus {
                        background: $color-two;
                        color: #fff;
                    }
                }
            }
        }
    }
}
.pagination-two {
    border-top: 1px dashed rgba($color: #000000, $alpha: 0.1);
    border-bottom: 1px dashed rgba($color: #000000, $alpha: 0.1);
    padding: 60px 0;
    .pr-dir {
        font-size: 16px;
        letter-spacing: 0.89px;
        color: #979797;
    }
    .pr-name {
        font-size: 32px;
        color: $heading;
        margin-top: 4px;
    }
    i {
        font-size: 30px;
        width: 75px;
        height: 75px;
        border: 1px solid #000;
        color: #000;
        background: #fff;
        border-radius: 50%;
        text-align: center;
        line-height: 66px;
        @include transition(0.2s);
    }
    a:hover i {
        background: $color-one;
        border-color: $color-one;
    }
    &.border-0 {
        i {
            border: none;
        }
        a:hover i {
            background: #FFE86B;
        }
    }
}
