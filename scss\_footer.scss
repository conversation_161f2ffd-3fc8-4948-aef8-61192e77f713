// Footer One
.footer-one {
    background: $color-three;
    padding-top: 95px;
    .bottom-footer {
        border-top: 1px dashed rgba($color: #fff, $alpha: 0.25);
        margin-top: 32px;
        padding: 40px 0 25px;
    }
    .social-icon a {
        color: #fff;
        font-size: 18px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
        border-radius: 50%;
        background: rgba($color: #fff, $alpha: 0.4);
        @include transition(0.2s);
        &:hover {
            background: $color-one;
            color: $color-two;
        }
    }
    .footer-title {
        font-size: 24px;
        color: #fff;
        margin-bottom: 15px;
    }
    .footer-nav-link a {
        color: rgba($color: #fff, $alpha: 0.7);
        line-height: 46px;
        @include transition(0.2s);
        &:hover {
            color: #fff;
        }
    }
    .footer-newsletter {
        form {
            width: 410px;
            height: 54px;
            position: relative;
            input {
                width: 100%;
                height: 100%;
                font-size: 18px;
                color: rgba($color: #fff, $alpha: 0.7);
                padding: 0 30px 0 80px;
                border: none;
                border-radius: 35px;
                background: rgba($color: #fff, $alpha: 0.1);
                @include placeholder {
                    color: rgba($color: #fff, $alpha: 0.6); 
                }
            }
            button {
                width: 50px;
                height: 50px;
                font-weight: 900;
                text-align: center;
                border-radius: 50%;
                background: $color-one;
                color: $color-two;
                position: absolute;
                left: 0;
                top:2px;
                @include transition(0.2s);
                &:hover {
                    background: #fff;
                }
            }
        }
    }
    .copyright {
        font-size: 18px;
        color: rgba($color: #fff, $alpha: 0.7);
    }
    .bottom-nav {
        margin: 0 -12px;
        a {
            font-size: 18px;
            font-weight: 500;
            margin: 0 12px;
            color: rgba($color: #fff, $alpha: 0.7);
            &:hover {
                color: #fff;
                text-decoration: underline;
            }
        }
    }
}

// Footer Two
.footer-two {
    background: url(../images/media/img_18.jpg) no-repeat center bottom;
    background-size: cover;
    position: relative;
    z-index: 1;
    padding: 110px 15px 135px;
    &.no-bg {
        background: none;
        padding: 0;
        position: statics;
        &:before {
            display: none;
        }
        .bg-wrapper {
            box-shadow: none;
            padding-left: 0;
            padding-right: 0;
            .copyright {
                right: 0;
            }
        }
    }
    &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        bottom: 0;
        left: 0;
        z-index: -1;
        background: linear-gradient(180deg, #F5F8F7 0%, rgba(240, 243, 242, 0.58) 69.88%, rgba(238, 241, 240, 0.15) 100%);
    }
    .bg-wrapper {
        background: #FFFFFF;
        box-shadow: 0px 35px 70px rgba(0, 0, 0, 0.04);
        border-radius: 30px;
        z-index: 1;
        padding: 70px 40px 40px;

        .shape_01 {
            bottom:12%;
            left: 33%;
            width: 2.3%;
            min-width: 28px;
        }
        .shape_02 {
            bottom:38%;
            right: 25%;
            width: 3.8%;
            min-width: 28px;
        }
    }
    .social-icon a {
        color: $color-two;
        font-size: 17px;
        width: 40px;
        height: 40px;
        border: 1px solid $color-two;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
        border-radius: 50%;
        @include transition(0.2s);
        &:hover {
            background: $color-two;
            border-color: $color-two;
            color: #fff;
        }
    }
    .footer-title {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 15px;
    }
    .footer-nav-link a {
        color: rgba($color: #000, $alpha: 0.8);
        line-height: 42px;
        @include transition(0.2s);
        &:hover {
            color: $heading;
            text-decoration: underline;
        }
    }
    .copyright {
        font-size: 18px;
        color: $heading;
        position: absolute;
        right: 40px;
        bottom: 64px;
    }
}
// Footer Three 
.footer-three {
    padding: 75px 0 0;
    position: relative;
    z-index: 1;
    .round-bg {
        width: 350px;
        height: 350px;
        padding: 15px 15px;
        background: #FFF6C6;
        &.color-two {
            background: #F6FFDD;
        }
    }
    .footer-intro p a {
        font-weight: 500;
        font-size: 24px;
        color: #000;
        &:hover {
            color: $heading;
            text-decoration: underline;
        }
    }
    .footer-title {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 15px;
    }
    .footer-nav-link a {
        color: rgba($color: #000, $alpha: 0.5);
        line-height: 46px;
        @include transition(0.2s);
        &:hover {
            color: $heading;
            text-decoration: underline;
        }
    }
    .bottom-footer {
        border-top: 1px solid #e2e2e2;
        margin-top: 40px;
        padding: 40px 0 25px;

        .copyright {
            font-size: 16px;
            color: rgba($color: #000, $alpha: 0.7);
        }
        .bottom-nav {
            margin: 0 -12px;
            a {
                font-size: 18px;
                font-weight: 500;
                margin: 0 12px;
                color: #000;
                &:hover {
                    text-decoration: underline;
                }
            }
        }
        .social-icon {
            margin: 0 -10px;
            a {
                margin: 0 10px;
                &:hover {
                    color: $heading;
                }
            }
        }
    }
    .shape_01 {
        top: -3%;
        right: -3%;
    }
    .shape_02 {
        bottom: 8%;
        left: 33%;
        animation: rotated 48s infinite linear;
    }
}
// Footer Four
.footer-large-wrapper {
    z-index: 1;
    &:before {
        content: '';
        position: absolute;
        width: 100%;
        right: 0;
        bottom: 0;
        top:-20%;
        background: url(../images/assets/bg_04.svg) no-repeat center bottom;
        background-size: cover;
        z-index: -1;
    }
}
.footer-four {
    padding: 75px 0 0;
    .footer-intro {
        p {
            line-height: 30px;
            color: rgba($color: #fff, $alpha: 0.6);
        }
        li {
            margin-top: 15px;
            .icon {
                width: 20px;
            }
            a {
                font-weight: 500;
                color: rgba($color: #fff, $alpha: 0.8);
                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
    .footer-title {
        display: inline-block;
        position: relative;
        font-size: 24px;
        color: #fff;
        font-weight: 500;
        margin-bottom: 15px;
        &:before {
            content: '';
            position: absolute;
            right: 0;
            bottom: -6px;
            width: 20px;
            height: 2px;
            border-radius: 10px;
            background: #D3FF76;
        }
    }
    .footer-nav-link a {
        color: rgba($color: #fff, $alpha: 0.75);
        line-height: 44px;
        font-weight: 300;
        @include transition(0.2s);
        &:hover {
            color: #fff;
            text-decoration: underline;
        }
    }
    .bottom-footer {
        border-top: 1px dashed #37665c;
        margin-top: 40px;
        padding: 40px 0 25px;

        .copyright {
            font-size: 16px;
            color: rgba($color: #fff, $alpha: 0.75);
        }
        .bottom-nav {
            margin: 0 -12px;
            a {
                font-size: 18px;
                margin: 0 12px;
                color: #fff;
                &:hover {
                    text-decoration: underline;
                }
            }
        }
        .social-icon {
            margin: 0 -10px;
            a {
                margin: 0 10px;
                color: #fff;
                &:hover {
                    color: $color-four;
                }
            }
        }
    }
}
// Footer Five
.footer-five {
    background: #000;
    padding: 105px 0 20px;
    z-index: 1;
    overflow: hidden;
    &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        bottom: 0;
        left: 0;
        background: url(../images/assets/bg_07.svg) no-repeat center bottom;
        background-size: cover;
        z-index: -1;
    }
    h2 {
        font-size: 132px;
        line-height: 1.185em;
    }
    .btn-group {
        padding: 15px 55px 15px 15px;
        border-radius: 80px;
        background: $color-six;
        margin: 85px 0 110px;
        h3 {
            font-size: 70px;
            color: #000;
            margin: 0;
            span {
                font-weight: 700;
                font-style: italic;
                text-decoration-line: underline;
                text-decoration-thickness: 4px;
            }
        }
        .round-btn {
            width: 125px;
            height: 125px;
            text-align: center;
            font-size: 55px;
            color: $color-six;
            background: #101010;
            &:hover, &:focus {
                background: $color-two;
                color: #fff;
            }
        }
    }
    .social-icon {
        margin: 0 -20px;
        a {
            color: #fff;
            margin: 0 20px;
            &:hover {
                color: $color-six;
            }
        }
    }
    .bottom-nav {
        margin: 0 -12px;
        a {
            font-size: 18px;
            font-weight: 500;
            margin: 0 12px;
            color: #fff;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    .shape_01 {
        top: 11%;
        right: 10%;
        width: 3.4%;
        animation: rotated 48s infinite linear;
    }
    .shape_02 {
        bottom: 41%;
        left: 13%;
        width: 1.7%;
        animation: rotated 48s infinite linear;
    }
}