@import url("../fonts/Satoshi/css/satoshi.css");
@import url("../fonts/ClashDisplay/css/clash-display.css");
@import url("../fonts/Magnita/Magnita.css");
@import url("../fonts/bootstrap-icons-1.10.2/font.css");
::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track {
  background: #fff;
}

::-webkit-scrollbar-thumb {
  background: #19352D;
  border-radius: 0;
}

body {
  font-family: "Satoshi";
  font-weight: normal;
  font-size: 20px;
  position: relative;
  color: rgba(0, 0, 0, 0.7);
}

.main-page-wrapper {
  overflow-x: hidden;
}

.h1,
h1,
.h2,
h2,
.h3,
h3,
.h4,
h4,
.h5,
h5,
.h6,
h6 {
  font-weight: 500;
  color: #19352D;
}

.h1, h1 {
  font-size: 85px;
  line-height: 1.023em;
}

.h2, h2 {
  font-size: 64px;
  line-height: 1.1em;
}

.h4, h4 {
  font-size: 24px;
}

p {
  line-height: 1.7em;
}

.text-xl {
  font-size: 28px;
  line-height: 1.5em;
}

.text-lg {
  font-size: 24px;
  line-height: 1.583em;
}

.text-md {
  font-size: 22px;
}

hr {
  opacity: 0.09;
  margin: 5px 0;
}

.fw-600 {
  font-weight: 600;
}

.fw-500 {
  font-weight: 500;
}

.border-30 {
  border-radius: 30px;
}

.border-40 {
  border-radius: 40px;
}

.border-100 {
  border-radius: 100px;
}

.shapes {
  position: absolute;
  z-index: -1;
}

.light-bg {
  background-color: #EDF1EE;
}

.light-bg-deep {
  background-color: #EBF3EE;
}

.font-magnita {
  font-family: "Magnita";
}

.light-bg-page-wrapper {
  background: #EDF8EB;
}

.color-deep {
  color: #1F5E59;
}

.box-layout {
  margin: 30px;
}

.ctn-preloader {
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 999999;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.ctn-preloader .icon {
  animation: rotated 8s infinite linear;
}
.ctn-preloader .txt-loading {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  margin-top: 30px;
}
.ctn-preloader .txt-loading .letters-loading {
  font-family: "ClashDisplay";
  font-weight: 500;
  letter-spacing: 8px;
  display: inline-block;
  color: rgba(12, 58, 48, 0.12);
  position: relative;
  font-size: 40px;
  line-height: 30px;
}
.ctn-preloader .txt-loading .letters-loading::before {
  animation: letters-loading 4s infinite;
  color: #0C3A30;
  content: attr(data-text-preloader);
  left: 0;
  opacity: 0;
  top: 0;
  line-height: 30px;
  position: absolute;
}
.ctn-preloader .txt-loading .letters-loading:nth-child(2):before {
  animation-delay: 0.2s;
}
.ctn-preloader .txt-loading .letters-loading:nth-child(3):before {
  animation-delay: 0.4s;
}
.ctn-preloader .txt-loading .letters-loading:nth-child(4):before {
  animation-delay: 0.6s;
}
.ctn-preloader .txt-loading .letters-loading:nth-child(5):before {
  animation-delay: 0.8s;
}
.ctn-preloader .txt-loading .letters-loading:nth-child(6):before {
  animation-delay: 1s;
}
.ctn-preloader .txt-loading .letters-loading:nth-child(7):before {
  animation-delay: 1.2s;
}
.ctn-preloader .txt-loading .letters-loading:nth-child(8):before {
  animation-delay: 1.4s;
}

@keyframes spinner {
  to {
    transform: rotateZ(360deg);
  }
}
@keyframes letters-loading {
  0%, 75%, 100% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  25%, 50% {
    opacity: 1;
    transform: rotateY(0deg);
  }
}
.scroll-top {
  width: 35px;
  height: 35px;
  line-height: 32px;
  font-weight: 900;
  position: fixed;
  bottom: 20px;
  left: 5px;
  z-index: 99;
  text-align: center;
  color: #19352D;
  font-size: 25px;
  cursor: pointer;
  border-radius: 50%;
  background: #CFFF45;
  transition: all 0.3s ease-in-out;
}
.scroll-top:after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 100%;
  left: 5%;
  height: 10px;
  width: 90%;
  opacity: 1;
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0) 80%);
}

.tran3s {
  transition: all 0.3s ease-in-out;
}

.tran4s {
  transition: all 0.4s ease-in-out;
}

.tran5s {
  transition: all 0.5s ease-in-out;
}

.tran6s {
  transition: all 0.6s ease-in-out;
}

.btn-one {
  font-weight: 700;
  font-size: 18px;
  line-height: 48px;
  padding: 0 30px;
  text-align: center;
  border-radius: 40px;
  color: #000;
  background-color: #CFFF45;
  transition: all 0.3s ease-in-out 0s;
}
.btn-one:hover {
  background-color: #29594B;
  color: #fff;
}

.btn-two {
  font-weight: 500;
  font-size: 20px;
  color: #fff;
  line-height: 52px;
  text-align: center;
  padding: 0 35px;
  border: 1px solid #fff;
  border-radius: 50px;
  transition: all 0.3s ease-in-out 0s;
}
.btn-two .icon {
  width: 1.35em;
}
.btn-two:hover {
  background: #29594B;
  border-color: #29594B;
}

.btn-three {
  font-size: 18px;
  font-weight: 700;
  color: #000;
}
.btn-three img {
  width: 22px;
  margin-top: 3px;
}
.btn-three:hover span {
  text-decoration: underline;
}
.btn-three.border-style {
  border: 1px solid #000;
  border-radius: 40px;
  padding: 12px 25px;
  transition: all 0.3s ease-in-out 0s;
}
.btn-three.border-style:hover {
  background-color: #D0FF45;
  border-color: #D0FF45;
}
.btn-three.border-style:hover span {
  text-decoration: none;
}

.btn-four {
  font-weight: 700;
  font-size: 18px;
  line-height: 50px;
  padding: 0 38px;
  text-align: center;
  border-radius: 50px;
  color: #fff;
  background-color: #29594B;
  transition: all 0.3s ease-in-out 0s;
}
.btn-four:hover {
  background-color: #CFFF45;
  color: #000;
}

.btn-five .text {
  font-weight: 500;
  font-style: italic;
  color: #19352D;
  text-decoration: underline;
}
.btn-five .icon {
  width: 48px;
  height: 48px;
  background: #29594B;
  color: #fff;
  font-size: 22px;
  margin-right: 12px;
}
.btn-five:hover .icon {
  background: #CFFF45;
  color: #19352D;
}

.btn-six {
  font-weight: 700;
  font-size: 18px;
  line-height: 48px;
  padding: 0 38px;
  text-align: center;
  border-radius: 50px;
  color: #29594B;
  border: 1px solid #29594B;
  transition: all 0.3s ease-in-out 0s;
}
.btn-six:hover {
  background-color: #CFFF45;
  border-color: #CFFF45;
  color: #000;
}

.btn-seven .text {
  font-weight: 500;
  font-style: italic;
  color: #19352D;
}
.btn-seven .icon {
  width: 50px;
  height: 50px;
  background: #29594B;
  margin-right: 45px;
  position: relative;
}
.btn-seven .icon:before {
  content: "";
  position: absolute;
  width: 30px;
  height: 2px;
  background: #29594B;
  right: -30px;
  top: 50%;
}
.btn-seven:hover .text {
  text-decoration: underline;
}

.btn-eight .text {
  font-weight: 700;
  font-size: 18px;
  color: #19352D;
}
.btn-eight .icon {
  width: 45px;
  height: 45px;
  border: 1px solid #fff;
  font-size: 20px;
  background: #29594B;
  color: #fff;
  margin-right: 10px;
  transition: all 0.3s ease-in-out 0s;
}
.btn-eight:hover .text {
  text-decoration: underline;
}
.btn-eight:hover .icon {
  background: #CFFF45;
  color: #19352D;
}

.btn-nine {
  font-size: 40px;
  width: 105px;
  height: 105px;
  color: #19352D;
  border: 1px solid #29594B;
}
.btn-nine:hover {
  background: #CFFF45;
  border-color: #CFFF45;
}

.btn-ten {
  font-size: 18px;
  font-weight: 700;
  padding: 0 35px;
  min-width: 160px;
  text-align: center;
  background: #101010;
  color: #fff;
  line-height: 50px;
}
.btn-ten:hover {
  background: #29594B;
}

.btn-eleven .text {
  font-weight: 700;
  font-size: 18px;
  color: #000;
}
.btn-eleven .icon {
  width: 45px;
  height: 45px;
  background: #000;
  margin-right: 38px;
  position: relative;
}
.btn-eleven .icon img {
  width: 30px;
}
.btn-eleven .icon:before {
  content: "";
  position: absolute;
  width: 26px;
  height: 2px;
  background: #000;
  right: -26px;
  top: 50%;
}
.btn-eleven:hover .text {
  text-decoration: underline;
}
.btn-eleven:hover .icon {
  background: #29594B;
}

.btn-twelve {
  font-family: "ClashDisplay";
  font-weight: 600;
  text-transform: uppercase;
  font-size: 16px;
  line-height: 50px;
  padding: 0 30px;
  text-align: center;
  border-radius: 40px;
  color: #000;
  background-color: #E6FD5A;
  transition: all 0.3s ease-in-out 0s;
}
.btn-twelve:hover {
  background-color: #000;
  color: #fff;
}

.btn-thirteen {
  font-family: "ClashDisplay";
  font-weight: 600;
  text-transform: uppercase;
  font-size: 16px;
  line-height: 50px;
  padding: 0 30px;
  text-align: center;
  border-radius: 40px;
  color: #fff;
  min-width: 175px;
  background-color: #202020;
  transition: all 0.3s ease-in-out 0s;
}
.btn-thirteen:hover {
  background-color: #E6FD5A;
  color: #000;
}

.btn-fourteen {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 20px;
  line-height: 46px;
  text-align: center;
  color: #fff;
  background-color: #202020;
  transition: all 0.3s ease-in-out 0s;
}
.btn-fourteen:hover {
  background-color: #E6FD5A;
  color: #000;
}

.btn-fifteen {
  line-height: 46px;
  border: 2px solid #000;
  border-radius: 40px;
  font-size: 18px;
  font-weight: 700;
  min-width: 135px;
  padding: 0 30px;
  text-align: center;
  color: #000;
  background-color: #DFFF5E;
  transition: all 0.3s ease-in-out 0s;
}
.btn-fifteen:hover {
  background-color: #000;
  color: #fff;
}

.btn-sixteen {
  color: #000;
  font-weight: 700;
  font-size: 18px;
  padding: 0 35px;
  line-height: 50px;
  border: 1px solid #000;
  position: relative;
  display: inline-block;
  background: #fff;
}
.btn-sixteen:before {
  position: absolute;
  content: "";
  top: -14px;
  height: 14px;
  width: calc(100% + 3px);
  left: 6px;
  transform: skewX(-45deg);
  background: #000;
  transition: all 0.3s ease-in-out 0s;
}
.btn-sixteen:after {
  position: absolute;
  content: "";
  right: -16px;
  height: calc(100% + 1px);
  width: 15px;
  top: -7px;
  transform: skewY(-45deg);
  background: #000;
  transition: all 0.3s ease-in-out 0s;
}
.btn-sixteen:hover {
  background: #DFFF5E;
}

.btn-seventeen {
  color: #000;
  font-weight: 500;
  padding: 0 30px;
  line-height: 55px;
  border-radius: 40px;
  background: #FFE86B;
}
.btn-seventeen i {
  margin-right: 8px;
}
.btn-seventeen:hover {
  background: #DFFF5E;
}

.btn-eighteen {
  font-size: 18px;
  color: #fff;
  font-weight: 700;
  padding: 0 38px;
  line-height: 52px;
  border-radius: 10px;
  background: #1F5E59;
  text-align: center;
}
.btn-eighteen:hover {
  background: #CFFF45;
  color: #19352D;
}

.btn-nineteen {
  font-size: 18px;
  color: #1F5E59;
  font-weight: 700;
  padding: 0 30px;
  line-height: 50px;
  border: 1px solid #1F5E59;
  border-radius: 10px;
  text-align: center;
}
.btn-nineteen:hover {
  background: #1F5E59;
  color: #fff;
}

.btn-twenty {
  font-weight: 500;
  text-transform: uppercase;
  font-size: 16px;
  line-height: 55px;
  padding: 0 45px;
  text-align: center;
  border-radius: 40px;
  color: #1F5E59;
  min-width: 175px;
  background-color: #D0FF45;
  transition: all 0.3s ease-in-out 0s;
}
.btn-twenty:hover {
  background-color: #E6FD5A;
  color: #000;
}

.btn-twentyOne {
  font-weight: 500;
  font-size: 18px;
  line-height: 55px;
  padding: 0 38px;
  text-align: center;
  border-radius: 40px;
  color: #000;
  background-color: #FFDB1E;
  transition: all 0.3s ease-in-out 0s;
}
.btn-twentyOne:hover {
  background-color: #000;
  color: #fff;
}

.btn-twentytwo {
  font-weight: 500;
  font-size: 18px;
  line-height: 53px;
  padding: 0 38px;
  text-align: center;
  border-radius: 40px;
  color: #fff;
  border: 1px solid #fff;
  transition: all 0.3s ease-in-out 0s;
}
.btn-twentytwo:hover {
  background-color: #CFFF45;
  border-color: #CFFF45;
  color: #000;
}

.title-one .upper-title {
  font-weight: 500;
  margin-bottom: 14px;
  color: #46846A;
}
.title-one h2 {
  font-weight: 700;
}

.title-two .upper-title {
  font-weight: 500;
  margin-bottom: 5px;
  color: #1CA161;
}
.title-two h2 {
  font-size: 72px;
  font-weight: 700;
  line-height: 1.027em;
  color: #0E3E2F;
}
.title-two h2 span {
  color: #E6FD5A;
}
.title-two .upper-title-two {
  text-transform: uppercase;
  font-size: 18px;
  letter-spacing: 2px;
  color: rgba(0, 0, 0, 0.4);
}

.title-three .upper-title {
  font-weight: 700;
  letter-spacing: 2px;
  font-size: 14px;
  text-transform: uppercase;
  margin-bottom: 15px;
  color: #1CA161;
}
.title-three h2 {
  font-weight: 700;
  line-height: 1.03em;
  color: #0E3E2F;
}

.title-four h2 {
  font-family: "Magnita";
  font-size: 68px;
  line-height: 1.323em;
  color: #000;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

a {
  text-decoration: none;
  display: inline-block;
  color: inherit;
}
a:hover, a:focus, a:visited {
  text-decoration: none;
  outline: none;
}

img {
  max-width: 100%;
  display: block;
}

button {
  border: none;
  outline: none;
  box-shadow: none;
  display: block;
  padding: 0;
  cursor: pointer;
  background: transparent;
  color: inherit;
}

button:focus {
  outline: none;
}

[type=email], [type=number], [type=tel], [type=url] {
  outline: none;
  direction: rtl;
}

input, textarea {
  outline: none;
  box-shadow: none;
  transition: all 0.3s ease-in-out;
}

audio, video, canvas {
  max-width: 100%;
}

iframe {
  border: none !important;
}

.style-none {
  list-style: none;
  padding-right: 0;
  margin-bottom: 0;
}

.p0 {
  padding: 0 !important;
}

.m0 {
  margin: 0 !important;
}

.theme-mb-0 {
  margin-bottom: 0;
}

.theme-pb-0 {
  padding-bottom: 0;
}

.pt-5 {
  padding-top: 5px !important;
}

.pt-10 {
  padding-top: 10px;
}

.pt-15 {
  padding-top: 15px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-25 {
  padding-top: 25px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-45 {
  padding-top: 45px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-55 {
  padding-top: 55px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-65 {
  padding-top: 65px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-75 {
  padding-top: 75px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-85 {
  padding-top: 85px;
}

.pt-90 {
  padding-top: 90px;
}

.pt-95 {
  padding-top: 95px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-110 {
  padding-top: 110px;
}

.pt-120 {
  padding-top: 120px;
}

.pt-130 {
  padding-top: 130px;
}

.pt-140 {
  padding-top: 140px;
}

.pt-150 {
  padding-top: 150px;
}

.pt-160 {
  padding-top: 160px;
}

.pt-170 {
  padding-top: 170px;
}

.pt-180 {
  padding-top: 180px;
}

.pt-190 {
  padding-top: 190px;
}

.pt-200 {
  padding-top: 200px;
}

.pt-225 {
  padding-top: 225px;
}

.pt-250 {
  padding-top: 250px;
}

.pt-300 {
  padding-top: 300px;
}

.pt-350 {
  padding-top: 350px;
}

.pb-5 {
  padding-bottom: 5px !important;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-65 {
  padding-bottom: 65px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pb-75 {
  padding-bottom: 75px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-85 {
  padding-bottom: 85px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-95 {
  padding-bottom: 95px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pb-130 {
  padding-bottom: 130px;
}

.pb-140 {
  padding-bottom: 140px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pb-170 {
  padding-bottom: 170px;
}

.pb-180 {
  padding-bottom: 180px;
}

.pb-190 {
  padding-bottom: 190px;
}

.pb-200 {
  padding-bottom: 200px;
}

.pb-225 {
  padding-bottom: 225px;
}

.pb-250 {
  padding-bottom: 250px;
}

.pb-300 {
  padding-bottom: 300px;
}

.pb-350 {
  padding-bottom: 350px;
}

.mt-5 {
  margin-top: 5px !important;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-35 {
  margin-top: 35px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-45 {
  margin-top: 45px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-55 {
  margin-top: 55px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-65 {
  margin-top: 65px;
}

.mt-70 {
  margin-top: 70px;
}

.mt-75 {
  margin-top: 75px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-85 {
  margin-top: 85px;
}

.mt-90 {
  margin-top: 90px;
}

.mt-95 {
  margin-top: 95px;
}

.mt-100 {
  margin-top: 100px;
}

.mt-110 {
  margin-top: 110px;
}

.mt-120 {
  margin-top: 120px;
}

.mt-130 {
  margin-top: 130px;
}

.mt-140 {
  margin-top: 140px;
}

.mt-150 {
  margin-top: 150px;
}

.mt-160 {
  margin-top: 160px;
}

.mt-170 {
  margin-top: 170px;
}

.mt-180 {
  margin-top: 180px;
}

.mt-190 {
  margin-top: 190px;
}

.mt-200 {
  margin-top: 200px;
}

.mt-225 {
  margin-top: 225px;
}

.mt-250 {
  margin-top: 250px;
}

.mt-300 {
  margin-top: 300px;
}

.mt-350 {
  margin-top: 350px;
}

.mb-5 {
  margin-bottom: 5px !important;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-55 {
  margin-bottom: 55px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-65 {
  margin-bottom: 65px;
}

.mb-70 {
  margin-bottom: 70px;
}

.mb-75 {
  margin-bottom: 75px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-85 {
  margin-bottom: 85px;
}

.mb-90 {
  margin-bottom: 90px;
}

.mb-95 {
  margin-bottom: 95px;
}

.mb-100 {
  margin-bottom: 100px;
}

.mb-110 {
  margin-bottom: 110px;
}

.mb-120 {
  margin-bottom: 120px;
}

.mb-130 {
  margin-bottom: 130px;
}

.mb-140 {
  margin-bottom: 140px;
}

.mb-150 {
  margin-bottom: 150px;
}

.mb-160 {
  margin-bottom: 160px;
}

.mb-170 {
  margin-bottom: 170px;
}

.mb-180 {
  margin-bottom: 180px;
}

.mb-190 {
  margin-bottom: 190px;
}

.mb-200 {
  margin-bottom: 200px;
}

.mb-225 {
  margin-bottom: 225px;
}

.mb-250 {
  margin-bottom: 250px;
}

.mb-300 {
  margin-bottom: 300px;
}

.mb-350 {
  margin-bottom: 350px;
}

@media (max-width: 1399px) {
  .xl-p0 {
    padding: 0 !important;
  }
  .xl-m0 {
    margin: 0 !important;
  }
  .xl-pt-10 {
    padding-top: 10px !important;
  }
  .xl-pt-20 {
    padding-top: 20px !important;
  }
  .xl-pt-30 {
    padding-top: 30px !important;
  }
  .xl-pt-40 {
    padding-top: 40px !important;
  }
  .xl-pt-50 {
    padding-top: 50px !important;
  }
  .xl-pt-60 {
    padding-top: 60px !important;
  }
  .xl-pt-70 {
    padding-top: 70px !important;
  }
  .xl-pt-80 {
    padding-top: 80px !important;
  }
  .xl-pt-90 {
    padding-top: 90px !important;
  }
  .xl-pt-100 {
    padding-top: 100px !important;
  }
  .xl-pt-110 {
    padding-top: 110px !important;
  }
  .xl-pt-120 {
    padding-top: 120px !important;
  }
  .xl-pt-130 {
    padding-top: 130px !important;
  }
  .xl-pt-140 {
    padding-top: 140px !important;
  }
  .xl-pt-150 {
    padding-top: 150px !important;
  }
  .xl-pt-200 {
    padding-top: 200px !important;
  }
  .xl-pb-10 {
    padding-bottom: 10px !important;
  }
  .xl-pb-20 {
    padding-bottom: 20px !important;
  }
  .xl-pb-30 {
    padding-bottom: 30px !important;
  }
  .xl-pb-40 {
    padding-bottom: 40px !important;
  }
  .xl-pb-50 {
    padding-bottom: 50px !important;
  }
  .xl-pb-60 {
    padding-bottom: 60px !important;
  }
  .xl-pb-70 {
    padding-bottom: 70px !important;
  }
  .xl-pb-80 {
    padding-bottom: 80px !important;
  }
  .xl-pb-90 {
    padding-bottom: 90px !important;
  }
  .xl-pb-100 {
    padding-bottom: 100px !important;
  }
  .xl-pb-110 {
    padding-bottom: 110px !important;
  }
  .xl-pb-120 {
    padding-bottom: 120px !important;
  }
  .xl-pb-130 {
    padding-bottom: 130px !important;
  }
  .xl-pb-140 {
    padding-bottom: 140px !important;
  }
  .xl-pb-150 {
    padding-bottom: 150px !important;
  }
  .xl-pb-200 {
    padding-bottom: 200px !important;
  }
  .xl-mt-10 {
    margin-top: 10px !important;
  }
  .xl-mt-20 {
    margin-top: 20px !important;
  }
  .xl-mt-30 {
    margin-top: 30px !important;
  }
  .xl-mt-40 {
    margin-top: 40px !important;
  }
  .xl-mt-50 {
    margin-top: 50px !important;
  }
  .xl-mt-60 {
    margin-top: 60px !important;
  }
  .xl-mt-70 {
    margin-top: 70px !important;
  }
  .xl-mt-80 {
    margin-top: 80px !important;
  }
  .xl-mt-90 {
    margin-top: 90px !important;
  }
  .xl-mt-100 {
    margin-top: 100px !important;
  }
  .xl-mt-110 {
    margin-top: 110px !important;
  }
  .xl-mt-120 {
    margin-top: 120px !important;
  }
  .xl-mt-130 {
    margin-top: 130px !important;
  }
  .xl-mt-140 {
    margin-top: 140px !important;
  }
  .xl-mt-150 {
    margin-top: 150px !important;
  }
  .xl-mt-200 {
    margin-top: 200px !important;
  }
  .xl-mb-10 {
    margin-bottom: 10px !important;
  }
  .xl-mb-20 {
    margin-bottom: 20px !important;
  }
  .xl-mb-30 {
    margin-bottom: 30px !important;
  }
  .xl-mb-40 {
    margin-bottom: 40px !important;
  }
  .xl-mb-50 {
    margin-bottom: 50px !important;
  }
  .xl-mb-60 {
    margin-bottom: 60px !important;
  }
  .xl-mb-70 {
    margin-bottom: 70px !important;
  }
  .xl-mb-80 {
    margin-bottom: 80px !important;
  }
  .xl-mb-90 {
    margin-bottom: 90px !important;
  }
  .xl-mb-100 {
    margin-bottom: 100px !important;
  }
  .xl-mb-110 {
    margin-bottom: 110px !important;
  }
  .xl-mb-120 {
    margin-bottom: 120px !important;
  }
  .xl-mb-130 {
    margin-bottom: 130px !important;
  }
  .xl-mb-140 {
    margin-bottom: 140px !important;
  }
  .xl-mb-150 {
    margin-bottom: 150px !important;
  }
  .xl-mb-200 {
    margin-bottom: 200px !important;
  }
}
/*(max-width: 1399px)*/
@media (max-width: 1199px) {
  .lg-p0 {
    padding: 0 !important;
  }
  .lg-m0 {
    margin: 0 !important;
  }
  .lg-pt-10 {
    padding-top: 10px !important;
  }
  .lg-pt-20 {
    padding-top: 20px !important;
  }
  .lg-pt-30 {
    padding-top: 30px !important;
  }
  .lg-pt-40 {
    padding-top: 40px !important;
  }
  .lg-pt-50 {
    padding-top: 50px !important;
  }
  .lg-pt-60 {
    padding-top: 60px !important;
  }
  .lg-pt-70 {
    padding-top: 70px !important;
  }
  .lg-pt-80 {
    padding-top: 80px !important;
  }
  .lg-pt-90 {
    padding-top: 90px !important;
  }
  .lg-pt-100 {
    padding-top: 100px !important;
  }
  .lg-pt-110 {
    padding-top: 110px !important;
  }
  .lg-pt-120 {
    padding-top: 120px !important;
  }
  .lg-pt-130 {
    padding-top: 130px !important;
  }
  .lg-pt-140 {
    padding-top: 140px !important;
  }
  .lg-pt-150 {
    padding-top: 150px !important;
  }
  .lg-pt-200 {
    padding-top: 200px !important;
  }
  .lg-pb-10 {
    padding-bottom: 10px !important;
  }
  .lg-pb-20 {
    padding-bottom: 20px !important;
  }
  .lg-pb-30 {
    padding-bottom: 30px !important;
  }
  .lg-pb-40 {
    padding-bottom: 40px !important;
  }
  .lg-pb-50 {
    padding-bottom: 50px !important;
  }
  .lg-pb-60 {
    padding-bottom: 60px !important;
  }
  .lg-pb-70 {
    padding-bottom: 70px !important;
  }
  .lg-pb-80 {
    padding-bottom: 80px !important;
  }
  .lg-pb-90 {
    padding-bottom: 90px !important;
  }
  .lg-pb-100 {
    padding-bottom: 100px !important;
  }
  .lg-pb-110 {
    padding-bottom: 110px !important;
  }
  .lg-pb-120 {
    padding-bottom: 120px !important;
  }
  .lg-pb-130 {
    padding-bottom: 130px !important;
  }
  .lg-pb-140 {
    padding-bottom: 140px !important;
  }
  .lg-pb-150 {
    padding-bottom: 150px !important;
  }
  .lg-pb-200 {
    padding-bottom: 200px !important;
  }
  .lg-mt-10 {
    margin-top: 10px !important;
  }
  .lg-mt-20 {
    margin-top: 20px !important;
  }
  .lg-mt-30 {
    margin-top: 30px !important;
  }
  .lg-mt-40 {
    margin-top: 40px !important;
  }
  .lg-mt-50 {
    margin-top: 50px !important;
  }
  .lg-mt-60 {
    margin-top: 60px !important;
  }
  .lg-mt-70 {
    margin-top: 70px !important;
  }
  .lg-mt-80 {
    margin-top: 80px !important;
  }
  .lg-mt-90 {
    margin-top: 90px !important;
  }
  .lg-mt-100 {
    margin-top: 100px !important;
  }
  .lg-mt-110 {
    margin-top: 110px !important;
  }
  .lg-mt-120 {
    margin-top: 120px !important;
  }
  .lg-mt-130 {
    margin-top: 130px !important;
  }
  .lg-mt-140 {
    margin-top: 140px !important;
  }
  .lg-mt-150 {
    margin-top: 150px !important;
  }
  .lg-mt-200 {
    margin-top: 200px !important;
  }
  .lg-mb-10 {
    margin-bottom: 10px !important;
  }
  .lg-mb-20 {
    margin-bottom: 20px !important;
  }
  .lg-mb-30 {
    margin-bottom: 30px !important;
  }
  .lg-mb-40 {
    margin-bottom: 40px !important;
  }
  .lg-mb-50 {
    margin-bottom: 50px !important;
  }
  .lg-mb-60 {
    margin-bottom: 60px !important;
  }
  .lg-mb-70 {
    margin-bottom: 70px !important;
  }
  .lg-mb-80 {
    margin-bottom: 80px !important;
  }
  .lg-mb-90 {
    margin-bottom: 90px !important;
  }
  .lg-mb-100 {
    margin-bottom: 100px !important;
  }
  .lg-mb-110 {
    margin-bottom: 110px !important;
  }
  .lg-mb-120 {
    margin-bottom: 120px !important;
  }
  .lg-mb-130 {
    margin-bottom: 130px !important;
  }
  .lg-mb-140 {
    margin-bottom: 140px !important;
  }
  .lg-mb-150 {
    margin-bottom: 150px !important;
  }
  .lg-mb-200 {
    margin-bottom: 200px !important;
  }
}
/*(max-width: 1199px)*/
@media (max-width: 991px) {
  .md-p0 {
    padding: 0 !important;
  }
  .md-m0 {
    margin: 0 !important;
  }
  .md-pt-10 {
    padding-top: 10px !important;
  }
  .md-pt-20 {
    padding-top: 20px !important;
  }
  .md-pt-30 {
    padding-top: 30px !important;
  }
  .md-pt-40 {
    padding-top: 40px !important;
  }
  .md-pt-50 {
    padding-top: 50px !important;
  }
  .md-pt-60 {
    padding-top: 60px !important;
  }
  .md-pt-70 {
    padding-top: 70px !important;
  }
  .md-pt-80 {
    padding-top: 80px !important;
  }
  .md-pt-90 {
    padding-top: 90px !important;
  }
  .md-pt-100 {
    padding-top: 100px !important;
  }
  .md-pt-110 {
    padding-top: 110px !important;
  }
  .md-pt-120 {
    padding-top: 120px !important;
  }
  .md-pt-130 {
    padding-top: 130px !important;
  }
  .md-pt-140 {
    padding-top: 140px !important;
  }
  .md-pt-150 {
    padding-top: 150px !important;
  }
  .md-pt-200 {
    padding-top: 200px !important;
  }
  .md-pb-10 {
    padding-bottom: 10px !important;
  }
  .md-pb-20 {
    padding-bottom: 20px !important;
  }
  .md-pb-30 {
    padding-bottom: 30px !important;
  }
  .md-pb-40 {
    padding-bottom: 40px !important;
  }
  .md-pb-50 {
    padding-bottom: 50px !important;
  }
  .md-pb-60 {
    padding-bottom: 60px !important;
  }
  .md-pb-70 {
    padding-bottom: 70px !important;
  }
  .md-pb-80 {
    padding-bottom: 80px !important;
  }
  .md-pb-90 {
    padding-bottom: 90px !important;
  }
  .md-pb-100 {
    padding-bottom: 100px !important;
  }
  .md-pb-110 {
    padding-bottom: 110px !important;
  }
  .md-pb-120 {
    padding-bottom: 120px !important;
  }
  .md-pb-130 {
    padding-bottom: 130px !important;
  }
  .md-pb-140 {
    padding-bottom: 140px !important;
  }
  .md-pb-150 {
    padding-bottom: 150px !important;
  }
  .md-pb-200 {
    padding-bottom: 200px !important;
  }
  .md-mt-10 {
    margin-top: 10px !important;
  }
  .md-mt-20 {
    margin-top: 20px !important;
  }
  .md-mt-30 {
    margin-top: 30px !important;
  }
  .md-mt-40 {
    margin-top: 40px !important;
  }
  .md-mt-50 {
    margin-top: 50px !important;
  }
  .md-mt-60 {
    margin-top: 60px !important;
  }
  .md-mt-70 {
    margin-top: 70px !important;
  }
  .md-mt-80 {
    margin-top: 80px !important;
  }
  .md-mt-90 {
    margin-top: 90px !important;
  }
  .md-mt-100 {
    margin-top: 100px !important;
  }
  .md-mt-110 {
    margin-top: 110px !important;
  }
  .md-mt-120 {
    margin-top: 120px !important;
  }
  .md-mt-130 {
    margin-top: 130px !important;
  }
  .md-mt-140 {
    margin-top: 140px !important;
  }
  .md-mt-150 {
    margin-top: 150px !important;
  }
  .md-mt-200 {
    margin-top: 200px !important;
  }
  .md-mb-10 {
    margin-bottom: 10px !important;
  }
  .md-mb-20 {
    margin-bottom: 20px !important;
  }
  .md-mb-30 {
    margin-bottom: 30px !important;
  }
  .md-mb-40 {
    margin-bottom: 40px !important;
  }
  .md-mb-50 {
    margin-bottom: 50px !important;
  }
  .md-mb-60 {
    margin-bottom: 60px !important;
  }
  .md-mb-70 {
    margin-bottom: 70px !important;
  }
  .md-mb-80 {
    margin-bottom: 80px !important;
  }
  .md-mb-90 {
    margin-bottom: 90px !important;
  }
  .md-mb-100 {
    margin-bottom: 100px !important;
  }
  .md-mb-110 {
    margin-bottom: 110px !important;
  }
  .md-mb-120 {
    margin-bottom: 120px !important;
  }
  .md-mb-130 {
    margin-bottom: 130px !important;
  }
  .md-mb-140 {
    margin-bottom: 140px !important;
  }
  .md-mb-150 {
    margin-bottom: 150px !important;
  }
  .md-mb-200 {
    margin-bottom: 200px !important;
  }
}
/*(max-width: 991px)*/
@media (max-width: 767px) {
  .sm-p0 {
    padding: 0 !important;
  }
  .sm-m0 {
    margin: 0 !important;
  }
  .sm-pt-10 {
    padding-top: 10px !important;
  }
  .sm-pt-20 {
    padding-top: 20px !important;
  }
  .sm-pt-30 {
    padding-top: 30px !important;
  }
  .sm-pt-40 {
    padding-top: 40px !important;
  }
  .sm-pt-50 {
    padding-top: 50px !important;
  }
  .sm-pt-60 {
    padding-top: 60px !important;
  }
  .sm-pt-70 {
    padding-top: 70px !important;
  }
  .sm-pt-80 {
    padding-top: 80px !important;
  }
  .sm-pt-90 {
    padding-top: 90px !important;
  }
  .sm-pt-100 {
    padding-top: 100px !important;
  }
  .sm-pt-110 {
    padding-top: 110px !important;
  }
  .sm-pt-120 {
    padding-top: 120px !important;
  }
  .sm-pt-130 {
    padding-top: 130px !important;
  }
  .sm-pt-140 {
    padding-top: 140px !important;
  }
  .sm-pt-150 {
    padding-top: 150px !important;
  }
  .sm-pt-200 {
    padding-top: 200px !important;
  }
  .sm-pb-10 {
    padding-bottom: 10px !important;
  }
  .sm-pb-20 {
    padding-bottom: 20px !important;
  }
  .sm-pb-30 {
    padding-bottom: 30px !important;
  }
  .sm-pb-40 {
    padding-bottom: 40px !important;
  }
  .sm-pb-50 {
    padding-bottom: 50px !important;
  }
  .sm-pb-60 {
    padding-bottom: 60px !important;
  }
  .sm-pb-70 {
    padding-bottom: 70px !important;
  }
  .sm-pb-80 {
    padding-bottom: 80px !important;
  }
  .sm-pb-90 {
    padding-bottom: 90px !important;
  }
  .sm-pb-100 {
    padding-bottom: 100px !important;
  }
  .sm-pb-110 {
    padding-bottom: 110px !important;
  }
  .sm-pb-120 {
    padding-bottom: 120px !important;
  }
  .sm-pb-130 {
    padding-bottom: 130px !important;
  }
  .sm-pb-140 {
    padding-bottom: 140px !important;
  }
  .sm-pb-150 {
    padding-bottom: 150px !important;
  }
  .sm-pb-200 {
    padding-bottom: 200px !important;
  }
  .sm-mt-10 {
    margin-top: 10px !important;
  }
  .sm-mt-20 {
    margin-top: 20px !important;
  }
  .sm-mt-30 {
    margin-top: 30px !important;
  }
  .sm-mt-40 {
    margin-top: 40px !important;
  }
  .sm-mt-50 {
    margin-top: 50px !important;
  }
  .sm-mt-60 {
    margin-top: 60px !important;
  }
  .sm-mt-70 {
    margin-top: 70px !important;
  }
  .sm-mt-80 {
    margin-top: 80px !important;
  }
  .sm-mt-90 {
    margin-top: 90px !important;
  }
  .sm-mt-100 {
    margin-top: 100px !important;
  }
  .sm-mt-110 {
    margin-top: 110px !important;
  }
  .sm-mt-120 {
    margin-top: 120px !important;
  }
  .sm-mt-130 {
    margin-top: 130px !important;
  }
  .sm-mt-140 {
    margin-top: 140px !important;
  }
  .sm-mt-150 {
    margin-top: 150px !important;
  }
  .sm-mt-200 {
    margin-top: 200px !important;
  }
  .sm-mb-10 {
    margin-bottom: 10px !important;
  }
  .sm-mb-20 {
    margin-bottom: 20px !important;
  }
  .sm-mb-30 {
    margin-bottom: 30px !important;
  }
  .sm-mb-40 {
    margin-bottom: 40px !important;
  }
  .sm-mb-50 {
    margin-bottom: 50px !important;
  }
  .sm-mb-60 {
    margin-bottom: 60px !important;
  }
  .sm-mb-70 {
    margin-bottom: 70px !important;
  }
  .sm-mb-80 {
    margin-bottom: 80px !important;
  }
  .sm-mb-90 {
    margin-bottom: 90px !important;
  }
  .sm-mb-100 {
    margin-bottom: 100px !important;
  }
  .sm-mb-110 {
    margin-bottom: 110px !important;
  }
  .sm-mb-120 {
    margin-bottom: 120px !important;
  }
  .sm-mb-130 {
    margin-bottom: 130px !important;
  }
  .sm-mb-140 {
    margin-bottom: 140px !important;
  }
  .sm-mb-150 {
    margin-bottom: 150px !important;
  }
  .sm-mb-200 {
    margin-bottom: 200px !important;
  }
}
/*(max-width: 767px)*/
@media (max-width: 575px) {
  .xs-p0 {
    padding: 0 !important;
  }
  .xs-m0 {
    margin: 0 !important;
  }
  .xs-pt-10 {
    padding-top: 10px !important;
  }
  .xs-pt-20 {
    padding-top: 20px !important;
  }
  .xs-pt-30 {
    padding-top: 30px !important;
  }
  .xs-pt-40 {
    padding-top: 40px !important;
  }
  .xs-pt-50 {
    padding-top: 50px !important;
  }
  .xs-pt-60 {
    padding-top: 60px !important;
  }
  .xs-pt-70 {
    padding-top: 70px !important;
  }
  .xs-pt-80 {
    padding-top: 80px !important;
  }
  .xs-pt-90 {
    padding-top: 90px !important;
  }
  .xs-pt-100 {
    padding-top: 100px !important;
  }
  .xs-pt-110 {
    padding-top: 110px !important;
  }
  .xs-pt-120 {
    padding-top: 120px !important;
  }
  .xs-pt-130 {
    padding-top: 130px !important;
  }
  .xs-pt-140 {
    padding-top: 140px !important;
  }
  .xs-pt-150 {
    padding-top: 150px !important;
  }
  .xs-pt-200 {
    padding-top: 200px !important;
  }
  .xs-pb-10 {
    padding-bottom: 10px !important;
  }
  .xs-pb-20 {
    padding-bottom: 20px !important;
  }
  .xs-pb-30 {
    padding-bottom: 30px !important;
  }
  .xs-pb-40 {
    padding-bottom: 40px !important;
  }
  .xs-pb-50 {
    padding-bottom: 50px !important;
  }
  .xs-pb-60 {
    padding-bottom: 60px !important;
  }
  .xs-pb-70 {
    padding-bottom: 70px !important;
  }
  .xs-pb-80 {
    padding-bottom: 80px !important;
  }
  .xs-pb-90 {
    padding-bottom: 90px !important;
  }
  .xs-pb-100 {
    padding-bottom: 100px !important;
  }
  .xs-pb-110 {
    padding-bottom: 110px !important;
  }
  .xs-pb-120 {
    padding-bottom: 120px !important;
  }
  .xs-pb-130 {
    padding-bottom: 130px !important;
  }
  .xs-pb-140 {
    padding-bottom: 140px !important;
  }
  .xs-pb-150 {
    padding-bottom: 150px !important;
  }
  .xs-pb-200 {
    padding-bottom: 200px !important;
  }
  .xs-mt-10 {
    margin-top: 10px !important;
  }
  .xs-mt-20 {
    margin-top: 20px !important;
  }
  .xs-mt-30 {
    margin-top: 30px !important;
  }
  .xs-mt-40 {
    margin-top: 40px !important;
  }
  .xs-mt-50 {
    margin-top: 50px !important;
  }
  .xs-mt-60 {
    margin-top: 60px !important;
  }
  .xs-mt-70 {
    margin-top: 70px !important;
  }
  .xs-mt-80 {
    margin-top: 80px !important;
  }
  .xs-mt-90 {
    margin-top: 90px !important;
  }
  .xs-mt-100 {
    margin-top: 100px !important;
  }
  .xs-mt-110 {
    margin-top: 110px !important;
  }
  .xs-mt-120 {
    margin-top: 120px !important;
  }
  .xs-mt-130 {
    margin-top: 130px !important;
  }
  .xs-mt-140 {
    margin-top: 140px !important;
  }
  .xs-mt-150 {
    margin-top: 150px !important;
  }
  .xs-mt-200 {
    margin-top: 200px !important;
  }
  .xs-mb-10 {
    margin-bottom: 10px !important;
  }
  .xs-mb-20 {
    margin-bottom: 20px !important;
  }
  .xs-mb-30 {
    margin-bottom: 30px !important;
  }
  .xs-mb-40 {
    margin-bottom: 40px !important;
  }
  .xs-mb-50 {
    margin-bottom: 50px !important;
  }
  .xs-mb-60 {
    margin-bottom: 60px !important;
  }
  .xs-mb-70 {
    margin-bottom: 70px !important;
  }
  .xs-mb-80 {
    margin-bottom: 80px !important;
  }
  .xs-mb-90 {
    margin-bottom: 90px !important;
  }
  .xs-mb-100 {
    margin-bottom: 100px !important;
  }
  .xs-mb-110 {
    margin-bottom: 110px !important;
  }
  .xs-mb-120 {
    margin-bottom: 120px !important;
  }
  .xs-mb-130 {
    margin-bottom: 130px !important;
  }
  .xs-mb-140 {
    margin-bottom: 140px !important;
  }
  .xs-mb-150 {
    margin-bottom: 150px !important;
  }
  .xs-mb-200 {
    margin-bottom: 200px !important;
  }
}
/*(max-width: 575px)*/
.theme-main-menu {
  background: #fff;
  position: relative;
  z-index: 999;
  padding: 18px 40px;
  transition: all 0.4s ease-in-out 0s;
}
.theme-main-menu.menu-overlay {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  background: transparent;
}
.theme-main-menu.fixed {
  position: fixed;
  left: 0;
  right: 0;
  padding-top: 5px;
  padding-bottom: 5px;
  background: #fff;
  box-shadow: 0 13px 35px -12px rgba(35, 35, 35, 0.1);
}
.theme-main-menu .navbar-toggler {
  width: 48px;
  height: 44px;
  padding: 0;
  box-shadow: none;
  position: relative;
  z-index: 99;
  border: none;
  background: #CFFF45;
}
.theme-main-menu.menu-style-three .navbar-toggler {
  background: #000;
}
.theme-main-menu.menu-style-four .navbar-toggler {
  background: #E6FD5A;
}
.theme-main-menu.menu-style-five .navbar-toggler {
  background: #DFFF5E;
}
.theme-main-menu .navbar-toggler:focus {
  box-shadow: none;
}
.theme-main-menu .navbar-toggler::before,
.theme-main-menu .navbar-toggler::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 11px;
  width: 26px;
  height: 2px;
  pointer-events: none;
  transition: transform 0.25s;
  transform-origin: 50% 50%;
  background: #29594B;
}
.theme-main-menu .navbar-toggler span {
  position: absolute;
  left: 11px;
  overflow: hidden;
  width: 26px;
  height: 2px;
  margin-top: -1px;
  text-indent: 200%;
  transition: opacity 0.25s;
  background: #29594B;
}
.theme-main-menu.menu-style-three .navbar-toggler:before, .theme-main-menu.menu-style-three .navbar-toggler:after, .theme-main-menu.menu-style-three .navbar-toggler span {
  background: #fff;
}
.theme-main-menu .navbar-toggler::before {
  transform: translate3d(0, -9px, 0) scale3d(1, 1, 1);
}
.theme-main-menu .navbar-toggler::after {
  transform: translate3d(0, 8px, 0) scale3d(1, 1, 1);
}
.theme-main-menu .navbar-toggler[aria-expanded=true] span {
  opacity: 0;
}
.theme-main-menu .navbar-toggler[aria-expanded=true]::before {
  transform: rotate3d(0, 0, 1, 45deg);
}
.theme-main-menu .navbar-toggler[aria-expanded=true]::after {
  transform: rotate3d(0, 0, 1, -45deg);
}
.theme-main-menu .nav-item .nav-link {
  font-family: "Satoshi";
  font-weight: 500;
  font-size: 20px;
  line-height: initial;
  color: #000;
  padding: 20px 0;
  margin: 0 30px;
  position: relative;
  transition: all 0.2s ease-in-out 0s;
}
.theme-main-menu .nav-item:hover .nav-link {
  color: #0C3A30;
}
.theme-main-menu.white-vr .nav-item .nav-link {
  color: #fff;
}
.theme-main-menu.white-vr .nav-item:hover .nav-link {
  color: #CFFF45;
}
.theme-main-menu.white-vr.fixed {
  background: #0C3A30;
  border: none;
}
.theme-main-menu.menu-style-one {
  border-bottom: 1px solid rgba(255, 255, 255, 0.4);
}
.theme-main-menu.menu-style-two {
  padding: 0;
  background: #EDF1EE;
}
.theme-main-menu.menu-style-two .info-row {
  padding-top: 12px;
  padding-bottom: 12px;
  overflow: hidden;
}
.theme-main-menu.menu-style-two .gap-fix {
  padding-left: 40px;
  padding-right: 40px;
}
.theme-main-menu.menu-style-two .greetings {
  font-size: 18px;
  color: #19352D;
}
.theme-main-menu.menu-style-two .contact-info {
  margin: 0 -15px;
}
.theme-main-menu.menu-style-two .contact-info li {
  padding: 0 15px;
}
.theme-main-menu.menu-style-two .contact-info li a {
  font-size: 18px;
  color: #19352D;
}
.theme-main-menu.menu-style-two .contact-info li a:hover {
  text-decoration: underline;
}
.theme-main-menu.menu-style-two .inner-content {
  padding-top: 8px;
  padding-bottom: 8px;
  border-top: 1px solid #E0E0E0;
  border-bottom: 1px solid #E0E0E0;
}
.theme-main-menu.menu-style-two.fixed .info-row {
  display: none;
}
.theme-main-menu.menu-style-five {
  background: #000;
  padding: 0;
}
.theme-main-menu.menu-style-five .inner-content {
  background: #fff;
  border-radius: 30px;
  padding: 18px 40px;
}
.theme-main-menu.menu-style-six .nav-item .nav-link {
  color: #044F3B;
}
.theme-main-menu.menu-style-six .nav-item:hover .nav-link {
  color: #0C3A30;
}

.navbar .dropdown-menu .dropdown-item {
  text-transform: capitalize;
  line-height: 35px;
  color: #19352D;
  font-size: 18px;
  background: transparent;
  position: relative;
  transition: all 0.2s ease-in-out 0s;
}
.navbar .dropdown-menu .dropdown-item span {
  position: relative;
}
.navbar .dropdown-menu .dropdown-item span:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 100%;
  height: 1px;
  background: #0C3A30;
  transform: scale(0, 1);
  transform-origin: 100% 100%;
  transition: all 0.2s ease-in-out 0s;
}
.navbar .dropdown-menu .dropdown-item:hover span:before, .navbar .dropdown-menu .dropdown-item.active span:before {
  transform: scale(1);
}

.theme-main-menu .navbar {
  position: static;
}

.navbar [class*=mega-dropdown] .dropdown-menu {
  padding: 0 0 15px;
  margin: 0;
  right: 0;
}

.navbar .mega-dropdown-sm .dropdown-menu {
  min-width: 600px;
  left: 0;
  padding: 10px;
}

.navbar [class*=mega-dropdown] .menu-column {
  padding: 5px 0;
}
.navbar [class*=mega-dropdown] .menu-column .mega-menu-title {
  font-family: "Satoshi";
  font-size: 14px;
  font-weight: 500;
  color: #E6FD5A;
  display: inline-block;
  position: relative;
  margin: 0 0 5px;
  padding-left: 15px;
}

.theme-main-menu .login-btn-one a {
  color: #000;
}
.theme-main-menu .login-btn-one a:hover {
  text-decoration: underline;
}
.theme-main-menu .login-btn-two a {
  color: #044F3B;
}
.theme-main-menu .login-btn-two a:hover {
  text-decoration: underline;
}
.theme-main-menu .signup-btn-one span {
  display: inline-block;
  font-weight: 500;
  color: #19352D;
  padding: 0 30px;
  line-height: 45px;
  border: 2px solid #29594B;
  border-radius: 30px;
  background: #fff;
  transition: all 0.2s ease-in-out 0s;
}
.theme-main-menu .signup-btn-one .icon {
  width: 49px;
  height: 49px;
  background: #29594B;
  color: #fff;
  font-size: 22px;
  margin-right: -12px;
  transition: all 0.2s ease-in-out 0s;
}
.theme-main-menu .signup-btn-one:hover span {
  background: #29594B;
  color: #fff;
}
.theme-main-menu .signup-btn-one:hover .icon {
  background: #CFFF45;
  color: #29594B;
}
.theme-main-menu .signup-btn-two {
  color: #044F3B;
  line-height: 50px;
  border: 1px solid #044F3B;
  border-radius: 40px;
  background: #CFFF45;
  padding: 0 32px;
  text-align: center;
}
.theme-main-menu .signup-btn-two:hover {
  background: #29594B;
  color: #fff;
}
.theme-main-menu .quote-one {
  line-height: 48px;
  color: #fff;
  font-size: 18px;
  padding: 0 26px;
  border: 1px solid #fff;
  border-radius: 40px;
}
.theme-main-menu .quote-one:hover {
  background: #CFFF45;
  color: #19352D;
}

.category-menu {
  border-top: 1px solid #E9E9E9;
  border-bottom: 1px solid #E9E9E9;
  padding: 0 40px;
}
.category-menu li {
  position: relative;
  padding: 0 15px;
}
.category-menu li a {
  font-size: 15px;
  font-weight: 500;
  color: #536159;
  padding: 13px 0;
  transition: all 0.2s ease-in-out 0s;
}
.category-menu li a:hover {
  color: #0C3A30;
}
.category-menu .dropdown-menu {
  padding: 0;
}
.category-menu .dropdown-menu li {
  padding: 0;
}
.category-menu .dropdown-menu li a {
  padding: 8px 15px;
  font-size: 14px;
}

/*----- For Desktop -------*/
@media screen and (min-width: 992px) {
  .navbar .dropdown-menu {
    font-size: 1em;
    z-index: 5;
    background-color: #fff;
    border-radius: 10px;
    display: block;
    left: auto;
    right: 0;
    padding: 10px 5px;
    border: none;
    top: 100%;
    visibility: hidden;
    transform: translateY(5px);
    opacity: 0;
    min-width: 200px;
    box-shadow: 0px 50px 100px rgba(0, 0, 0, 0.12);
    margin: 0;
    transform-origin: 0 0;
    transition: all 0.3s ease-out;
  }
  .navbar .dropdown-menu:before {
    content: "";
    position: absolute;
    left: 30px;
    top: -17px;
  }
  .navbar .dropdown-menu .dropdown-menu {
    left: calc(100% + 5px);
    top: 0;
    right: auto;
    min-width: 240px;
    box-shadow: 0 20px 30px -10px rgba(0, 0, 0, 0.15);
    transform: translateY(0);
  }
  .navbar .dropdown:hover > .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  .navbar .dropdown-toggle::after {
    display: none;
  }
  .navbar .show.dropdown-toggle::after {
    transform: rotate(180deg);
  }
}
/*----- For Mobile ----*/
@media screen and (max-width: 991px) {
  .navbar {
    padding: 0;
  }
  .navbar-collapse .logo {
    margin-bottom: 10vh;
    padding-left: 12px;
  }
  .white-vr .navbar-collapse {
    background-color: #0C3A30;
  }
  .navbar-collapse {
    position: fixed;
    top: 0;
    background-color: #EDF1EE;
    right: 0;
    height: 100vh;
    max-height: 100vh;
    overflow-y: auto;
    clear: both;
    width: 320px;
    max-width: calc(100vw - 60px);
    z-index: 9999;
    transform: translateX(100%);
    display: block !important;
    padding: 16px 0 20px;
    transition: all 0.3s ease-in-out;
  }
  .navbar-collapse.show {
    transform: translateX(0);
    box-shadow: 15px 0 25px rgba(35, 35, 35, 0.09);
  }
  .theme-main-menu .navbar .mega-dropdown {
    position: relative;
  }
  .navbar .navbar-nav .nav-link {
    margin: 0;
    padding: 15px 12px;
    border-top: 1px dashed rgba(0, 0, 0, 0.1);
  }
  .white-vr .navbar .navbar-nav .nav-link {
    border-top: 1px dashed rgba(255, 255, 255, 0.15);
  }
  .navbar .dropdown-menu .dropdown-item {
    padding: 0 10px;
    line-height: 46px;
  }
  .navbar .dropdown-menu {
    border: none;
    padding: 0;
    border-radius: 0;
    margin: 0;
    background: #fff;
  }
  .navbar [class*=mega-dropdown] .dropdown-menu {
    padding: 0;
    min-width: 100%;
  }
  .navbar [class*=mega-dropdown] .menu-column {
    padding: 0;
  }
  .navbar .dropdown-toggle::after {
    position: absolute;
    left: 15px;
    top: calc(50% - 2px);
  }
  .dashboard-menu .nav-link::before {
    right: auto;
    left: 0;
    top: -2px;
  }
}
/*(max-width: 991px)*/
.hero-banner-one {
  background: #29594B;
  z-index: 9;
}
.hero-banner-one::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.53) 0%, rgba(0, 0, 0, 0) 100%);
}
.hero-banner-one .hero-slider-one {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: -1;
}
.hero-banner-one .hero-slider-one .hero-img {
  position: absolute;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.hero-banner-one .hero-slider-one .slick-list, .hero-banner-one .hero-slider-one .slick-track, .hero-banner-one .hero-slider-one .item {
  height: 100%;
}
.hero-banner-one .hero-heading {
  font-weight: 700;
  color: #000;
  background: url(../images/shape/shape_01.svg) no-repeat left top;
  background-size: cover;
  padding: 11px 31px 25px 25px;
}
.hero-banner-one .more-btn {
  position: absolute;
  font-size: 40px;
  color: #fff;
  width: 105px;
  height: 105px;
  border: 2px solid #fff;
  top: 20%;
  right: 4%;
  z-index: 1;
}
.hero-banner-one .more-btn:hover {
  background: #CFFF45;
  border-color: #CFFF45;
  color: #19352D;
}

.hero-banner-two {
  z-index: 1;
}
.hero-banner-two .hero-heading {
  font-size: 100px;
  line-height: 1em;
}
.hero-banner-two .hero-heading span {
  z-index: 1;
}
.hero-banner-two .hero-heading span img {
  position: absolute;
  width: 100%;
  bottom: -18%;
  left: 0;
  z-index: -1;
}
.hero-banner-two form {
  max-width: 588px;
  height: 70px;
  box-shadow: 0px 10px 20px rgba(8, 32, 26, 0.04);
  border-radius: 50px;
}
.hero-banner-two form input {
  font-size: 18px;
  width: 100%;
  height: 100%;
  border-radius: 50px;
  background: #fff;
  border: none;
  padding: 0 35px 0 150px;
}
.hero-banner-two form button {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  border-radius: 50px;
}
.hero-banner-two .shape_01 {
  top: 22%;
  left: 7%;
  width: 3.1%;
  min-width: 30px;
  animation: rotated 50s infinite linear;
}
.hero-banner-two .shape_02 {
  top: 40%;
  right: 7%;
  width: 1.8%;
  min-width: 22px;
  animation: rotated 48s infinite linear;
}
.hero-banner-two .shape_03 {
  top: 36%;
  left: 0;
  width: 15.57%;
}
.hero-banner-two .shape_04 {
  top: 49%;
  right: 0;
  width: 15%;
}

.hero-banner-three {
  z-index: 1;
}
.hero-banner-three .hero-heading {
  font-size: 100px;
  letter-spacing: 1px;
  line-height: 1.3em;
  color: #000;
}
.hero-banner-three .right-widget .main-count {
  font-size: 58px;
  margin: -17px 0 -4px;
}
.hero-banner-three .img-wrapper {
  position: absolute;
  z-index: -1;
  bottom: 0;
  width: 33.53%;
  left: 50%;
  transform: translateX(-50%);
}
.hero-banner-three .img-wrapper .round-bg {
  position: absolute;
  z-index: -2;
  top: 0;
  left: 50%;
  transform: translate(-50%, -7%);
}

.hero-banner-four {
  background: url(../images/assets/bg_01.svg) no-repeat center bottom;
  background-size: cover;
  z-index: 1;
}
.hero-banner-four .hero-heading {
  font-family: "ClashDisplay";
  font-size: 130px;
  font-weight: 600;
  line-height: 0.884em;
}
.hero-banner-four .hero-heading span {
  color: #E6FD5A;
  display: block;
}
.hero-banner-four .media-wrapper {
  position: absolute;
  left: 3%;
  bottom: -6%;
  width: 42.56%;
  z-index: -1;
}
.hero-banner-four .shape_01 {
  bottom: -5%;
  left: 41%;
  max-width: 9%;
}
.hero-banner-four .shape_02 {
  bottom: 0;
  right: 14%;
  width: 30%;
}

.hero-banner-five {
  background: #000;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
}
.hero-banner-five .bg-wrapper {
  background: #fff;
  border-radius: 30px;
  z-index: 1;
}
.hero-banner-five .hero-heading {
  font-size: 85px;
  font-weight: normal;
  letter-spacing: 1px;
  line-height: 1.176em;
  color: #000;
}
.hero-banner-five .rating h3 {
  font-size: 42px;
  margin-bottom: 0px;
}
.hero-banner-five .rating p {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.4);
}
.hero-banner-five .media-wrapper {
  position: absolute;
  background: url(../images/assets/bg_05.svg) no-repeat left top;
  background-size: cover;
  border-radius: 30px 0 0 30px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  width: 39.43%;
}
.hero-banner-five .media-wrapper .shape_01 {
  left: 0;
  bottom: 14%;
  width: 41.1%;
  z-index: 1;
}
.hero-banner-five .media-wrapper .shape_02 {
  top: 34%;
  right: 0;
  transform: translateX(50%);
  width: 19.34%;
}

.hero-banner-six .hero-heading {
  font-size: 100px;
  line-height: 1.05em;
  color: #044F3B;
}
.hero-banner-six .media-wrapper {
  background: url(../images/media/img_48.jpg) no-repeat center;
  background-size: cover;
  border-radius: 20px;
  max-width: 536px;
  width: 100%;
  height: 100%;
}
.hero-banner-six .media-wrapper .screen_01 {
  left: 4%;
  top: 4%;
  width: 41.1%;
  border-radius: 10px;
  box-shadow: 10px 30px 50px rgba(0, 0, 0, 0.06);
  z-index: 1;
}
.hero-banner-six .media-wrapper .screen_02 {
  bottom: 9%;
  right: -28%;
  border-radius: 10px;
  width: 48.51%;
  box-shadow: -10px 30px 50px rgba(0, 0, 0, 0.07);
  z-index: 1;
  animation: jumpTwo 10s infinite linear;
}
.hero-banner-six .media-wrapper .bg-shape {
  max-width: 130%;
  left: 47%;
  bottom: -12%;
  transform: translateX(-50%);
}
.hero-banner-six .shape_01 {
  bottom: 9%;
  right: 40%;
  width: 6.53%;
}

.hero-banner-seven {
  padding: 500px 0 70px;
  background: #29594B;
  z-index: 9;
}
.hero-banner-seven::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 50%;
  left: 0px;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.87) 100%);
  z-index: -1;
}
.hero-banner-seven::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 20%;
  left: 0px;
  top: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.99) 100%);
  mix-blend-mode: overlay;
  transform: rotate(-180deg);
  z-index: -1;
}
.hero-banner-seven .hero-slider-one {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: -2;
}
.hero-banner-seven .hero-slider-one .hero-img {
  position: absolute;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.hero-banner-seven .hero-slider-one .slick-list, .hero-banner-seven .hero-slider-one .slick-track, .hero-banner-seven .hero-slider-one .item {
  height: 100%;
}
.hero-banner-seven .hero-heading {
  font-weight: 700;
  font-size: 110px;
  letter-spacing: -1px;
  line-height: 1em;
}
.hero-banner-seven .lead-form {
  background: #fff;
  border-radius: 30px;
  padding: 35px 50px 50px;
}
.hero-banner-seven .lead-form h3 {
  font-size: 32px;
}
.hero-banner-seven .lead-form label {
  font-size: 17px;
  color: rgba(0, 0, 0, 0.3);
}
.hero-banner-seven .lead-form input {
  height: 65px;
  padding: 0 20px;
  border: 1px solid #000;
  border-radius: 10px;
}
.hero-banner-seven .lead-form button {
  height: 60px;
  border-radius: 10px;
  font-size: 17px;
  background: #D0FF45;
}
.hero-banner-seven .lead-form button:hover {
  background: #29594B;
  color: #fff;
}

.hero-banner-eight {
  background: #144D41;
}
.hero-banner-eight:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: url(../images/shape/shape_53.svg) no-repeat center;
  background-size: cover;
}
.hero-banner-eight .hero-heading {
  font-size: 110px;
  font-weight: 700;
  line-height: 1em;
}
.hero-banner-eight .media-wrapper {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -1;
  max-width: 43%;
}
.hero-banner-eight .media-wrapper .shape_01 {
  left: 5%;
  top: 27%;
  z-index: 0;
  max-width: 25%;
  animation: jumpTwo 5s infinite linear;
}
.hero-banner-eight .media-wrapper .shape_02 {
  right: 3%;
  bottom: 25%;
  z-index: 0;
  max-width: 35%;
  animation: jumpThree 5s infinite linear;
}

.fancy-banner-one {
  z-index: 1;
  position: relative;
  padding-left: 12px;
  padding-right: 12px;
}
.fancy-banner-one:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 50%;
  background: #CFFF45;
  z-index: -1;
}
.fancy-banner-one h2 {
  font-size: 68px;
  font-weight: normal;
  margin: 0;
}
.fancy-banner-one h2 span {
  font-weight: 700;
  font-style: italic;
  text-decoration: underline;
}
.fancy-banner-one h3 {
  font-size: 48px;
  margin-bottom: -5px;
}
.fancy-banner-one p {
  color: rgba(25, 53, 45, 0.6);
}

.fancy-banner-three {
  background: url(../images/media/img_17.jpg) no-repeat center;
  background-size: cover;
  padding: 80px 0;
  z-index: 1;
}
.fancy-banner-three:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: #174034;
  mix-blend-mode: darken;
}
.fancy-banner-three .title-one h2 {
  font-size: 58px;
}
.fancy-banner-three .quote-btn {
  width: 200px;
  height: 200px;
  padding: 16px;
  background: #FFE86B;
}
.fancy-banner-three .quote-btn:hover {
  transform: rotate(15deg);
}
.fancy-banner-three .quote-btn.color-two {
  background: #CFFF45;
}

.fancy-banner-four {
  background: #DCEFF0;
  z-index: 1;
}
.fancy-banner-four:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  z-index: -1;
  background: url(../images/shape/shape_10.svg) no-repeat left bottom;
  background-size: cover;
  transform: scaleX(-1);
}
.fancy-banner-four ul li {
  font-size: 28px;
  color: #000;
  position: relative;
  padding-right: 42px;
  margin-bottom: 13px;
}
.fancy-banner-four ul li:after {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  font-size: 0.95em;
  top: 3px;
  right: 0;
  color: #000;
}
.fancy-banner-four .platform-button-group a {
  width: 190px;
  height: 58px;
  padding: 0 25px 0 5px;
  margin: 10px 0 0 20px;
  background: #1B1B1B;
  color: #fff;
  transition: all 0.3s ease-in-out;
}
.fancy-banner-four .platform-button-group a:hover {
  transform: translateY(-5px);
  box-shadow: -5px 10px 30px rgba(0, 0, 0, 0.05);
}
.fancy-banner-four .platform-button-group a .icon {
  margin-left: 14px;
}
.fancy-banner-four .platform-button-group a span {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  display: block;
  line-height: initial;
  margin-bottom: -3px;
}
.fancy-banner-four .platform-button-group a strong {
  font-weight: 500;
  display: block;
}
.fancy-banner-four .platform-button-group a.ios-button {
  background: #fff;
  border-color: #DADADA;
}
.fancy-banner-four .platform-button-group a.ios-button span {
  color: #999999;
}
.fancy-banner-four .platform-button-group a.ios-button strong {
  color: #000;
}

.fancy-banner-five {
  background: url(../images/media/img_31.jpg) no-repeat center;
  background-size: cover;
  z-index: 1;
}
.fancy-banner-five.no-bg {
  background: none;
}
.fancy-banner-five.no-bg:before {
  display: none;
}
.fancy-banner-five.no-bg .bg-wrapper {
  background: url(../images/media/img_33.jpg) no-repeat center;
  background-size: cover;
}
.fancy-banner-five.no-bg .bg-wrapper .video-icon {
  width: 185px;
  height: 185px;
  background: #DFFF5E;
}
.fancy-banner-five:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: #113D2F;
  mix-blend-mode: hard-light;
}
.fancy-banner-five h2 {
  font-size: 100px;
  line-height: 1.1em;
}
.fancy-banner-five .video-icon {
  width: 200px;
  height: 200px;
  padding: 22px;
  background: #FFE86B;
}
.fancy-banner-five .video-icon:hover {
  transform: rotate(15deg);
}

.fancy-banner-six {
  background: url(../images/media/img_34.jpg) no-repeat center;
  background-size: cover;
  z-index: 1;
}
.fancy-banner-six h2 {
  font-size: 85px;
  line-height: 1.117em;
}
.fancy-banner-six .video-icon {
  width: 200px;
  height: 200px;
  padding: 22px;
  background: #FFE86B;
}
.fancy-banner-six .video-icon:hover {
  transform: rotate(15deg);
}

.fancy-banner-seven .bg-wrapper {
  background: url(../images/media/img_50.jpg) no-repeat center;
  background-size: cover;
}
.fancy-banner-seven .bg-wrapper:before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: 70%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 14.17%, rgba(0, 0, 0, 0.8) 101.25%);
  transform: rotate(-180deg);
  z-index: -1;
}
.fancy-banner-seven .bg-wrapper:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 70%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.58) 47.84%, rgba(0, 0, 0, 0.87) 100%);
  z-index: -1;
}
.fancy-banner-seven .bg-wrapper li {
  font-weight: 500;
  font-size: 24px;
  line-height: 1.5em;
  color: #000;
  padding: 18px 68px 25px 80px;
  border-radius: 20px;
  background: #fff;
  margin: 12px 0;
  position: relative;
}
.fancy-banner-seven .bg-wrapper li:before {
  content: "";
  position: absolute;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #29594B;
  right: 27px;
  top: 27px;
  transition: all 0.2s ease-in-out 0s;
}
.fancy-banner-seven .bg-wrapper li:after {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  top: 21px;
  right: 31px;
  color: #fff;
  font-size: 16px;
  transition: all 0.2s ease-in-out 0s;
}
.fancy-banner-seven .bg-wrapper li:hover:before {
  background: #CFFF45;
}
.fancy-banner-seven .bg-wrapper li:hover:after {
  color: #000;
}
.fancy-banner-seven .bg-wrapper .shape_01 {
  bottom: 17%;
  left: 36%;
  max-width: 11%;
  z-index: 0;
}

.fancy-banner-eight .bg-wrapper {
  background: #F3F8F7;
  padding: 30px 75px 0;
}
.fancy-banner-eight .media-wrapper {
  padding: 0 38px;
  margin-bottom: -6px;
}
.fancy-banner-eight .media-wrapper .shape_01 {
  width: 100%;
  bottom: 0;
  left: 0;
}
.fancy-banner-eight .shape_02 {
  left: -123px;
  bottom: -5px;
}

.newsletter-banner .main-wrapper {
  padding: 50px 0 45px;
}
.newsletter-banner .main-wrapper.top-border {
  border-top: 1px solid #E2E2E2;
}
.newsletter-banner .main-wrapper.bottom-border {
  border-bottom: 1px solid #E2E2E2;
}
.newsletter-banner h2 {
  font-size: 50px;
}
.newsletter-banner form {
  max-width: 510px;
}
.newsletter-banner form input {
  width: calc(100% - 75px);
  font-size: 18px;
  padding: 0 30px;
  height: 60px;
  background: #F6F6F6;
  border: none;
  border-radius: 35px;
}
.newsletter-banner form button {
  width: 60px;
  height: 60px;
  text-align: center;
  font-size: 28px;
  color: #fff;
  background: #101010;
}
.newsletter-banner form button:hover, .newsletter-banner form button:focus {
  background: #29594B;
}
.newsletter-banner form button.color-two {
  background: #29594B;
}
.newsletter-banner form button.color-two:hover, .newsletter-banner form button.color-two:focus {
  background: #000;
}
.newsletter-banner form p {
  font-size: 18px;
}
.newsletter-banner form p a:hover {
  text-decoration: underline;
}
.newsletter-banner.white-vr .main-wrapper {
  padding-top: 100px;
}
.newsletter-banner.white-vr .bottom-border {
  border-bottom: 1px dashed #37665c;
}
.newsletter-banner.white-vr form button {
  background: #E6FD5A;
  color: #19352D;
}
.newsletter-banner.white-vr form p {
  color: rgba(255, 255, 255, 0.6);
}
.newsletter-banner.white-vr form p a {
  color: #E6FD5A;
}

.inner-banner-one {
  background-repeat: no-repeat;
  background-position: top center;
  background-size: cover;
  z-index: 1;
}
.inner-banner-one::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 35%;
  top: 0;
  left: 0;
  z-index: -1;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.53) 0%, rgba(0, 0, 0, 0) 100%);
}
.inner-banner-one::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  background: linear-gradient(-121.31deg, rgba(0, 0, 0, 0) 0.55%, rgba(0, 0, 0, 0.88) 98.35%);
}
.inner-banner-one .hero-heading {
  font-weight: 700;
  font-size: 85px;
  line-height: 1.023em;
  z-index: 1;
  padding: 10px 22px 23px 10px;
}
.inner-banner-one .hero-heading img {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: -1;
  max-height: 100%;
  transform: scaleX(-1);
}
.inner-banner-one .pager {
  border-bottom: 1px solid #fff;
  padding-bottom: 3px;
}
.inner-banner-one .pager li {
  color: rgba(255, 255, 255, 0.5);
  margin-left: 5px;
}
.inner-banner-one .pager li:last-child {
  margin: 0;
  color: #fff;
}
.inner-banner-one .pager li a {
  transition: all 0.2s ease-in-out 0s;
}
.inner-banner-one .pager li a:hover {
  color: #fff;
}
.inner-banner-one .tag {
  display: inline-block;
  line-height: 25px;
  border: 1px solid #fff;
  border-radius: 30px;
  padding: 0 10px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 1px;
  color: #fff;
  text-transform: uppercase;
}

.inner-banner-two {
  z-index: 1;
}
.inner-banner-two .pager {
  border-bottom: 1px solid #0A4020;
  padding-bottom: 2px;
}
.inner-banner-two .pager li {
  color: #000;
}
.inner-banner-two .pager li:nth-child(even) {
  padding: 0 5px;
}
.inner-banner-two .pager li a {
  color: rgba(0, 0, 0, 0.5);
  transition: all 0.2s ease-in-out 0s;
}
.inner-banner-two .pager li a:hover {
  color: #000;
}
.inner-banner-two .hero-heading {
  font-weight: 700;
  font-size: 85px;
  line-height: 1.023em;
  margin: 22px 0 40px;
}
.inner-banner-two .tag {
  display: inline-block;
  line-height: 25px;
  border: 1px solid #19352D;
  border-radius: 30px;
  padding: 0 10px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 1px;
  color: #19352D;
  text-transform: uppercase;
}
.inner-banner-two .shape_01 {
  left: 0%;
  bottom: 10%;
  width: 18.23%;
  animation: jumpTwo 6s infinite linear;
}
.inner-banner-two .shape_02 {
  right: 2%;
  bottom: 14%;
  width: 14.52%;
  animation: jumpThree 6s infinite linear;
}
.inner-banner-two .shape_03 {
  left: 24%;
  top: 33%;
  width: 2.2%;
  animation: rotated 50s infinite linear;
}
.inner-banner-two .shape_04 {
  right: 21%;
  bottom: 30%;
  width: 1.65%;
  animation: rotated 50s infinite linear;
}

.block-feature-one {
  z-index: 1;
}
.block-feature-one:before {
  content: "";
  position: absolute;
  right: 0;
  left: 0;
  top: 0;
  bottom: 17%;
  background: #EBF3EE;
  z-index: -1;
}
.block-feature-one .upper-wrapper {
  border-bottom: 1px dashed rgba(0, 0, 0, 0.5);
}
.block-feature-one .section-subheading {
  position: absolute;
  left: 0;
  top: 0;
  width: 37%;
}
.block-feature-one .shape_01 {
  width: 2.3%;
  top: 10%;
  left: 10%;
}
.block-feature-one .shape_02 {
  width: 1.5%;
  bottom: 6%;
  right: 9%;
}

.block-feature-two .wrapper {
  border-top: 1px dashed #CACACA;
  border-bottom: 1px dashed #CACACA;
}
.block-feature-two .border-line {
  border-left: 1px dashed #CACACA;
  border-right: 1px dashed #CACACA;
}
.block-feature-two .numb {
  display: inline-block;
  font-size: 90px;
  letter-spacing: -1px;
  color: #19352D;
  position: relative;
}
.block-feature-two .numb::before {
  content: "";
  position: absolute;
  width: 85px;
  height: 85px;
  border-radius: 50%;
  background: #CFFF45;
  right: 8px;
  top: 27px;
  z-index: -1;
}

.block-feature-three {
  z-index: 1;
}
.block-feature-three:before {
  content: "";
  position: absolute;
  right: 0;
  left: 0;
  top: 34%;
  bottom: 0;
  background: #EBF3EE;
  z-index: -1;
}
.block-feature-three.no-bg:before {
  display: none;
}
.block-feature-three .section-btn {
  position: absolute;
  left: 0;
  top: 68px;
}
.block-feature-three .block-title a {
  font-size: 32px;
  font-weight: 500;
  line-height: 1.25em;
  color: #19352D;
}
.block-feature-three .block-title a:hover {
  text-decoration: underline;
}
.block-feature-three .round-btn {
  font-size: 18px;
  font-weight: 900;
  color: #29594B;
  width: 38px;
  height: 38px;
  border: 1px solid #29594B;
}
.block-feature-three .round-btn:hover {
  background: #CFFF45;
  border-color: #CFFF45;
  color: #19352D;
}
.block-feature-three .block-one {
  background: url(../images/media/img_05.jpg) no-repeat center;
  background-size: cover;
  height: 470px;
}
.block-feature-three .block-one .tag {
  font-size: 14px;
  letter-spacing: 1px;
  color: #000;
  line-height: 25px;
  border: 1px solid #000;
  padding: 0 10px;
}
.block-feature-three .block-one .block-title {
  background: url(../images/shape/shape_02.svg) no-repeat right top;
  background-size: cover;
  border-radius: 25px;
  padding: 12px 18px 18px 0;
}
.block-feature-three .block-two {
  overflow: hidden;
}
.block-feature-three .block-two .img-wrapper {
  background: url(../images/media/img_06.jpg) no-repeat center;
  background-size: cover;
  width: 42%;
}
.block-feature-three .block-two .text-wrapper {
  width: 58%;
  padding: 30px 60px 35px 30px;
}
.block-feature-three .block-two .tag {
  font-size: 13px;
  letter-spacing: 1px;
  color: white;
  line-height: 27px;
  border-radius: 17px;
  background: #29594B;
  padding: 0 17px;
}
.block-feature-three .block-three {
  background: #D3FF76;
}
.block-feature-three .block-three .tag {
  font-size: 13px;
  letter-spacing: 1px;
  color: white;
  line-height: 27px;
  border-radius: 17px;
  background: #29594B;
  padding: 0 17px;
}
.block-feature-three .block-three .round-btn {
  background: #fff;
  border-color: #fff;
}
.block-feature-three .block-three .round-btn:hover {
  background: #29594B;
  border-color: #29594B;
  color: #fff;
}
.block-feature-three .block-four {
  background: url(../images/media/img_07.jpg) no-repeat center;
  background-size: cover;
}
.block-feature-three .block-four .block-title {
  background: url(../images/shape/shape_03.svg) no-repeat right top;
  background-size: cover;
  border-radius: 18px;
  padding: 12px 16px 12px 18px;
}
.block-feature-three .block-four .block-title a {
  font-size: 28px;
}
.block-feature-three .block-four .tag {
  font-size: 13px;
  letter-spacing: 1px;
  color: #29594B;
  line-height: 27px;
  border-radius: 17px;
  background: #fff;
  padding: 0 17px;
}
.block-feature-three .block-four .round-btn {
  background: transparent;
  border-color: #fff;
  color: #fff;
}
.block-feature-three .block-four .round-btn:hover {
  background: #CFFF45;
  border-color: #CFFF45;
  color: #000;
}
.block-feature-three .block-five {
  background: url(../images/blog/blog_img_05.jpg) no-repeat center;
  background-size: cover;
  height: 358px;
}
.block-feature-three .block-five .tag {
  font-size: 14px;
  letter-spacing: 1px;
  color: #000;
  line-height: 25px;
  border: 1px solid #000;
  padding: 0 10px;
}
.block-feature-three .block-five .block-title {
  background: url(../images/shape/shape_29.svg) no-repeat right top;
  background-size: cover;
  border-radius: 15px;
  padding: 12px 10px 18px 18px;
}

.block-feature-four {
  z-index: 1;
}
.block-feature-four .shape_01 {
  top: 0;
  left: 7%;
  width: 2.52%;
  min-width: 28px;
  animation: rotated 50s infinite linear;
}
.block-feature-four .shape_02 {
  bottom: 2%;
  right: 7%;
  width: 1.8%;
  min-width: 22px;
  animation: rotated 48s infinite linear;
}

.block-feature-five {
  z-index: 1;
}
.block-feature-five:before {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 45%;
  background: url(../images/shape/shape_09.svg) no-repeat right bottom;
  background-size: cover;
  z-index: -1;
}
.block-feature-five .section-btn {
  position: absolute;
  left: 0;
  top: 75px;
}
.block-feature-five .shape_01 {
  right: 6%;
  bottom: 5%;
  width: 1%;
  animation: rotated 50s infinite linear;
}

.block-feature-six {
  background: #DCEFF0;
  z-index: 1;
}
.block-feature-six:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  z-index: -1;
  background: url(../images/shape/shape_10.svg) no-repeat center;
  background-size: cover;
  transform: scaleX(-1);
}
.block-feature-six.bg-two {
  background: #EBF3EE;
}
.block-feature-six.bg-two:before {
  display: none;
}
.block-feature-six .shape_01 {
  bottom: 12%;
  right: 8%;
  width: 10%;
  transform: scaleX(-1);
}
.block-feature-six .shape_02 {
  bottom: 39%;
  right: 30%;
  width: 2.5%;
}

.block-feature-seven {
  z-index: 1;
}
.block-feature-seven .shape_01 {
  top: 2%;
  left: 0;
  animation: rotated 50s infinite linear;
}

.block-feature-eight {
  background: url(../images/assets/bg_02.svg) no-repeat center;
  background-size: cover;
  z-index: 1;
}
.block-feature-eight .section-btn {
  position: absolute;
  left: 0;
  top: 72px;
}
.block-feature-eight .shape_01 {
  width: 40px;
  bottom: -20px;
  right: 25%;
  animation: rotated 50s infinite linear;
}

.block-feature-nine {
  z-index: 1;
  background: #000;
  border-radius: 30px 30px 100px 100px;
}
.block-feature-nine:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 39%;
  left: 0;
  top: 0;
  background: #EDF8EB;
  border-radius: 30px 30px 0 0;
  z-index: -1;
}
.block-feature-nine:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 50px;
  left: 0;
  top: 0;
  background: #000;
  z-index: -1;
}
.block-feature-nine .heading {
  font-size: 85px;
  line-height: 1.411em;
}
.block-feature-nine .heading span {
  color: #DFFF5E;
  text-decoration-line: underline;
  text-decoration-thickness: 4px;
}
.block-feature-nine .heading a {
  width: 110px;
  height: 110px;
  display: inline-flex;
  align-items: center;
  background: #DFFF5E;
  border-radius: 50%;
  position: relative;
  margin-right: 100px;
  transition: all 0.2s ease-in-out 0s;
}
.block-feature-nine .heading a:before {
  content: "";
  position: absolute;
  width: 85px;
  height: 3px;
  right: -85px;
  top: calc(50% - 3px);
  background: #fff;
}
.block-feature-nine .heading a:hover {
  background: #E6FD5A;
}
.block-feature-nine .shape_01 {
  left: 0;
  top: 5%;
  width: 4%;
}
.block-feature-nine .shape_02 {
  right: 7%;
  bottom: 0;
  width: 8.4%;
}
.block-feature-nine .shape_03 {
  left: 5%;
  bottom: 5%;
}

.block-feature-ten .shape_01 {
  top: 1%;
  left: 5%;
  width: 2%;
}
.block-feature-ten .line-wrapper:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  border-bottom: 1px dashed #e5e5e5;
  left: 0;
  top: 50%;
}

.block-feature-eleven {
  z-index: 1;
}
.block-feature-eleven .slider-wrapper {
  width: 78vw;
}
.block-feature-eleven .slider-wrapper .slick-dots {
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: 0;
  right: -140px;
}
.block-feature-eleven .slider-wrapper .slick-dots li button {
  width: 10px;
  height: 10px;
  background: #fff;
  border: 1px solid #1F5E59;
}
.block-feature-eleven .slider-wrapper .slick-dots .slick-active button {
  background: #1F5E59;
}
.block-feature-eleven .shape_01 {
  right: 0;
  bottom: 0;
  max-width: 13.4%;
  transform: scaleX(-1);
}

.block-feature-twelve {
  background: #EDF1EE;
  z-index: 1;
}
.block-feature-twelve .shape_01 {
  right: 0;
  top: 12%;
  max-width: 13.4%;
}

.block-feature-thirteen .upper-wrapper {
  border-bottom: 1px solid #E6E6E6;
}
.block-feature-thirteen .upper-wrapper .shape_01 {
  left: 6%;
  bottom: 16%;
}
.block-feature-thirteen .line-loop:before {
  content: "";
  position: absolute;
  height: 1px;
  width: 130%;
  left: 0;
  top: 57%;
  background: #E6E6E6;
  z-index: -1;
}
.block-feature-thirteen .graph-panel {
  border-left: 1px solid #E6E6E6;
}
.block-feature-thirteen .graph-panel .main-count {
  font-size: 120px;
}
.block-feature-thirteen .graph-panel .chart-box {
  padding: 0 15px;
}
.block-feature-thirteen .graph-panel .chart-box .chart-inner {
  width: 120px;
  background: #196164;
}

.block-feature-fourteen .section-subheading {
  position: absolute;
  left: 0;
  top: 0;
  width: 37%;
}
.block-feature-fourteen .shape_01 {
  max-width: 4%;
  top: 13%;
  right: 48%;
  animation: rotated 48s infinite linear;
}

.block-feature-fifteen .shape_01 {
  max-width: 4%;
  top: 3%;
  left: 2%;
  animation: rotated 48s infinite linear;
}
.block-feature-fifteen .line-btn:before, .block-feature-fifteen .line-btn:after {
  content: "";
  position: absolute;
  width: calc(50% - 150px);
  height: 1px;
  background: #E3E3E3;
  top: 32px;
  left: 0;
}
.block-feature-fifteen .line-btn:after {
  left: auto;
  right: 0;
}

.block-feature-sixteen .media-img {
  border-radius: 30px;
}
.block-feature-sixteen .shape_01 {
  top: 31%;
  left: 50%;
}

.block-feature-seventeen .section-subheading {
  position: absolute;
  left: 0;
  top: 0;
  width: 37%;
}
.block-feature-seventeen .shape_01 {
  max-width: 8%;
  top: 12%;
  right: 43%;
}

.block-feature-eighteen {
  background: url(../images/media/img_52.jpg) no-repeat left top;
  background-size: cover;
}
.block-feature-eighteen:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(0, 0, 0, 0.44) 24.81%, rgba(0, 0, 0, 0) 94.81%);
  top: 0;
  left: 0;
  z-index: -1;
}
.block-feature-eighteen .video-icon {
  width: 170px;
  height: 170px;
  color: #000;
  padding: 20px;
  background: #FFDB1E;
}
.block-feature-eighteen .video-icon:hover {
  background: #DFFF5E;
}
.block-feature-eighteen .bg-wrapper {
  padding: 48px 60px 60px;
  border-radius: 20px;
  background: #144D41;
}
.block-feature-eighteen .bg-wrapper ul {
  position: relative;
  z-index: 1;
}
.block-feature-eighteen .bg-wrapper ul:before {
  content: "";
  position: absolute;
  width: 1px;
  height: 96%;
  border-left: 1px dashed rgba(255, 255, 255, 0.3);
  right: 20px;
  top: 2%;
  z-index: -1;
}
.block-feature-eighteen .bg-wrapper ul li {
  position: relative;
  background: #fff;
  border-radius: 45px;
  padding: 9px 25px 9px 45px;
  font-weight: 500;
  font-size: 20px;
  line-height: 1.2em;
  color: #1F5E59;
  display: inline-block;
  margin-bottom: 35px;
  margin-right: 52px;
}
.block-feature-eighteen .bg-wrapper ul li:last-child {
  margin-bottom: 0;
}
.block-feature-eighteen .bg-wrapper ul li:before {
  content: url(../images/icon/icon_111.svg);
  position: absolute;
  right: -52px;
  top: 0;
}
.block-feature-eighteen .bg-wrapper .more-btn {
  border-radius: 30px;
  padding: 5px 25px 5px 5px;
  color: #fff;
  font-weight: 500;
  font-size: 18px;
  border: 1px solid #fff;
}
.block-feature-eighteen .bg-wrapper .more-btn .icon {
  width: 40px;
  height: 40px;
  background: #FFDB1E;
}
.block-feature-eighteen .bg-wrapper .more-btn:hover {
  background: #fff;
  color: #1F5E59;
}

.text-feature-one .line-wrapper {
  border-top: 1px dashed #d7d7d7;
  border-bottom: 1px dashed #d7d7d7;
}
.text-feature-one .line-wrapper .shape_01 {
  width: 48px;
  bottom: 12%;
  right: 32%;
  animation: rotated 48s infinite linear;
}
.text-feature-one .card-style-three {
  border-right: 1px dashed #d7d7d7;
  border-bottom: 1px dashed #d7d7d7;
  padding-right: 100px;
}
.text-feature-one .card-style-three:last-child {
  border-bottom: none;
}
.text-feature-one .media-list-item {
  height: 600px;
  width: 590px;
  background: url(../images/media/img_04.jpg) no-repeat center;
  background-size: cover;
  border-radius: 30px;
  position: relative;
  z-index: 1;
}
.text-feature-one .media-list-item:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 50%;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 0 0 30px 30px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 3.1%, #000000 100%);
  mix-blend-mode: overlay;
}
.text-feature-one .media-list-item li {
  display: inline-block;
  font-weight: 500;
  color: #000;
  padding: 6px 50px 6px 50px;
  border-radius: 45px;
  background: #fff;
  margin: 7px 0;
  position: relative;
}
.text-feature-one .media-list-item li:before {
  content: "";
  position: absolute;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #CFFF45;
  right: 10px;
  top: 7px;
  transition: all 0.2s ease-in-out 0s;
}
.text-feature-one .media-list-item li:after {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  top: 6px;
  right: 14px;
  color: #29594B;
  transition: all 0.2s ease-in-out 0s;
}
.text-feature-one .media-list-item li:hover:before {
  background: #29594B;
}
.text-feature-one .media-list-item li:hover:after {
  color: #fff;
}

.text-feature-two {
  background: #0C3A30;
  z-index: 1;
}
.text-feature-two .shape_01 {
  top: 16%;
  left: 3%;
  width: 2.3%;
  min-width: 28px;
  animation: rotated 50s infinite linear;
}
.text-feature-two .shape_02 {
  bottom: 5%;
  right: 2%;
  width: 1.5%;
  min-width: 22px;
  animation: rotated 48s infinite linear;
}

.text-feature-three {
  z-index: 1;
}
.text-feature-three .counter-wrapper {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.text-feature-three .media-wrapper {
  background: url(../images/media/img_13.jpg) no-repeat center;
  background-size: cover;
  border-radius: 30px;
  z-index: 1;
  max-width: 590px;
}
.text-feature-three .media-wrapper .screen_01 {
  left: -9%;
  top: 12%;
  width: 37.3%;
  border-radius: 10px;
  box-shadow: 10px 30px 50px rgba(0, 0, 0, 0.06);
}
.text-feature-three .media-wrapper .screen_02 {
  right: -11%;
  bottom: -9%;
  width: 65.5%;
}
.text-feature-three .media-wrapper .screen_03 {
  right: -13%;
  bottom: -11%;
  width: 50.85%;
  box-shadow: 0px 35px 70px rgba(0, 0, 0, 0.06);
}
.text-feature-three .shape_01 {
  bottom: 2%;
  left: 39%;
  width: 2.3%;
  min-width: 28px;
  animation: rotated 50s infinite linear;
}

.text-feature-four {
  z-index: 1;
}
.text-feature-four .avatar {
  width: 65px;
  height: 65px;
}
.text-feature-four .name {
  font-size: 28px;
}
.text-feature-four .quote-text {
  font-size: 50px;
  line-height: 1.4em;
}
.text-feature-four .shape_01 {
  top: 56%;
  right: 22%;
  animation: rotated 60s infinite linear;
}
.text-feature-four .shape_02 {
  top: 12%;
  left: 9%;
  width: 16px;
  animation: rotated 50s infinite linear;
}

.text-feature-five .bg-wrapper {
  background: #FFF2AC;
}
.text-feature-five li {
  display: inline-block;
  font-weight: 500;
  color: #000;
  padding: 6px 50px 6px 50px;
  border-radius: 45px;
  background: #fff;
  margin: 7px 0;
  position: relative;
}
.text-feature-five li:before {
  content: "";
  position: absolute;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #FFF2AC;
  right: 10px;
  top: 7px;
  transition: all 0.2s ease-in-out 0s;
}
.text-feature-five li:after {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  top: 6px;
  right: 14px;
  color: #29594B;
  transition: all 0.2s ease-in-out 0s;
}
.text-feature-five li:hover:before {
  background: #29594B;
}
.text-feature-five li:hover:after {
  color: #fff;
}
.text-feature-five .media-wrapper {
  background: url(../images/media/img_20.jpg) no-repeat center;
  background-size: cover;
  z-index: 1;
}
.text-feature-five .media-wrapper .video-icon {
  width: 90px;
  height: 90px;
  background: #fff;
}
.text-feature-five .media-wrapper .video-icon:hover {
  background: #CFFF45;
}
.text-feature-five .media-wrapper .screen_01 {
  right: -9%;
  bottom: -9%;
  width: 46.3%;
  animation: jumpThree 5s infinite linear;
  box-shadow: 0px 35px 70px rgba(0, 0, 0, 0.06);
}

.text-feature-six {
  z-index: 1;
}
.text-feature-six .media-wrapper {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 63.64%;
  z-index: -1;
}
.text-feature-six .media-wrapper .screen_01 {
  width: 61.1%;
  bottom: 32%;
  left: -4%;
  z-index: 1;
  animation: jumpThree 5s infinite linear;
}
.text-feature-six .shape_01 {
  right: 0;
  bottom: 23%;
  width: 12.3%;
}
.text-feature-six .shape_02 {
  top: 1%;
  left: 39%;
}

.text-feature-seven ul li {
  font-weight: 500;
  color: #0E3E2F;
  position: relative;
  padding-right: 33px;
  margin-bottom: 22px;
}
.text-feature-seven ul li:after {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  top: 1px;
  right: 0;
}

.text-feature-nine {
  background: url(../images/assets/bg_03.svg) no-repeat center bottom;
  background-size: cover;
  z-index: 1;
}
.text-feature-nine .block .icon {
  height: 50px;
}
.text-feature-nine .vertical-text-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 45%;
  border-right: 1px solid rgba(254, 255, 252, 0.17);
}
.text-feature-nine .vertical-text-wrapper .text-list {
  padding: 0 35px;
  font-weight: 700;
  font-size: 125px;
  letter-spacing: -2px;
  height: 20%;
  width: 100%;
  border-top: 1px solid rgba(254, 255, 252, 0.17);
  display: flex;
  align-items: center;
  justify-content: end;
  color: #FFFFFF;
  mix-blend-mode: overlay;
  opacity: 0.8;
}
.text-feature-nine .vertical-text-wrapper .text-list:hover {
  color: #C5FF4A;
  opacity: 1;
  mix-blend-mode: normal;
}
.text-feature-nine .vertical-text-wrapper .shape_01 {
  right: -83px;
  bottom: 21%;
  animation: jumpThree 6s infinite linear;
}
.text-feature-nine .vertical-text-wrapper .shape_02 {
  right: 10%;
  top: 25%;
  animation: jumpFour 6s infinite linear;
}
.text-feature-nine .shape_03 {
  right: 5%;
  top: 10%;
  animation: jumpFour 6s infinite linear;
}
.text-feature-nine .shape_04 {
  bottom: 0;
  right: 13%;
  width: 29%;
}

.team-section-one .section-btn {
  position: absolute;
  left: 0;
  top: 25px;
}

.team-section-two {
  background: #fff;
  border: 1px solid #000;
}
.team-section-two .section-btn {
  position: absolute;
  left: 0;
  top: 30px;
}

.team-section-three {
  z-index: 1;
}
.team-section-three:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 45%;
  background: url(../images/shape/shape_09.svg) no-repeat right bottom;
  background-size: cover;
  z-index: -1;
  transform: scaleX(-1);
}
.team-section-three .section-btn {
  position: absolute;
  left: 0;
  top: 15px;
}
.team-section-three .shape_01 {
  width: 2.3%;
  top: 17%;
  right: 39%;
}
.team-section-three .shape_02 {
  width: 1.5%;
  bottom: 6%;
  left: 9%;
}
.team-section-three .shape_03 {
  max-width: 22%;
  bottom: 0;
  right: 0;
}

.team-details .bg-wrapper {
  background: #fff;
  border-radius: 30px;
  overflow: hidden;
}
.team-details .bg-wrapper .border-right {
  border-left: 1px solid #e9e9e9;
}
.team-details .bg-wrapper .member-img {
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.team-details .bg-wrapper .name {
  font-size: 32px;
}
.team-details .bg-wrapper .post {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.3);
}
.team-details .bg-wrapper h6 {
  font-size: 20px;
  padding: 42px 0 10px;
}
.team-details .bg-wrapper p {
  font-size: 18px;
}
.team-details .bg-wrapper .social-share li a {
  font-size: 20px;
  color: #19352D;
  margin-right: 20px;
}
.team-details .bg-wrapper .social-share li a:hover {
  color: #29594B;
}
.team-details .bg-wrapper h3 {
  font-size: 28px;
}
.team-details .bg-wrapper table {
  font-size: 18px;
}
.team-details .bg-wrapper table tr td:first-child {
  color: rgba(36, 64, 52, 0.6);
  padding: 13px 0;
}
.team-details .bg-wrapper table tr td:last-child {
  font-weight: 500;
  color: #19352D;
}

.faq-section-two {
  z-index: 1;
}
.faq-section-two .section-btn {
  position: absolute;
  right: 0;
  top: 13px;
}
.faq-section-two .shape_01 {
  top: 1%;
  right: 33%;
  width: 2.1%;
  min-width: 28px;
  animation: rotated 50s infinite linear;
}
.faq-section-two .shape_02 {
  bottom: 3%;
  left: 5%;
  width: 2.1%;
  min-width: 28px;
  animation: rotated 50s infinite linear;
}

.faq-section-three .tab-content {
  background: #fff;
  border-radius: 30px;
  padding: 0 60px;
}
.faq-section-three .tab-content .accordion-style-one .accordion-item:last-child {
  border-bottom: none;
}
.faq-section-three .tab-content .accordion-style-one .accordion-item:first-child {
  border-top: none;
}
.faq-section-three nav .nav {
  border: none;
}
.faq-section-three nav .nav .nav-link {
  background: transparent;
  border: 0;
  border-radius: 30px;
  height: 36px;
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
  padding: 0 20px;
  margin: 0 2px 10px;
}
.faq-section-three nav .nav .nav-link.active {
  background: #29594B;
  color: #fff;
}

.pricing-section .contact-banner {
  background: #fff;
  border-radius: 30px;
  z-index: 1;
  padding: 48px 45px;
}
.pricing-section .contact-banner h2 {
  font-size: 42px;
  line-height: 1.238em;
  margin: 0;
}
.pricing-section .contact-banner h2 span {
  font-weight: 700;
  font-style: italic;
  text-decoration-line: underline;
  text-decoration-thickness: 2px;
}
.pricing-section .contact-banner .screen_01 {
  right: 3%;
  top: 13%;
  width: 12.4%;
}

.pricing-nav .nav {
  border: 2px solid #186560;
  border-radius: 10px;
  overflow: hidden;
}
.pricing-nav .nav .nav-link {
  width: 150px;
  height: 61px;
  font-weight: 500;
  color: #186560;
  background: transparent;
  padding: 0;
  margin: 0;
  border: 0;
  border-radius: 0;
}
.pricing-nav .nav .nav-link.active {
  background: #186560;
  color: #fff;
}

.error-page {
  min-height: 100vh;
  padding: 200px 12px 50px;
  z-index: 1;
}
.error-page h1 {
  font-size: 140px;
  line-height: 1em;
}
.error-page h2 {
  font-size: 85px;
  padding: 32px 0 36px;
}
.error-page .shape_01 {
  right: 1%;
  bottom: 20%;
  width: 14.52%;
}
.error-page .shape_02 {
  left: 1%;
  bottom: 30%;
  width: 18.64%;
}

.accordion-style-one .accordion-item {
  border: none;
  border-top: 1px solid #E0E0E0;
  border-radius: 0;
}
.accordion-style-one .accordion-item .accordion-button {
  font-weight: 500;
  font-size: 25px;
  line-height: 1.55em;
  color: #19352D;
  padding: 33px 0;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}
.accordion-style-one .accordion-item .accordion-button:not(.collapsed) {
  padding-bottom: 20px;
}
.accordion-style-one .accordion-item .accordion-button:not(.collapsed):after {
  content: "-";
  font-size: 40px;
}
.accordion-style-one .accordion-item .accordion-button:after {
  content: "+";
  font-weight: 300;
  font-size: 32px;
  background: none;
  width: auto;
  height: auto;
}
.accordion-style-one .accordion-item .accordion-body {
  padding: 0 0 20px 35px;
}
.accordion-style-one .accordion-item .accordion-body p {
  margin: 0;
}
.accordion-style-one .accordion-item:last-child {
  border-bottom: 1px solid #E0E0E0;
}

.accordion-style-two .accordion-item {
  border: none;
  background: #FFFAEB;
  border-radius: 0;
}
.accordion-style-two .accordion-item .accordion-button {
  font-weight: 500;
  font-size: 32px;
  line-height: 1.55em;
  color: #AEA78F;
  padding: 28px 50px;
  background: transparent;
  border-radius: 40px 40px 0 0;
  box-shadow: none;
}
.accordion-style-two .accordion-item .accordion-button:not(.collapsed) {
  color: #000;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.accordion-style-two .accordion-item .accordion-button:not(.collapsed):after {
  content: "-";
  font-size: 1.5em;
  line-height: 36px;
}
.accordion-style-two .accordion-item .accordion-button:after {
  content: "+";
  font-weight: 400;
  text-align: center;
  color: #000;
  line-height: 48px;
  font-size: 32px;
  background: none;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #fff;
}
.accordion-style-two .accordion-item .accordion-body {
  padding: 20px 50px 55px;
}
.accordion-style-two .accordion-item .accordion-body h6 {
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.96px;
  margin-bottom: 15px;
  font-weight: 700;
}
.accordion-style-two .accordion-item .accordion-body ul li {
  display: inline-block;
  color: #272727;
  font-weight: 500;
  background: #fff;
  border-radius: 45px;
  padding: 6px 40px 6px 16px;
  margin-bottom: 13px;
  position: relative;
  z-index: 1;
}
.accordion-style-two .accordion-item .accordion-body ul li:before {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  font-size: 18px;
  top: 6px;
  right: 14px;
  color: #212121;
}
.accordion-style-two .accordion-item .accordion-body .media-wrapper {
  background-position: center;
  background-size: cover;
  border-radius: 30px;
  max-width: 610px;
}
.accordion-style-two .accordion-item .accordion-body .media-wrapper .video-icon {
  width: 90px;
  height: 90px;
  background: #fff;
}
.accordion-style-two .accordion-item .accordion-body .media-wrapper .video-icon:hover {
  background: #CFFF45;
}
.accordion-style-two .accordion-item:nth-child(1) {
  border-radius: 40px 40px 0 0;
}
.accordion-style-two .accordion-item:nth-child(2) {
  background: #FFFAEB;
}
.accordion-style-two .accordion-item:nth-child(2) .accordion-button {
  background: #FBF4DC;
}
.accordion-style-two .accordion-item:nth-child(2) .accordion-body {
  background: #FBF4DC;
}
.accordion-style-two .accordion-item:nth-child(3) {
  background: #f9efcf;
  border-radius: 0 0 40px 40px;
}
.accordion-style-two .accordion-item:nth-child(3) .accordion-button {
  background: #F9EFCF;
  border-radius: 0 0 40px 40px;
}
.accordion-style-two .accordion-item:nth-child(3) .accordion-body {
  background: #F9EFCF;
  border-radius: 0 0 40px 40px;
}

.accordion-style-three .accordion-item {
  border: none;
  border-bottom: 1px solid #E0E0E0;
  border-radius: 0;
}
.accordion-style-three .accordion-item .accordion-button {
  font-weight: 500;
  font-size: 24px;
  line-height: 1.55em;
  color: #0E3E2F;
  padding: 24px 0;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}
.accordion-style-three .accordion-item .accordion-button:after {
  content: "\f286";
  font-family: "bootstrap-icons";
  font-size: 20px;
  background: none;
  width: auto;
  height: auto;
}
.accordion-style-three .accordion-item .accordion-body {
  padding: 0 0 8px 35px;
}

.accordion-style-four .accordion-item {
  border: none;
  border-radius: 10px;
  overflow: hidden;
  background: transparent;
  margin-bottom: 30px;
}
.accordion-style-four .accordion-item .accordion-button {
  font-weight: 500;
  font-size: 26px;
  line-height: 1.55em;
  color: #1F5E59;
  padding: 24px 50px;
  background: #fff;
  border-radius: 0;
  box-shadow: none;
  transition: none;
}
.accordion-style-four .accordion-item .accordion-button span {
  font-weight: normal;
  font-size: 18px;
  display: inline-block;
  margin-left: 22px;
  min-width: 30px;
}
.accordion-style-four .accordion-item .accordion-button:not(.collapsed) {
  padding-bottom: 15px;
  background: #144D41;
  color: #fff;
}
.accordion-style-four .accordion-item .accordion-button:after {
  content: "\f286";
  font-family: "bootstrap-icons";
  font-size: 20px;
  background: none;
  width: auto;
  height: auto;
}
.accordion-style-four .accordion-item .accordion-body {
  background: #fff;
  padding: 0 102px 20px 15px;
  background: #144D41;
}
.accordion-style-four .accordion-item .accordion-body p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
}

.partner-logo-two {
  background: #000;
  border-bottom: 2px solid #000;
}
.partner-logo-two .bg-wrapper {
  background: #DFFF5E;
  border-radius: 30px;
}
.partner-logo-two .bg-wrapper .wrapper {
  max-width: 1660px;
  padding: 22px 12px;
}
.partner-logo-two .bg-wrapper .title {
  color: #000;
  font-size: 30px;
}
.partner-logo-two .bg-wrapper .logo-wrapper {
  font-weight: 700;
  color: #000;
  font-size: 30px;
  letter-spacing: -0.5px;
}
.partner-logo-two .bg-wrapper .logo-wrapper .br-name {
  margin: 0 10px;
}
.partner-logo-two .bg-wrapper .logo-wrapper .br-name img {
  width: 15px;
}

.partner-logo-one .logo {
  width: 100%;
  height: 60px;
}

.modal .modal-dialog {
  height: auto;
}
.modal .modal-dialog .modal-content {
  margin: 40px auto;
}

.user-data-form {
  background: #fff;
  margin: 0 auto;
  max-width: 720px;
  border-radius: 20px !important;
  padding: 50px 15px 40px;
}
.user-data-form h2 {
  font-size: 42px;
  font-weight: 500;
  margin-bottom: 10px;
}
.user-data-form p a {
  color: #31795A;
}
.user-data-form p a:hover {
  text-decoration: underline;
}
.user-data-form .form-wrapper {
  max-width: 565px;
}
.user-data-form .form-wrapper .input-group-meta input {
  width: 100%;
  height: 55px;
  font-size: 16px;
  border: 1px solid rgba(37, 64, 53, 0.06);
  border-radius: 8px;
  padding: 0 20px 0 52px;
  color: #000;
  background: rgba(0, 0, 0, 0.04);
}
.user-data-form .form-wrapper .input-group-meta label {
  font-size: 16px;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.5);
  display: block;
  margin-bottom: 6px;
}
.user-data-form .form-wrapper .input-group-meta .placeholder_icon {
  position: absolute;
  height: 55px;
  top: 30px;
  left: 0;
  bottom: 0;
  width: 50px;
  text-align: center;
  z-index: 1;
  color: rgba(0, 0, 0, 0.45);
  font-size: 17px;
}
.user-data-form .form-wrapper .input-group-meta .placeholder_icon img {
  position: relative;
  top: 50%;
  margin: 0 auto;
  transform: translateY(-50%);
}
.user-data-form .form-wrapper .input-group-meta .placeholder_icon span {
  width: 100%;
  height: 100%;
  cursor: pointer;
  display: block;
  position: relative;
}
.user-data-form .form-wrapper .input-group-meta .placeholder_icon span:before {
  content: "";
  width: 2px;
  height: 26px;
  background: #000;
  position: absolute;
  top: 14px;
  left: 24px;
  transform: rotate(45deg);
  z-index: 5;
  transition: all 0.2s ease-in-out;
}
.user-data-form .form-wrapper .input-group-meta .placeholder_icon span.eye-slash:before {
  opacity: 0;
}
.user-data-form .agreement-checkbox label {
  position: relative;
  font-size: 16px;
  font-weight: 500;
  color: #19352D;
  cursor: pointer;
  padding-right: 22px;
  transition: all 0.1s ease-in-out;
}
.user-data-form .agreement-checkbox label a {
  color: #0E3E2F;
  text-decoration: underline;
}
.user-data-form .agreement-checkbox input[type=checkbox] {
  display: none;
}
.user-data-form .agreement-checkbox label:before {
  content: "";
  width: 14px;
  height: 14px;
  line-height: 11px;
  border-radius: 2px;
  border: 2px solid #B3B3B3;
  font-size: 12px;
  text-align: center;
  position: absolute;
  right: 0;
  top: 4px;
  transition: all 0.1s ease-in-out;
}
.user-data-form .agreement-checkbox input[type=checkbox]:checked + label:before {
  content: "\f633";
  font-family: bootstrap-icons !important;
  background: #000;
  color: #fff;
  border-color: #000;
}
.user-data-form .agreement-checkbox a {
  position: relative;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.5);
}
.user-data-form .agreement-checkbox a:hover {
  text-decoration: underline;
  color: #0E3E2F;
}
.user-data-form .line {
  height: 1px;
  width: 100%;
  background: rgba(0, 0, 0, 0.2);
}
.user-data-form .social-use-btn {
  font-size: 16px;
  color: #000;
  height: 55px;
  border: 1px solid #E5E5E5;
  border-radius: 7px;
}
.user-data-form .social-use-btn:hover {
  background: rgba(0, 0, 0, 0.06);
}
.user-data-form .social-use-btn img {
  width: 20px;
}
.user-data-form .btn-close {
  position: absolute;
  right: 15px;
  top: 15px;
  box-shadow: none;
  z-index: 1;
}
.user-data-form .nav {
  background: #F0F5F3;
  border-radius: 40px;
}
.user-data-form .nav .nav-item {
  width: 50%;
}
.user-data-form .nav .nav-item .nav-link {
  font-weight: 500;
  display: block;
  width: 100%;
  border: 1px solid transparent;
  border-radius: 40px;
  font-size: 20px;
  color: #839B8F;
}
.user-data-form .nav .nav-item .nav-link.active {
  color: #29594B;
  border-color: #29594B;
}

.counter-block-one .main-count {
  font-size: 64px;
  color: #19352D;
}

.counter-block-two .main-count {
  font-size: 68px;
  color: #19352D;
  margin-bottom: -5px;
}

.counter-block-three .main-count {
  font-size: 70px;
  color: #1F5E59;
  margin-bottom: -5px;
}

.counter-block-four {
  border: 1px solid #1F5E59;
  padding: 30px 15px 20px;
  border-radius: 20px;
}
.counter-block-four .main-count {
  font-size: 90px;
  margin-bottom: -12px;
}

.pagination-one ul {
  margin: 0 -5;
}
.pagination-one ul li {
  padding: 0 5px;
  font-size: 20px;
  font-weight: 500;
  color: #19352D;
}
.pagination-one ul li:not(:last-child) a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  transition: all 0.2s ease-in-out 0s;
}
.pagination-one ul li:not(:last-child) a.active, .pagination-one ul li:not(:last-child) a:hover, .pagination-one ul li:not(:last-child) a:focus {
  background: #29594B;
  color: #fff;
}

.pagination-two {
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
  padding: 60px 0;
}
.pagination-two .pr-dir {
  font-size: 16px;
  letter-spacing: 0.89px;
  color: #979797;
}
.pagination-two .pr-name {
  font-size: 32px;
  color: #19352D;
  margin-top: 4px;
}
.pagination-two i {
  font-size: 30px;
  width: 75px;
  height: 75px;
  border: 1px solid #000;
  color: #000;
  background: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 66px;
  transition: all 0.2s ease-in-out 0s;
}
.pagination-two a:hover i {
  background: #CFFF45;
  border-color: #CFFF45;
}
.pagination-two.border-0 i {
  border: none;
}
.pagination-two.border-0 a:hover i {
  background: #FFE86B;
}

.card-style-one .icon {
  background: #fff;
  width: 62px;
  height: 62px;
}
.card-style-one .text {
  width: calc(100% - 62px);
}
.card-style-one:hover .icon {
  background: #CFFF45;
}

.card-style-two {
  position: relative;
  padding: 50px 40px 48px;
  background: #fff;
  border: 1px solid #EBF3EE;
  border-radius: 20px;
}
.card-style-two .icon {
  height: 50px;
}
.card-style-two .icon2 {
  height: 42px;
}
.card-style-two .arrow-btn {
  opacity: 0.3;
  width: 34px;
}
.card-style-two:hover {
  background: #CFFF45;
  border-color: #CFFF45;
}
.card-style-two:hover .arrow-btn {
  opacity: 1;
}

.card-style-three .icon {
  height: 38px;
}

.card-style-four .media:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(41, 89, 75, 0.7);
  opacity: 0;
  transition: all 0.3s ease-in-out 0s;
}
.card-style-four .media:after {
  content: "";
  position: absolute;
  width: 50px;
  height: 50px;
  transform: rotate(45deg);
  left: -25px;
  bottom: -25px;
  background: #fff;
}
.card-style-four .round-btn {
  position: absolute;
  font-size: 25px;
  width: 55px;
  height: 55px;
  background: #fff;
  color: #19352D;
  opacity: 0;
}
.card-style-four .round-btn:hover {
  background: #CFFF45;
}
.card-style-four:hover .media::before, .card-style-four:hover .round-btn {
  opacity: 1;
}

.card-style-five .icon {
  height: 42px;
}
.card-style-five .main-count {
  font-size: 68px;
  color: #FFDB1E;
  border-top: 1px dashed rgba(255, 255, 255, 0.25);
  margin-top: 42px;
  padding-top: 25px;
}
.card-style-five p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.6em;
}

.card-style-six {
  position: relative;
  padding: 50px 40px 48px;
  background: #fff;
  border-radius: 30px;
}
.card-style-six:hover, .card-style-six.active {
  background: #EDF1EE;
}
.card-style-six:hover .arrow-btn, .card-style-six.active .arrow-btn {
  opacity: 1;
}
.card-style-six .icon {
  width: 70px;
  height: 70px;
  background: #29594B;
}
.card-style-six .arrow-btn {
  opacity: 0.3;
  width: 34px;
}

.card-style-seven {
  position: relative;
  padding: 40px 30px 45px;
  background: #fff;
  border-radius: 30px;
}
.card-style-seven .icon {
  width: 70px;
  height: 70px;
  background: #FFE86B;
}
.card-style-seven p {
  font-size: 18px;
  line-height: 1.666em;
}
.card-style-seven .arrow-btn {
  opacity: 0.3;
  width: 34px;
}
.card-style-seven:hover, .card-style-seven.active {
  box-shadow: 0px 35px 70px rgba(0, 0, 0, 0.04);
}
.card-style-seven:hover .arrow-btn, .card-style-seven.active .arrow-btn {
  opacity: 1;
}
.card-style-seven:hover .icon, .card-style-seven.active .icon {
  background: #CFFF45;
}

.card-style-eight {
  padding: 40px 35px 30px;
  background: #fff;
  position: relative;
}
.card-style-eight:hover {
  box-shadow: 0px 10px 20px rgba(8, 32, 26, 0.04);
}
.card-style-eight .icon {
  height: 55px;
}
.card-style-eight .icon img {
  max-height: 100%;
}

.card-style-nine {
  background: #F6F6F6;
  padding: 50px 55px 0;
}
.card-style-nine .icon {
  width: 60px;
  height: 60px;
  background: #FFE86B;
}
.card-style-nine h3 {
  font-size: 32px;
}

.card-style-ten {
  padding-bottom: 60px;
}
.card-style-ten h4 {
  color: #0E3E2F;
}
.card-style-ten .main-count {
  font-size: 80px;
  color: #0E3E2F;
  z-index: 1;
  margin-bottom: -16px;
}
.card-style-ten .main-count:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 13px;
  background: #C5FF4A;
  left: 0;
  bottom: 19%;
  z-index: -1;
}

.card-style-eleven {
  position: relative;
  padding: 45px 40px 48px;
  background: #fff;
  border-radius: 20px;
}
.card-style-eleven h4 {
  font-family: "ClashDisplay";
  color: #0E3E2F;
}
.card-style-eleven .icon {
  height: 58px;
}
.card-style-eleven .arrow-btn {
  opacity: 0.3;
  width: 34px;
}
.card-style-eleven:hover {
  background: #f3f3f3;
}
.card-style-eleven:hover .arrow-btn {
  opacity: 1;
}

.card-style-twelve h4 {
  color: #0E3E2F;
}
.card-style-twelve .icon {
  width: 55px;
  height: 55px;
  border: 1px solid #E0E0E0;
}
.card-style-twelve .arrow-btn {
  opacity: 0.3;
  width: 34px;
}
.card-style-twelve:hover .icon {
  background: #E0E0E0;
}

.card-style-thirteen {
  background: #fff;
  border-radius: 200px;
  border: 1px solid #fff;
  padding: 110px 45px 120px;
  position: relative;
  z-index: 1;
}
.card-style-thirteen:before {
  content: "";
  position: absolute;
  width: 48px;
  height: 2px;
  background: #000;
  bottom: 11%;
  left: 50%;
  transform: translateX(-50%);
}
.card-style-thirteen .icon {
  height: 75px;
}
.card-style-thirteen .icon img {
  max-height: 100%;
}
.card-style-thirteen p {
  font-size: 18px;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: rgba(0, 0, 0, 0.4);
}
.card-style-thirteen h3 {
  font-size: 30px;
  line-height: 1.266em;
  color: #000;
}
.card-style-thirteen:hover {
  background: #DFFF5E;
  border-color: #000;
}

.card-style-fourteen .media:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(223, 255, 94, 0.3);
  opacity: 0;
  transition: all 0.3s ease-in-out 0s;
}
.card-style-fourteen .round-btn {
  position: absolute;
  font-size: 25px;
  width: 55px;
  height: 55px;
  background: #0C3A30;
  color: #fff;
  opacity: 0;
}
.card-style-fourteen .round-btn:hover {
  background: #CFFF45;
  color: #19352D;
}
.card-style-fourteen:hover .media::before, .card-style-fourteen:hover .round-btn {
  opacity: 1;
}
.card-style-fourteen p {
  color: #B6B6B6;
}

.card-style-fifteen {
  background: #fff;
  box-shadow: 0px 20px 40px rgba(0, 0, 0, 0.02);
  border-radius: 30px;
  overflow: hidden;
  text-align: center;
  padding-bottom: 30px;
}
.card-style-fifteen .media:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(41, 89, 75, 0.3);
  opacity: 0;
  transition: all 0.3s ease-in-out 0s;
}
.card-style-fifteen .round-btn {
  position: absolute;
  font-size: 25px;
  width: 55px;
  height: 55px;
  background: #0C3A30;
  color: #fff;
  opacity: 0;
}
.card-style-fifteen .round-btn:hover {
  background: #CFFF45;
  color: #19352D;
}
.card-style-fifteen:hover .media::before, .card-style-fifteen:hover .round-btn {
  opacity: 1;
}
.card-style-fifteen h4 {
  font-size: 22px;
}
.card-style-fifteen p {
  color: #B6B6B6;
}

.card-style-sixteen {
  position: relative;
}
.card-style-sixteen .icon {
  width: 60px;
  height: 60px;
  background: #FFE86B;
}
.card-style-sixteen .text {
  width: calc(100% - 60px);
  padding-right: 30px;
}
.card-style-sixteen:hover .icon, .card-style-sixteen.active .icon {
  background: #CFFF45;
}
.card-style-sixteen.arrow:before, .card-style-sixteen.arrow:after {
  content: url(../images/shape/shape_30.svg);
  position: absolute;
  top: 11px;
  left: -99px;
  transform: scaleX(-1);
}
.card-style-sixteen.arrow:after {
  left: auto;
  right: -99px;
}

.card-style-seventeen {
  padding: 30px 30px 40px;
  border-radius: 15px;
  border: 1px solid #E6E6E6;
}
.card-style-seventeen .icon {
  width: 70px;
  height: 70px;
  padding: 15px;
  background: #F6F6F6;
}
.card-style-seventeen .text {
  width: calc(100% - 70px);
  padding-right: 25px;
}
.card-style-seventeen .text .btn-three {
  color: #1F5E59;
}
.card-style-seventeen .text .btn-three i {
  font-size: 0.8em;
}
.card-style-seventeen:hover {
  background: #CFFF45;
  border-color: #CFFF45;
}
.card-style-seventeen:hover .icon {
  background: #fff;
}

.card-style-eighteen {
  padding: 5px 35px 0;
  border-radius: 20px;
  background: #1F5E59;
  z-index: 1;
}
.card-style-eighteen:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: 0;
  background: url(../images/assets/bg_08.svg) no-repeat right bottom;
  background-size: cover;
  z-index: -1;
  transform: scaleX(-1);
}
.card-style-eighteen .icon {
  width: 70px;
  height: 70px;
  padding: 15px;
  background: #CFFF45;
}
.card-style-eighteen blockquote {
  font-size: 36px;
  color: #fff;
  line-height: 1.555em;
  letter-spacing: -0.5px;
  margin: 30px 0 35px;
}
.card-style-eighteen h6 {
  font-size: 20px;
  color: #fff;
  margin: 0;
}
.card-style-eighteen h6 span {
  font-weight: normal;
  color: rgba(255, 255, 255, 0.4);
}

.card-style-nineteen {
  padding: 30px 50px 50px;
  border-radius: 20px;
  background: #fff;
}
.card-style-nineteen h2 {
  font-size: 48px;
}
.card-style-nineteen .counter-block-one .main-count {
  font-size: 58px;
  color: #1F5E59;
}

.card-style-twenty {
  background: #fff;
  border-radius: 20px;
  padding: 35px 35px 40px;
}
.card-style-twenty .icon {
  width: 60px;
  height: 60px;
  background: #1F5E59;
}
.card-style-twenty .text {
  width: calc(100% - 60px);
  padding-right: 35px;
}
.card-style-twenty .text .arrow-btn {
  position: absolute;
  left: 50px;
  top: 40px;
}
.card-style-twenty:hover {
  box-shadow: 0px 35px 70px rgba(0, 0, 0, 0.04);
}

.card-style-twentyOne .icon {
  width: 320px;
  height: 320px;
  padding: 13%;
  border: 1px solid rgba(25, 97, 100, 0.15);
}
.card-style-twentyOne .icon .numb {
  width: 46px;
  height: 46px;
  border: 1px solid #000;
  font-size: 24px;
  color: #000;
  bottom: -23px;
  left: calc(50% - 23px);
  z-index: 1;
  background: #fff;
}

.card-style-twentyTwo {
  background: #fff;
  border-radius: 20px;
  text-align: center;
  padding: 50px 12px 70px;
  position: relative;
}
.card-style-twentyTwo .icon {
  height: 83px;
}
.card-style-twentyTwo h4 {
  font-size: 28px;
  margin: 135px 0 20px;
  text-transform: capitalize;
}
.card-style-twentyTwo .learn-btn {
  font-size: 16px;
  text-transform: uppercase;
  color: #000;
  letter-spacing: 1px;
}
.card-style-twentyTwo:hover {
  background: #FFDB1E;
}

.card-style-twentyThree {
  background: #144D41;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.card-style-twentyThree .name {
  position: absolute;
  bottom: 15px;
  right: 15px;
  left: 15px;
  border-radius: 15px;
  padding: 15px 10px 10px;
  background: #fff;
  text-align: center;
  opacity: 0;
}
.card-style-twentyThree .name h4 {
  font-size: 22px;
}
.card-style-twentyThree:hover .name {
  opacity: 1;
}
.card-style-twentyThree:hover img {
  opacity: 0.3;
}

.pricing-card-one {
  background: #fff;
  border-radius: 20px;
  padding: 45px 70px 50px;
}
.pricing-card-one h2 {
  font-size: 36px;
}
.pricing-card-one p {
  line-height: 1.5em;
  color: #878787;
  padding: 17px 0 20px;
}
.pricing-card-one p span {
  color: #000;
}
.pricing-card-one .price-banner {
  padding: 8px 10px;
  border-radius: 15px;
  background: #F4F4F4;
  margin-bottom: 30px;
}
.pricing-card-one .price-banner .price {
  font-size: 52px;
  font-weight: 500;
  color: #19352D;
  line-height: initial;
}
.pricing-card-one .price-banner .price sup {
  font-size: 0.461em;
  top: -18px;
  right: 6px;
}
.pricing-card-one .price-banner strong {
  color: #19352D;
  display: block;
  margin-bottom: -9px;
}
.pricing-card-one .price-banner span {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
}
.pricing-card-one ul li {
  color: #000;
  margin-top: 10px;
}
.pricing-card-one .action-btn {
  max-width: 335px;
  margin: auto auto 0;
  border: 1px solid #DCDCDC;
  border-radius: 30px;
  padding: 10px 25px;
  font-size: 18px;
  color: #8B8B8B;
}
.pricing-card-one .action-btn a {
  font-weight: 500;
  color: #19352D;
}
.pricing-card-one .action-btn a i {
  display: inline-block;
  width: 20px;
  line-height: 15px;
  background: #0C3A30;
  color: #fff;
  font-size: 10px;
  border-radius: 50px;
  font-weight: 900;
  vertical-align: 3px;
  margin-left: 4px;
}
.pricing-card-one .action-btn a:hover {
  text-decoration: underline;
}

.service-details .details-meta h2 {
  font-size: 64px;
  line-height: 1.1875em;
  font-weight: 700;
  margin-bottom: 42px;
}
.service-details .details-meta p {
  margin-bottom: 35px;
}
.service-details .details-meta h3 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 25px;
}
.service-details .details-meta .line-wrapper {
  border-top: 1px dashed #e5e5e5;
  border-bottom: 1px dashed #e5e5e5;
}
.service-details .details-meta .quote-wrapper {
  padding: 50px 80px 60px;
  border-radius: 30px;
  z-index: 1;
}
.service-details .details-meta .quote-wrapper .icon {
  margin-top: 7px;
}
.service-details .details-meta .quote-wrapper blockquote {
  font-size: 48px;
  line-height: 1.354em;
  font-weight: 500;
  color: #19352D;
  margin-bottom: 30px;
}
.service-details .details-meta .quote-wrapper .shape_01 {
  bottom: 0;
  left: 1%;
  width: 37.2%;
}
.service-details .details-meta .list-item li {
  font-size: 22px;
  font-weight: 500;
  color: #000;
  padding-right: 45px;
  margin-bottom: 24px;
  position: relative;
}
.service-details .details-meta .list-item li:before {
  content: "";
  position: absolute;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  background: #FFF2AC;
  right: 0;
  top: 3px;
}
.service-details .details-meta .list-item li:after {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  font-size: 18px;
  top: 3px;
  right: 5px;
  color: #000;
}
.service-details .service-nav-item {
  background: #EBF3EE;
  border-radius: 20px;
  overflow: hidden;
  padding: 0 25px;
}
.service-details .service-nav-item a {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.4);
  padding: 24px 0;
  line-height: 20px;
  border-bottom: 1px dashed rgba(10, 64, 32, 0.2);
  transition: all 0.3s ease-in-out 0s;
}
.service-details .service-nav-item a img {
  width: 20px;
  margin-left: 22px;
  opacity: 0.3;
  transition: all 0.3s ease-in-out 0s;
}
.service-details .service-nav-item a.active, .service-details .service-nav-item a:hover {
  color: #0A4020;
}
.service-details .service-nav-item a.active img, .service-details .service-nav-item a:hover img {
  opacity: 1;
}
.service-details .service-nav-item li:last-child a {
  border: none;
}
.service-details .contact-banner {
  padding: 35px 3% 50px;
  background: #FFE86B;
  border-radius: 20px;
}
.service-details .contact-banner h3 {
  font-size: 32px;
  line-height: 1.25em;
  color: #000;
}
.service-details .contact-banner a {
  line-height: 36px;
  border: 2px solid #000;
  border-radius: 30px;
  color: #000;
  padding: 0 30px;
}
.service-details .contact-banner a:hover {
  background: #0C3A30;
  border-color: #0C3A30;
  color: #fff;
}

#isotop-gallery-wrapper {
  margin: 0 -20px;
}
#isotop-gallery-wrapper .grid-sizer, #isotop-gallery-wrapper .isotop-item {
  padding: 0 20px;
}
#isotop-gallery-wrapper.column-two .grid-sizer, #isotop-gallery-wrapper.column-two .isotop-item {
  width: 50%;
}

.portfolio-one {
  background: #fff;
  border: 1px solid #000;
}
.portfolio-one .section-btn {
  position: absolute;
  left: 0;
  top: 50px;
}

.portfolio-block-one .img-holder {
  position: relative;
  overflow: hidden;
}
.portfolio-block-one .img-holder.round-border {
  border-radius: 30px;
}
.portfolio-block-one .img-holder .expend {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  font-size: 35px;
  opacity: 0;
}
.portfolio-block-one .img-holder i {
  display: block;
  width: 50px;
  height: 50px;
  line-height: 44px;
  background: #fff;
  color: #000;
  text-align: center;
  border-radius: 50%;
  transition: all 0.3s ease-in-out 0s;
}
.portfolio-block-one .img-holder i:hover {
  background: #DFFF5E;
}
.portfolio-block-one:hover .expend {
  opacity: 1;
}
.portfolio-block-one:hover .img-meta {
  transform: scale(1.1) rotate(5deg);
}
.portfolio-block-one:hover .pj-title {
  text-decoration: underline;
}
.portfolio-block-one .caption {
  margin-top: 35px;
}
.portfolio-block-one .caption .tag {
  margin: 0 -3px;
}
.portfolio-block-one .caption .tag li {
  margin: 0 3px;
  font-size: 14px;
  text-transform: uppercase;
  color: #B8B8B8;
  padding: 0 14px;
  line-height: 21px;
  border: 1px solid #B8B8B8;
  border-radius: 15px;
}
.portfolio-block-one .caption .pj-title {
  font-size: 32px;
  color: #000;
  margin-top: 15px;
}
.portfolio-block-one .caption .arrow {
  font-size: 26px;
  color: #000;
}
.portfolio-block-one .caption .arrow:hover {
  color: #0C3A30;
}

.portfolio-two {
  z-index: 1;
}
.portfolio-two .slider-wrapper {
  width: 126vw;
  transform: translateX(13vw);
}
.portfolio-two .slider-wrapper .slick-slider {
  margin: 0 -25px;
}
.portfolio-two .slider-wrapper .slick-slider .slick-slide {
  margin: 0 25px 5px;
}
.portfolio-two .slick-arrow-one {
  margin-left: -2px;
  margin-right: -2px;
}
.portfolio-two .slick-arrow-one li {
  cursor: pointer;
  display: block;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  color: #1F5E59;
  text-align: center;
  line-height: 57px;
  font-size: 24px;
  margin: 0 2px;
  transition: all 0.2s ease-in-out 0s;
}
.portfolio-two .slick-arrow-one li:hover {
  background: #1F5E59;
  color: #fff;
}

.portfolio-block-two .img-wrapper {
  border-radius: 30px;
  overflow: hidden;
}
.portfolio-block-two .caption {
  padding: 40px 0 30px;
  border-bottom: 1px solid #044F3B;
}
.portfolio-block-two .caption span {
  display: block;
  font-size: 14px;
  letter-spacing: 3px;
  margin-bottom: 15px;
  color: rgba(0, 0, 0, 0.3);
}
.portfolio-block-two .caption h3 a {
  font-size: 40px;
  letter-spacing: -1px;
  color: #1F5E59;
}
.portfolio-block-two .caption h3 a:hover {
  text-decoration: underline;
}
.portfolio-block-two .caption .round-btn {
  font-size: 30px;
  width: 70px;
  height: 70px;
  border: 1px solid #1F5E59;
  color: #1F5E59;
}
.portfolio-block-two .caption .round-btn:hover {
  background: #CFFF45;
}

.portfolio-three .shape_01 {
  top: 6%;
  left: 50%;
  transform: translateX(-50%);
}

.portfolio-block-three {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 80px 0;
}
.portfolio-block-three:last-child {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.portfolio-block-three .num {
  font-size: 30px;
  font-weight: 500;
  color: #000;
}
.portfolio-block-three .media-img {
  width: 27%;
  padding: 0 45px 0 65px;
}
.portfolio-block-three .media-img .img {
  background: #044F3B;
  border-radius: 75px;
  overflow: hidden;
}
.portfolio-block-three .title {
  width: 40%;
  font-size: 48px;
  line-height: 1.2em;
  font-weight: 500;
  color: #1F5E59;
}
.portfolio-block-three .arrow-btn {
  margin-right: auto;
  width: 108px;
  height: 108px;
}
.portfolio-block-three .arrow-btn:hover {
  background: #FFDB1E;
}
.portfolio-block-three .arrow-btn:hover img {
  transform: rotate(-45deg);
}
.portfolio-block-three:hover .img img {
  opacity: 0.5;
}

.project-details-one {
  z-index: 2;
}
.project-details-one .project-info {
  border-bottom: 1px solid #E4E4E4;
}
.project-details-one .project-info:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 30%;
  z-index: -1;
  background: #CFFF45;
}
.project-details-one .project-info .inner-wrapper {
  max-width: 1600px;
  padding: 0 12px;
}
.project-details-one .project-info h3 {
  font-size: 48px;
  font-weight: normal;
  margin: 0;
}
.project-details-one .project-info h3 span {
  font-style: italic;
  text-decoration-line: underline;
  text-decoration-thickness: 2px;
}
.project-details-one .project-info li {
  width: 33.333%;
  padding: 35px 3%;
  border-left: 1px dashed rgba(0, 0, 0, 0.25);
  background: #CFFF45;
  margin-bottom: -1px;
}
.project-details-one .project-info li:last-child {
  border: none;
}
.project-details-one .project-info li .icon {
  max-width: 40px;
}
.project-details-one .project-info li .text1 {
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: #000;
}
.project-details-one .project-info li span {
  color: rgba(0, 0, 0, 0.5);
}
.project-details-one .upper-title {
  font-size: 18px;
  letter-spacing: 2px;
  margin-bottom: 15px;
  text-transform: uppercase;
}
.project-details-one h2 {
  font-size: 58px;
  font-weight: 700;
  margin-bottom: 35px;
}
.project-details-one p {
  font-size: 24px;
  line-height: 1.83em;
  margin-bottom: 40px;
}
.project-details-one .img-gallery img {
  border-radius: 30px;
  width: 100%;
  margin-bottom: 15px;
}
.project-details-one .social-share li {
  font-weight: 500;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.4);
}
.project-details-one .social-share li a {
  font-size: 20px;
  color: #19352D;
  margin-left: 20px;
}

.project-details-two .bg-wrapper {
  background: #fff;
  border-radius: 30px;
  overflow: hidden;
}
.project-details-two .slider-wrapper {
  height: 100%;
}
.project-details-two .slider-wrapper .carousel-item {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.project-details-two .slider-wrapper .carousel-control-next, .project-details-two .slider-wrapper .carousel-control-prev {
  width: 45px;
  height: 45px;
  background: #fff;
  color: #19352D;
  opacity: 1;
  left: 0;
  bottom: 0;
  top: auto;
  transition: all 0.2s ease-in-out 0s;
}
.project-details-two .slider-wrapper .carousel-control-next:hover, .project-details-two .slider-wrapper .carousel-control-prev:hover {
  background: #29594B;
  color: #fff;
}
.project-details-two .slider-wrapper .carousel-control-prev {
  right: auto;
  left: 45px;
}
.project-details-two .info-wrapper {
  padding: 55px 70px 85px 50px;
}
.project-details-two .info-wrapper h3 {
  font-size: 36px;
}
.project-details-two .info-wrapper .list-meta li {
  padding: 22px 0;
}
.project-details-two .info-wrapper .list-meta .icon {
  width: 40px;
}
.project-details-two .info-wrapper .list-meta .text1 {
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: #000;
}
.project-details-two .info-wrapper .list-meta span {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.5);
}
.project-details-two .info-wrapper .social-share li {
  font-weight: 500;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.4);
}
.project-details-two .info-wrapper .social-share li a {
  font-size: 20px;
  color: #19352D;
  margin-right: 20px;
}

.feedback-section-one {
  border-bottom: 1px dashed #CACACA;
}
.feedback-section-one.top-border {
  border-top: 1px dashed #CACACA;
}
.feedback-section-one .title-one {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}
.feedback-section-one .icon {
  position: absolute;
  width: 50px;
  height: 50px;
  padding: 12px;
  background: #CFFF45;
  left: 0;
  bottom: -1%;
}

.feedback-block-one blockquote {
  font-size: 32px;
  line-height: 1.531em;
  color: #000;
  margin-bottom: 40px;
}
.feedback-block-one .avatar {
  width: 60px;
  height: 60px;
}
.feedback-block-one .line {
  width: 70%;
  height: 1px;
  background: #000;
}
.feedback-block-one .count {
  width: 30%;
  text-align: left;
  padding-left: 15px;
  color: rgba(0, 0, 0, 0.3);
}

.feedback-section-two {
  z-index: 1;
}
.feedback-section-two .slider-wrapper {
  width: calc(100vw + 210px);
  transform: translateX(-105px);
}
.feedback-section-two .shape_01 {
  top: 0;
  left: 11%;
  width: 2.65%;
  min-width: 30px;
  animation: rotated 48s infinite linear;
}

.feedback-block-two {
  border: 1px solid #E1E2E2;
  border-radius: 20px;
  padding: 34px 48px 30px;
}
.feedback-block-two .avatar {
  width: 55px;
  height: 55px;
}
.feedback-block-two .name {
  color: #19352D;
}
.feedback-block-two blockquote {
  color: #0E3F30;
  margin: 24px 0 43px;
}
.feedback-block-two .bottom-line {
  border-top: 1px solid #E1E2E2;
  padding-top: 24px;
}
.feedback-block-two .bottom-line .rating li {
  color: #FFCC4A;
  margin-right: 5px;
  font-size: 18px;
}

.slick-center .feedback-block-two {
  background: #29594B;
  border-color: #29594B;
}
.slick-center .feedback-block-two .name, .slick-center .feedback-block-two blockquote {
  color: #fff;
}
.slick-center .feedback-block-two p {
  color: rgba(255, 255, 255, 0.5);
}
.slick-center .feedback-block-two .bottom-line {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.slick-center .feedback-block-two .bottom-line .icon {
  filter: brightness(0) invert(1);
}

.feedback-section-three .slider-wrapper {
  padding-right: 100px;
}
.feedback-section-three .slider-wrapper .icon {
  right: 0;
  top: 18px;
}
.feedback-section-three .slick-dots {
  margin: 50px 15px 0 0;
  padding: 0;
}
.feedback-section-three .slick-dots li button {
  width: 10px;
  height: 10px;
  background: #fff;
  border: 1px solid #000;
  padding: 0;
  margin-right: 3px;
}
.feedback-section-three .slick-dots .slick-active button {
  background: #000;
}
.feedback-section-three .rating-box {
  position: absolute;
  padding: 50px 37px 35px;
  right: -75px;
  bottom: -65px;
  z-index: 1;
  background: #000;
}
.feedback-section-three .rating-box .rating {
  font-size: 58px;
  letter-spacing: -1px;
  line-height: 25px;
  margin-bottom: 20px;
}
.feedback-section-three .shape_01 {
  width: 15px;
  top: 7%;
  right: 52%;
}
.feedback-section-three .shape_02 {
  width: 20px;
  bottom: 17%;
  left: 17%;
}

.feedback-block-three blockquote {
  font-size: 36px;
  line-height: 1.611em;
  color: #000;
}

.feedback-section-four .slider-arrows {
  position: absolute;
  left: 0;
  top: 80px;
  width: 125px;
  border: 1px solid #000;
  border-radius: 50px;
  overflow: hidden;
}
.feedback-section-four .slider-arrows li {
  width: 50%;
  line-height: 48px;
  text-align: center;
  cursor: pointer;
  font-weight: 900;
  color: #000;
  transition: all 0.2s ease-in-out 0s;
}
.feedback-section-four .slider-arrows li:hover {
  background: #000;
  color: #fff;
}
.feedback-section-four .partner-logo-one {
  border-bottom: 2px dashed #e3e3e3;
}

.feedback-block-four {
  border: 2px solid #0E3E2F;
  border-radius: 20px;
  padding: 35px 48px 35px;
}
.feedback-block-four .rating li {
  color: #FFCC4A;
  margin-right: 6px;
  font-size: 18px;
}
.feedback-block-four .icon {
  width: 30px;
}
.feedback-block-four .avatar {
  width: 55px;
  height: 55px;
}
.feedback-block-four blockquote {
  font-size: 28px;
  line-height: 1.571em;
  color: #0E3F30;
  margin: 37px 0 52px;
}
.feedback-block-four:hover {
  background: #F8FCF4;
  border-color: #F8FCF4;
}

.feedback-section-five {
  background: #DFFF5E;
  border: 1px solid #000;
  z-index: 1;
  overflow: hidden;
}
.feedback-section-five:before {
  content: "";
  position: absolute;
  width: 70%;
  height: 100%;
  left: 0;
  top: 0;
  background: url(../images/assets/bg_06.svg) no-repeat right bottom;
  background-size: cover;
  z-index: -1;
  transform: scaleX(-1);
}
.feedback-section-five .slick-dots {
  margin: 0;
  padding: 0;
  position: absolute;
  left: 0;
  top: -85px;
}
.feedback-section-five .slick-dots li button {
  width: 12px;
  height: 12px;
  background: #fff;
  border: 1px solid #000;
  padding: 0;
  margin-right: 3px;
}
.feedback-section-five .slick-dots .slick-active button {
  background: #000;
}
.feedback-section-five .shape_01 {
  bottom: 1%;
  right: 19%;
  width: 34%;
}
.feedback-section-five .shape_02 {
  bottom: 3%;
  left: 8%;
  width: 24%;
}

.feedback-block-five blockquote {
  font-weight: 700;
  font-size: 90px;
  line-height: 1.166em;
  color: #000;
  margin-bottom: 40px;
}
.feedback-block-five .name {
  font-size: 28px;
  color: #000;
}

.feedback-section-six {
  z-index: 1;
}
.feedback-section-six .icon-container {
  width: 90%;
  margin: 0 auto;
}
.feedback-section-six .icon-container:before, .feedback-section-six .icon-container:after {
  content: "";
  position: absolute;
  height: 1px;
  background: #000;
  width: calc(50% - 65px);
  top: 50%;
  left: 0;
}
.feedback-section-six .icon-container:after {
  left: auto;
  right: 0;
}
.feedback-section-six .icon-container .icon {
  width: 70px;
  height: 70px;
  padding: 15px;
  background: #CFFF45;
}
.feedback-section-six .slick-dots {
  margin: 95px 0 0;
  padding: 0;
  justify-content: center;
}
.feedback-section-six .slick-dots li button {
  width: 10px;
  height: 10px;
  background: #fff;
  border: 1px solid #1F5E59;
}
.feedback-section-six .slick-dots .slick-active button {
  background: #1F5E59;
}
.feedback-section-six .shape_01 {
  right: 5%;
  bottom: 0;
  max-width: 16%;
}
.feedback-section-six .shape_02 {
  left: 5%;
  bottom: 0;
  max-width: 16%;
}
.feedback-section-six .shape_03 {
  left: 0;
  top: 0;
  max-width: 16%;
}

.feedback-block-six p {
  font-size: 42px;
  line-height: 1.666em;
  color: #171717;
  margin-bottom: 45px;
}
.feedback-block-six h6 {
  display: inline-block;
}
.feedback-block-six h6:before, .feedback-block-six h6:after {
  content: "";
  position: absolute;
  height: 1px;
  background: #000;
  width: 140px;
  top: 20px;
  left: -175px;
}
.feedback-block-six h6:after {
  left: auto;
  right: -175px;
}

.feedback-section-seven .slider-arrows {
  position: absolute;
  top: 35%;
  width: 100%;
  max-width: 1600px;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.feedback-section-seven .slider-arrows li {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 1px solid #000;
  cursor: pointer;
}
.feedback-section-seven .slider-arrows li:hover {
  background-color: #D0FF45;
  border-color: #D0FF45;
}

.feedback-block-seven {
  text-align: center;
}
.feedback-block-seven blockquote {
  font-size: 48px;
  line-height: 1.3125em;
  font-weight: 500;
  color: #000;
  letter-spacing: -0.5px;
}
.feedback-block-seven .name {
  font-size: 22px;
}

.feedback-section-eight {
  background: #144D41;
}
.feedback-section-eight:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: url(../images/shape/shape_53.svg) no-repeat center;
  background-size: cover;
}
.feedback-section-eight .wrapper {
  background: #26685A;
  padding: 95px 80px;
  border-radius: 20px;
}
.feedback-section-eight .slider-arrows {
  position: absolute;
  left: -35px;
  right: -35px;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
}
.feedback-section-eight .slider-arrows li {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 4px solid #144D41;
  background: #2B5F54;
  padding: 15px;
  cursor: pointer;
}

.feedback-block-eight .media-img {
  border-radius: 20px;
}
.feedback-block-eight .icon {
  width: 70px;
  height: 70px;
  background: #FFDB1E;
}
.feedback-block-eight blockquote {
  font-size: 50px;
  line-height: 1.3em;
  font-weight: 800;
  color: #fff;
  font-style: italic;
  margin: 30px 0 52px;
}
.feedback-block-eight h6 {
  font-size: 22px;
  font-weight: 700;
  color: #fff;
  margin: 0;
}
.feedback-block-eight span {
  font-size: 22px;
  color: #fff;
}
.feedback-block-eight .rating li {
  margin-right: 6px;
}
.feedback-block-eight .rating li i {
  font-size: 18px;
  color: #FFDB1E;
}
.feedback-block-eight .rating li span {
  font-size: 20px;
  font-weight: 500;
  display: block;
  margin-left: 8px;
  color: #fff;
}

.blog-section-one {
  z-index: 1;
}
.blog-section-one:before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 17%;
  background: #EBF3EE;
  z-index: -1;
}
.blog-section-one .section-btn {
  position: absolute;
  left: 0;
  top: 65px;
  z-index: 1;
}

.blog-meta-one {
  background: #fff;
  border-radius: 30px;
}
.blog-meta-one .post-data {
  padding: 30px 35px 25px;
}
.blog-meta-one .post-data .post-info {
  font-size: 18px;
  color: #AAAAAA;
}
.blog-meta-one .post-data .blog-title {
  font-size: 32px;
  line-height: 1.281em;
}
.blog-meta-one .post-data .blog-title:hover {
  text-decoration: underline;
}
.blog-meta-one .post-img {
  height: 350px;
  border-radius: 0 0 30px 30px;
  padding: 0 0 25px 25px;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.blog-meta-one .post-img .round-btn {
  font-size: 25px;
  width: 55px;
  height: 55px;
  background: #fff;
  color: #19352D;
}
.blog-meta-one:hover .round-btn {
  background: #CFFF45;
}
.blog-meta-one.style-two {
  background: #F6F6F6;
  border-radius: 0;
}
.blog-meta-one.style-two .post-img {
  border-radius: 0;
}
.blog-meta-one.style-two .blog-title {
  font-weight: 700;
  font-size: 28px;
  color: #000;
}

.blog-section-two {
  z-index: 1;
}
.blog-section-two .section-btn {
  position: absolute;
  left: 0;
  top: 25px;
}
.blog-section-two .shape_01 {
  bottom: 7%;
  right: 15%;
  width: 1.1%;
  animation: rotated 50s infinite linear;
}

.blog-meta-two {
  background: #fff;
}
.blog-meta-two .post-data {
  padding-top: 30px;
}
.blog-meta-two .post-data .blog-title {
  width: 70%;
}
.blog-meta-two .post-data .blog-title h4 {
  font-size: 32px;
  line-height: 1.281em;
}
.blog-meta-two .post-data .blog-title:hover h4 {
  text-decoration: underline;
}
.blog-meta-two .post-data .round-btn {
  font-size: 25px;
  width: 60px;
  height: 60px;
  border: 1px solid #19352D;
  background: #fff;
  color: #19352D;
}
.blog-meta-two .post-data .round-btn:hover {
  background: #CFFF45;
  border-color: #CFFF45;
}
.blog-meta-two .post-data .post-info {
  border-top: 1px solid #D1D1D1;
  padding-top: 20px;
  margin-top: 20px;
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
}
.blog-meta-two .post-img {
  height: 350px;
  padding: 0 20px 20px 0;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.blog-meta-two .post-img .date {
  line-height: 38px;
  font-size: 16px;
  padding: 0 18px;
  text-transform: uppercase;
  background: #fff;
  color: #19352D;
}
.blog-meta-two.style-two {
  background: #F4F4F4;
  border-radius: 30px;
  overflow: hidden;
}
.blog-meta-two.style-two .post-img {
  height: 460px;
}
.blog-meta-two.style-two .post-data {
  padding: 26px 35px;
}
.blog-meta-two.style-two .post-data .post-info {
  border: none;
  padding: 0;
  margin: 0 0 12px;
}
.blog-meta-two.style-two .post-data .round-btn {
  border: none;
}

.blog-section-three {
  z-index: 1;
}
.blog-section-three .section-btn {
  position: absolute;
  left: 0;
  top: 80px;
  z-index: 1;
}

.blog-section-four {
  z-index: 5;
}
.blog-section-four.block-feature-three:before {
  display: none;
}
.blog-section-four.block-feature-three .block-one {
  background: url(../images/blog/blog_img_05.jpg) no-repeat center;
  height: 666px;
}

.blog-section-five {
  background: #EDF8EB;
  border: 1px solid #000;
  z-index: 5;
}
.blog-section-five .section-btn {
  position: absolute;
  left: 0;
  top: 50px;
}
.blog-section-five .wrapper {
  border-top: 2px solid #000;
}
.blog-section-five .wrapper:before {
  content: "";
  position: absolute;
  width: 2px;
  height: 100%;
  background: #000;
  top: 0;
  right: 50%;
}

.blog-meta-three .tag {
  line-height: 27px;
  border-radius: 20px;
  background: #000;
  font-size: 14px;
  padding: 0 16px;
  letter-spacing: 1px;
  color: #fff;
}
.blog-meta-three .blog-title {
  font-size: 50px;
  line-height: 1.16em;
  color: #000;
  margin: 37px 0 24px;
}
.blog-meta-three .round-btn {
  font-size: 25px;
  width: 50px;
  height: 50px;
  border: 1px solid #000;
  color: #19352D;
}
.blog-meta-three .round-btn:hover {
  background: #000;
  color: #fff;
}

.blog-meta-four {
  border: 1px solid #DFDFDF;
  border-radius: 30px;
  padding: 75px 35px 52px;
  text-align: center;
}
.blog-meta-four .post-data {
  position: relative;
  padding: 42px 12px 28px;
  border-top: 1px dashed #CBCBCB;
  border-bottom: 1px dashed #CBCBCB;
}
.blog-meta-four .post-data .icon {
  width: 55px;
  height: 55px;
  background: #29594B;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);
}
.blog-meta-four .post-data .blog-title h4 {
  font-size: 43px;
  line-height: 1.302em;
  margin: 0;
}
.blog-meta-four .post-data .blog-title h4:hover {
  text-decoration: underline;
}
.blog-meta-four .post-info {
  font-size: 20px;
  margin: 35px 0 0;
}
.blog-meta-four .post-info span {
  color: rgba(0, 0, 0, 0.5);
}

.blog-sidebar .sidebar-title {
  font-size: 32px;
  margin-bottom: 18px;
}
.blog-sidebar .sidebar-search {
  height: 65px;
  background: #F3F3F3;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}
.blog-sidebar .sidebar-search input {
  width: 100%;
  height: 100%;
  border: 0;
  background: transparent;
  padding: 0 50px 0 20px;
  font-size: 18px;
}
.blog-sidebar .sidebar-search button {
  position: absolute;
  width: 65px;
  left: 0;
  top: 0;
  bottom: 0;
  background: #29594B;
  color: #fff;
}
.blog-sidebar .sidebar-search button:hover {
  background: #CFFF45;
  color: #19352D;
}
.blog-sidebar .blog-category li a {
  line-height: 54px;
  color: #19352D;
  transition: all 0.2s ease-in-out 0s;
}
.blog-sidebar .blog-category li a span {
  color: rgba(0, 0, 0, 0.4);
}
.blog-sidebar .blog-category li a:hover {
  text-decoration: underline;
}
.blog-sidebar .blog-recent-news .recent-news {
  border-bottom: 1px solid #ebebeb;
  padding-bottom: 20px;
  margin-bottom: 35px;
}
.blog-sidebar .blog-recent-news .recent-news:last-child {
  border: none;
  margin: 0;
  padding: 0;
}
.blog-sidebar .blog-recent-news .recent-news .post-img {
  height: 222px;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 15px;
  margin-bottom: 15px;
}
.blog-sidebar .blog-recent-news .recent-news .date {
  font-size: 16px;
  color: #AAAAAA;
  margin-bottom: 5px;
}
.blog-sidebar .blog-recent-news .recent-news .blog-title h3 {
  font-size: 24px;
  line-height: 1.291em;
  max-width: 85%;
}
.blog-sidebar .blog-recent-news .recent-news .blog-title h3:hover {
  text-decoration: underline;
}
.blog-sidebar .blog-keyword ul {
  margin: 0 -4px;
}
.blog-sidebar .blog-keyword ul li {
  margin: 0 4px 10px;
}
.blog-sidebar .blog-keyword ul li a {
  line-height: 35px;
  background: #EFF6F3;
  border-radius: 30px;
  padding: 0 18px;
  font-size: 16px;
  color: #244034;
  transition: all 0.2s ease-in-out 0s;
}
.blog-sidebar .blog-keyword ul li a:hover {
  background: #29594B;
  color: #fff;
}
.blog-sidebar .contact-banner {
  padding: 35px 3% 50px;
  background: #CFFF45;
  border-radius: 20px;
}
.blog-sidebar .contact-banner h3 {
  font-size: 32px;
  line-height: 1.25em;
  color: #000;
}
.blog-sidebar .contact-banner a {
  line-height: 36px;
  border: 2px solid #000;
  border-radius: 30px;
  color: #000;
  padding: 0 30px;
}
.blog-sidebar .contact-banner a:hover {
  background: #0C3A30;
  border-color: #0C3A30;
  color: #fff;
}

.blog-details .post-details-meta {
  border-top: 1px dashed #BEBEBE;
  border-bottom: 1px dashed #BEBEBE;
  margin-top: 30px;
  padding: 36px 0 18px;
}
.blog-details .post-details-meta p {
  line-height: 1.8em;
  margin-bottom: 30px;
}
.blog-details .post-details-meta .quote-wrapper {
  text-align: center;
  margin: 62px 0 35px;
}
.blog-details .post-details-meta .quote-wrapper .wrapper {
  position: relative;
  padding: 38px 12px 40px;
  border-top: 1px dashed #CBCBCB;
  border-bottom: 1px dashed #CBCBCB;
}
.blog-details .post-details-meta .quote-wrapper .wrapper .icon {
  width: 55px;
  height: 55px;
  background: #29594B;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);
}
.blog-details .post-details-meta .quote-wrapper .wrapper h3 {
  font-size: 36px;
  line-height: 1.444em;
  margin: 0;
}
.blog-details .post-details-meta .quote-wrapper h6 {
  font-size: 20px;
  margin: 24px 0 0;
}
.blog-details .post-details-meta .quote-wrapper h6 span {
  color: rgba(0, 0, 0, 0.5);
}
.blog-details .post-details-meta .img-gallery {
  margin-bottom: 50px;
}
.blog-details .post-details-meta .img-gallery img {
  border-radius: 20px;
  margin-top: 15px;
}
.blog-details .post-details-meta h3 {
  font-size: 32px;
  margin-bottom: 20px;
}
.blog-details .post-details-meta .list-item li {
  font-size: 22px;
  font-weight: 500;
  color: #000;
  position: relative;
  padding-right: 33px;
  margin-bottom: 23px;
}
.blog-details .post-details-meta .list-item li:after {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  font-size: 0.95em;
  top: 3px;
  right: 0;
  color: #000;
}
.blog-details .bottom-widget {
  padding: 10px 0 5px;
}
.blog-details .bottom-widget .tags li:first-child {
  font-weight: 500;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.4);
  margin-left: 7px;
}
.blog-details .bottom-widget .tags a {
  line-height: 26px;
  border-radius: 16px;
  background: #fff;
  padding: 0 10px;
  color: #19352D;
  font-size: 16px;
  margin-right: 5px;
}
.blog-details .bottom-widget .tags a:hover {
  color: #000;
  text-decoration: underline;
}
.blog-details .bottom-widget .share-icon li:first-child {
  font-weight: 500;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.4);
}
.blog-details .bottom-widget .share-icon a {
  color: #19352D;
  font-size: 18px;
  margin-right: 17px;
}
.blog-details .grey-bg {
  background: #f4f4f4;
  border-radius: 30px;
  padding: 40px 35px;
}
.blog-details .blog-inner-title {
  font-size: 42px;
  margin-bottom: 10px;
}
.blog-details .blog-comment-area {
  margin: 60px 0;
}
.blog-details .blog-comment-area .comment {
  border-top: 1px dashed #CBCBCB;
  padding: 38px 0 20px;
}
.blog-details .blog-comment-area .comment:last-child {
  padding-bottom: 5px;
}
.blog-details .blog-comment-area .comment .reply-comment {
  border: none;
  padding: 40px 0 30px;
}
.blog-details .blog-comment-area .comment .user-avatar {
  width: 60px;
  height: 60px;
}
.blog-details .blog-comment-area .comment .comment-text {
  width: calc(100% - 60px);
  padding-right: 25px;
  position: relative;
}
.blog-details .blog-comment-area .comment .comment-text .name {
  font-size: 20px;
  color: #19352D;
}
.blog-details .blog-comment-area .comment .comment-text .date {
  font-size: 16px;
  color: #ADADAD;
}
.blog-details .blog-comment-area .comment .comment-text p {
  font-size: 18px;
  line-height: 32px;
  margin: 7px 0 10px 0px;
}
.blog-details .blog-comment-area .comment .comment-text .reply-btn {
  font-size: 13px;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0px;
  text-align: center;
  width: 60px;
  line-height: 25px;
  background: #29594B;
  border-radius: 3px;
}
.blog-details .blog-comment-area .comment .comment-text .reply-btn:hover {
  background: #CFFF45;
  color: #19352D;
}
.blog-details .blog-comment-form p a {
  color: #19352D;
}
.blog-details .blog-comment-form form label {
  font-size: 16px;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.5);
  display: block;
  padding-bottom: 5px;
}
.blog-details .blog-comment-form form input {
  display: block;
  font-size: 18px;
  width: 100%;
  height: 60px;
  border: none;
  border-radius: 8px;
  padding: 0 25px;
  background: #fff;
}
.blog-details .blog-comment-form form textarea {
  display: block;
  font-size: 17px;
  width: 100%;
  max-width: 100%;
  height: 145px;
  border: none;
  border-radius: 8px;
  padding: 20px 25px;
  background: #fff;
}

.map-banner {
  height: 600px;
}

.address-block-one .icon {
  width: 80px;
  height: 80px;
  background: #29594B;
}
.address-block-one .title {
  font-family: "Satoshi";
  font-size: 24px;
  padding: 20px 0 5px;
}
.address-block-one p {
  line-height: 1.6em;
  color: #878787;
}
.address-block-one p .call {
  color: #29594B;
}
.address-block-one p .call:hover {
  text-decoration: underline;
}
.address-block-one p .webaddress {
  color: #29594B;
  text-decoration: underline;
}

.contact-us-section .bg-wrapper {
  border-radius: 40px;
  padding: 55px 55px 60px;
}

.form-style-one .input-group-meta {
  position: relative;
}
.form-style-one .input-group-meta label {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 5px;
}
.form-style-one .input-group-meta input, .form-style-one .input-group-meta textarea {
  width: 100%;
  max-width: 100%;
  border: none;
  border-radius: 8px;
  background: #fff;
  font-size: 18px;
}
.form-style-one .input-group-meta input {
  height: 60px;
  padding: 0 20px;
}
.form-style-one .input-group-meta textarea {
  height: 165px;
  padding: 15px 20px;
}
.form-style-one .btn-four {
  font-weight: 500;
  line-height: 60px;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 2px;
  border-radius: 8px;
}

.form-style-two .input-group-meta {
  position: relative;
}
.form-style-two .input-group-meta input, .form-style-two .input-group-meta textarea {
  width: 100%;
  max-width: 100%;
  border: 1px solid #000;
  border-radius: 5px;
  background: #fff;
  font-size: 18px;
}
.form-style-two .input-group-meta input {
  height: 55px;
  padding: 0 20px;
}
.form-style-two .input-group-meta textarea {
  max-height: 160px;
  height: 160px;
  padding: 15px 20px;
}

#contact-form .help-block {
  position: absolute;
  left: 0;
  bottom: -18px;
  font-size: 13px;
  line-height: 20px;
  color: #ff4d44;
}
#contact-form .help-block ul {
  margin: 0;
}

.contact-section-one .media-img {
  border-radius: 20px;
}
.contact-section-one .shape_01 {
  top: 13%;
  left: 43%;
}

.footer-one {
  background: #0C3A30;
  padding-top: 95px;
}
.footer-one .bottom-footer {
  border-top: 1px dashed rgba(255, 255, 255, 0.25);
  margin-top: 32px;
  padding: 40px 0 25px;
}
.footer-one .social-icon a {
  color: #fff;
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transition: all 0.2s ease-in-out 0s;
}
.footer-one .social-icon a:hover {
  background: #CFFF45;
  color: #29594B;
}
.footer-one .footer-title {
  font-size: 24px;
  color: #fff;
  margin-bottom: 15px;
}
.footer-one .footer-nav-link a {
  color: rgba(255, 255, 255, 0.7);
  line-height: 46px;
  transition: all 0.2s ease-in-out 0s;
}
.footer-one .footer-nav-link a:hover {
  color: #fff;
}
.footer-one .footer-newsletter form {
  width: 410px;
  height: 54px;
  position: relative;
}
.footer-one .footer-newsletter form input {
  width: 100%;
  height: 100%;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
  padding: 0 30px 0 80px;
  border: none;
  border-radius: 35px;
  background: rgba(255, 255, 255, 0.1);
}
.footer-one .footer-newsletter form input::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.footer-one .footer-newsletter form input:-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.footer-one .footer-newsletter form input::-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.footer-one .footer-newsletter form input:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.footer-one .footer-newsletter form button {
  width: 50px;
  height: 50px;
  font-weight: 900;
  text-align: center;
  border-radius: 50%;
  background: #CFFF45;
  color: #29594B;
  position: absolute;
  left: 0;
  top: 2px;
  transition: all 0.2s ease-in-out 0s;
}
.footer-one .footer-newsletter form button:hover {
  background: #fff;
}
.footer-one .copyright {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
}
.footer-one .bottom-nav {
  margin: 0 -12px;
}
.footer-one .bottom-nav a {
  font-size: 18px;
  font-weight: 500;
  margin: 0 12px;
  color: rgba(255, 255, 255, 0.7);
}
.footer-one .bottom-nav a:hover {
  color: #fff;
  text-decoration: underline;
}

.footer-two {
  background: url(../images/media/img_18.jpg) no-repeat center bottom;
  background-size: cover;
  position: relative;
  z-index: 1;
  padding: 110px 15px 135px;
}
.footer-two.no-bg {
  background: none;
  padding: 0;
  position: statics;
}
.footer-two.no-bg:before {
  display: none;
}
.footer-two.no-bg .bg-wrapper {
  box-shadow: none;
  padding-left: 0;
  padding-right: 0;
}
.footer-two.no-bg .bg-wrapper .copyright {
  right: 0;
}
.footer-two:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
  left: 0;
  z-index: -1;
  background: linear-gradient(180deg, #F5F8F7 0%, rgba(240, 243, 242, 0.58) 69.88%, rgba(238, 241, 240, 0.15) 100%);
}
.footer-two .bg-wrapper {
  background: #FFFFFF;
  box-shadow: 0px 35px 70px rgba(0, 0, 0, 0.04);
  border-radius: 30px;
  z-index: 1;
  padding: 70px 40px 40px;
}
.footer-two .bg-wrapper .shape_01 {
  bottom: 12%;
  left: 33%;
  width: 2.3%;
  min-width: 28px;
}
.footer-two .bg-wrapper .shape_02 {
  bottom: 38%;
  right: 25%;
  width: 3.8%;
  min-width: 28px;
}
.footer-two .social-icon a {
  color: #29594B;
  font-size: 17px;
  width: 40px;
  height: 40px;
  border: 1px solid #29594B;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  border-radius: 50%;
  transition: all 0.2s ease-in-out 0s;
}
.footer-two .social-icon a:hover {
  background: #29594B;
  border-color: #29594B;
  color: #fff;
}
.footer-two .footer-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
}
.footer-two .footer-nav-link a {
  color: rgba(0, 0, 0, 0.8);
  line-height: 42px;
  transition: all 0.2s ease-in-out 0s;
}
.footer-two .footer-nav-link a:hover {
  color: #19352D;
  text-decoration: underline;
}
.footer-two .copyright {
  font-size: 18px;
  color: #19352D;
  position: absolute;
  right: 40px;
  bottom: 64px;
}

.footer-three {
  padding: 75px 0 0;
  position: relative;
  z-index: 1;
}
.footer-three .round-bg {
  width: 350px;
  height: 350px;
  padding: 15px 15px;
  background: #FFF6C6;
}
.footer-three .round-bg.color-two {
  background: #F6FFDD;
}
.footer-three .footer-intro p a {
  font-weight: 500;
  font-size: 24px;
  color: #000;
}
.footer-three .footer-intro p a:hover {
  color: #19352D;
  text-decoration: underline;
}
.footer-three .footer-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
}
.footer-three .footer-nav-link a {
  color: rgba(0, 0, 0, 0.5);
  line-height: 46px;
  transition: all 0.2s ease-in-out 0s;
}
.footer-three .footer-nav-link a:hover {
  color: #19352D;
  text-decoration: underline;
}
.footer-three .bottom-footer {
  border-top: 1px solid #e2e2e2;
  margin-top: 40px;
  padding: 40px 0 25px;
}
.footer-three .bottom-footer .copyright {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.7);
}
.footer-three .bottom-footer .bottom-nav {
  margin: 0 -12px;
}
.footer-three .bottom-footer .bottom-nav a {
  font-size: 18px;
  font-weight: 500;
  margin: 0 12px;
  color: #000;
}
.footer-three .bottom-footer .bottom-nav a:hover {
  text-decoration: underline;
}
.footer-three .bottom-footer .social-icon {
  margin: 0 -10px;
}
.footer-three .bottom-footer .social-icon a {
  margin: 0 10px;
}
.footer-three .bottom-footer .social-icon a:hover {
  color: #19352D;
}
.footer-three .shape_01 {
  top: -3%;
  right: -3%;
}
.footer-three .shape_02 {
  bottom: 8%;
  left: 33%;
  animation: rotated 48s infinite linear;
}

.footer-large-wrapper {
  z-index: 1;
}
.footer-large-wrapper:before {
  content: "";
  position: absolute;
  width: 100%;
  right: 0;
  bottom: 0;
  top: -20%;
  background: url(../images/assets/bg_04.svg) no-repeat center bottom;
  background-size: cover;
  z-index: -1;
}

.footer-four {
  padding: 75px 0 0;
}
.footer-four .footer-intro p {
  line-height: 30px;
  color: rgba(255, 255, 255, 0.6);
}
.footer-four .footer-intro li {
  margin-top: 15px;
}
.footer-four .footer-intro li .icon {
  width: 20px;
}
.footer-four .footer-intro li a {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}
.footer-four .footer-intro li a:hover {
  text-decoration: underline;
}
.footer-four .footer-title {
  display: inline-block;
  position: relative;
  font-size: 24px;
  color: #fff;
  font-weight: 500;
  margin-bottom: 15px;
}
.footer-four .footer-title:before {
  content: "";
  position: absolute;
  right: 0;
  bottom: -6px;
  width: 20px;
  height: 2px;
  border-radius: 10px;
  background: #D3FF76;
}
.footer-four .footer-nav-link a {
  color: rgba(255, 255, 255, 0.75);
  line-height: 44px;
  font-weight: 300;
  transition: all 0.2s ease-in-out 0s;
}
.footer-four .footer-nav-link a:hover {
  color: #fff;
  text-decoration: underline;
}
.footer-four .bottom-footer {
  border-top: 1px dashed #37665c;
  margin-top: 40px;
  padding: 40px 0 25px;
}
.footer-four .bottom-footer .copyright {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.75);
}
.footer-four .bottom-footer .bottom-nav {
  margin: 0 -12px;
}
.footer-four .bottom-footer .bottom-nav a {
  font-size: 18px;
  margin: 0 12px;
  color: #fff;
}
.footer-four .bottom-footer .bottom-nav a:hover {
  text-decoration: underline;
}
.footer-four .bottom-footer .social-icon {
  margin: 0 -10px;
}
.footer-four .bottom-footer .social-icon a {
  margin: 0 10px;
  color: #fff;
}
.footer-four .bottom-footer .social-icon a:hover {
  color: #E6FD5A;
}

.footer-five {
  background: #000;
  padding: 105px 0 20px;
  z-index: 1;
  overflow: hidden;
}
.footer-five:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
  left: 0;
  background: url(../images/assets/bg_07.svg) no-repeat center bottom;
  background-size: cover;
  z-index: -1;
}
.footer-five h2 {
  font-size: 132px;
  line-height: 1.185em;
}
.footer-five .btn-group {
  padding: 15px 55px 15px 15px;
  border-radius: 80px;
  background: #DFFF5E;
  margin: 85px 0 110px;
}
.footer-five .btn-group h3 {
  font-size: 70px;
  color: #000;
  margin: 0;
}
.footer-five .btn-group h3 span {
  font-weight: 700;
  font-style: italic;
  text-decoration-line: underline;
  text-decoration-thickness: 4px;
}
.footer-five .btn-group .round-btn {
  width: 125px;
  height: 125px;
  text-align: center;
  font-size: 55px;
  color: #DFFF5E;
  background: #101010;
}
.footer-five .btn-group .round-btn:hover, .footer-five .btn-group .round-btn:focus {
  background: #29594B;
  color: #fff;
}
.footer-five .social-icon {
  margin: 0 -20px;
}
.footer-five .social-icon a {
  color: #fff;
  margin: 0 20px;
}
.footer-five .social-icon a:hover {
  color: #DFFF5E;
}
.footer-five .bottom-nav {
  margin: 0 -12px;
}
.footer-five .bottom-nav a {
  font-size: 18px;
  font-weight: 500;
  margin: 0 12px;
  color: #fff;
}
.footer-five .bottom-nav a:hover {
  text-decoration: underline;
}
.footer-five .shape_01 {
  top: 11%;
  right: 10%;
  width: 3.4%;
  animation: rotated 48s infinite linear;
}
.footer-five .shape_02 {
  bottom: 41%;
  left: 13%;
  width: 1.7%;
  animation: rotated 48s infinite linear;
}

/* Custome Animation */
@keyframes jump {
  0% {
    transform: translate3d(0, 0, 0);
  }
  40% {
    transform: translate3d(0, 50%, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jumpTwo {
  0% {
    transform: translate3d(0, 0, 0);
  }
  40% {
    transform: translate3d(0, 20px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jumpThree {
  0% {
    transform: translate3d(0, 0, 0);
  }
  40% {
    transform: translate3d(0, -20px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jumpFour {
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, -10px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jumpFive {
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, 10px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes prXOne {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-15px);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes prXTwo {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(15px);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes rotated {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotatedTwo {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
  }
}
@keyframes rotatedHalf {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(90deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotatedHalfTwo {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(-90deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes scale-right {
  0% {
    transform: translateX(0%);
  }
  50% {
    transform: translateX(20%);
  }
  100% {
    transform: translateX(0%);
  }
}
@keyframes fade-in {
  0% {
    opacity: 0.2;
  }
  40% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}
@keyframes hvr-ripple-out {
  0% {
    top: -5px;
    right: -5px;
    bottom: -5px;
    left: -5px;
  }
  50% {
    top: -15px;
    right: -15px;
    bottom: -15px;
    left: -15px;
    opacity: 0.8;
  }
  100% {
    top: -5px;
    right: -5px;
    bottom: -5px;
    left: -5px;
    opacity: 1;
  }
}
@keyframes hvr-ripple-out-two {
  0% {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  100% {
    top: -30px;
    right: -30px;
    bottom: -30px;
    left: -30px;
    opacity: 0;
  }
}
@keyframes scale-up-one {
  0% {
    transform: scale(1);
  }
  40% {
    transform: scale(0.5);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes scale-up-two {
  0% {
    transform: scale(0.5);
  }
  40% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(0.5);
  }
}
@keyframes scale-up-three {
  0% {
    transform: scale(1);
  }
  40% {
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
/*!
Animate.css - http://daneden.me/animate
Licensed under the MIT license

Copyright (c) 2013 Daniel Eden

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/
.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animated.hinge {
  animation-duration: 2s;
}
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-30px);
  }
  60% {
    transform: translateY(-15px);
  }
}
.bounce {
  animation-name: bounce;
}
@keyframes flash {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}
.flash {
  animation-name: flash;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
.pulse {
  animation-name: pulse;
}
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}
.shake {
  animation-name: shake;
}
@keyframes swing {
  20% {
    transform: rotate(15deg);
  }
  40% {
    transform: rotate(-10deg);
  }
  60% {
    transform: rotate(5deg);
  }
  80% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
.swing {
  transform-origin: top center;
  animation-name: swing;
}
@keyframes tada {
  0% {
    transform: scale(1);
  }
  10%, 20% {
    transform: scale(0.9) rotate(-3deg);
  }
  30%, 50%, 70%, 90% {
    transform: scale(1.1) rotate(3deg);
  }
  40%, 60%, 80% {
    transform: scale(1.1) rotate(-3deg);
  }
  100% {
    transform: scale(1) rotate(0);
  }
}
.tada {
  animation-name: tada;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes wobble {
  0% {
    transform: translateX(0%);
  }
  15% {
    transform: translateX(-25%) rotate(-5deg);
  }
  30% {
    transform: translateX(20%) rotate(3deg);
  }
  45% {
    transform: translateX(-15%) rotate(-3deg);
  }
  60% {
    transform: translateX(10%) rotate(2deg);
  }
  75% {
    transform: translateX(-5%) rotate(-1deg);
  }
  100% {
    transform: translateX(0%);
  }
}
.wobble {
  animation-name: wobble;
}
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
.bounceIn {
  animation-name: bounceIn;
}
@keyframes bounceInDown {
  0% {
    opacity: 0;
    transform: translateY(-2000px);
  }
  60% {
    opacity: 1;
    transform: translateY(30px);
  }
  80% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}
.bounceInDown {
  animation-name: bounceInDown;
}
@keyframes bounceInLeft {
  0% {
    opacity: 0;
    transform: translateX(-2000px);
  }
  60% {
    opacity: 1;
    transform: translateX(30px);
  }
  80% {
    transform: translateX(-10px);
  }
  100% {
    transform: translateX(0);
  }
}
.bounceInLeft {
  animation-name: bounceInLeft;
}
@keyframes bounceInRight {
  0% {
    opacity: 0;
    transform: translateX(2000px);
  }
  60% {
    opacity: 1;
    transform: translateX(-30px);
  }
  80% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(0);
  }
}
.bounceInRight {
  animation-name: bounceInRight;
}
@keyframes bounceInUp {
  0% {
    opacity: 0;
    transform: translateY(2000px);
  }
  60% {
    opacity: 1;
    transform: translateY(-30px);
  }
  80% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(0);
  }
}
.bounceInUp {
  animation-name: bounceInUp;
}
@keyframes bounceOut {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}
.bounceOut {
  animation-name: bounceOut;
}
@keyframes bounceOutDown {
  0% {
    transform: translateY(0);
  }
  20% {
    opacity: 1;
    transform: translateY(-20px);
  }
  100% {
    opacity: 0;
    transform: translateY(2000px);
  }
}
.bounceOutDown {
  animation-name: bounceOutDown;
}
@keyframes bounceOutLeft {
  0% {
    transform: translateX(0);
  }
  20% {
    opacity: 1;
    transform: translateX(20px);
  }
  100% {
    opacity: 0;
    transform: translateX(-2000px);
  }
}
.bounceOutLeft {
  animation-name: bounceOutLeft;
}
@keyframes bounceOutRight {
  0% {
    transform: translateX(0);
  }
  20% {
    opacity: 1;
    transform: translateX(-20px);
  }
  100% {
    opacity: 0;
    transform: translateX(2000px);
  }
}
.bounceOutRight {
  animation-name: bounceOutRight;
}
@keyframes bounceOutUp {
  0% {
    transform: translateY(0);
  }
  20% {
    opacity: 1;
    transform: translateY(20px);
  }
  100% {
    opacity: 0;
    transform: translateY(-2000px);
  }
}
.bounceOutUp {
  animation-name: bounceOutUp;
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.fadeIn {
  animation-name: fadeIn;
}
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeInDown {
  animation-name: fadeInDown;
}
@keyframes fadeInDownBig {
  0% {
    opacity: 0;
    transform: translateY(-2000px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeInDownBig {
  animation-name: fadeInDownBig;
}
@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.fadeInLeft {
  animation-name: fadeInLeft;
}
@keyframes fadeInLeftBig {
  0% {
    opacity: 0;
    transform: translateX(-2000px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.fadeInLeftBig {
  animation-name: fadeInLeftBig;
}
@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.fadeInRight {
  animation-name: fadeInRight;
}
@keyframes fadeInRightBig {
  0% {
    opacity: 0;
    transform: translateX(2000px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.fadeInRightBig {
  animation-name: fadeInRightBig;
}
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeInUp {
  animation-name: fadeInUp;
}
@keyframes fadeInUpBig {
  0% {
    opacity: 0;
    transform: translateY(2000px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeInUpBig {
  animation-name: fadeInUpBig;
}
@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.fadeOut {
  animation-name: fadeOut;
}
@keyframes fadeOutDown {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(20px);
  }
}
.fadeOutDown {
  animation-name: fadeOutDown;
}
@keyframes fadeOutDownBig {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(2000px);
  }
}
.fadeOutDownBig {
  animation-name: fadeOutDownBig;
}
@keyframes fadeOutLeft {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-20px);
  }
}
.fadeOutLeft {
  animation-name: fadeOutLeft;
}
@keyframes fadeOutLeftBig {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-2000px);
  }
}
.fadeOutLeftBig {
  animation-name: fadeOutLeftBig;
}
@keyframes fadeOutRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(20px);
  }
}
.fadeOutRight {
  animation-name: fadeOutRight;
}
@keyframes fadeOutRightBig {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(2000px);
  }
}
.fadeOutRightBig {
  animation-name: fadeOutRightBig;
}
@keyframes fadeOutUp {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}
.fadeOutUp {
  animation-name: fadeOutUp;
}
@keyframes fadeOutUpBig {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-2000px);
  }
}
.fadeOutUpBig {
  animation-name: fadeOutUpBig;
}
@keyframes flip {
  0% {
    transform: perspective(400px) translateZ(0) rotateY(0) scale(1);
    animation-timing-function: ease-out;
  }
  40% {
    transform: perspective(400px) translateZ(150px) rotateY(170deg) scale(1);
    animation-timing-function: ease-out;
  }
  50% {
    transform: perspective(400px) translateZ(150px) rotateY(190deg) scale(1);
    animation-timing-function: ease-in;
  }
  80% {
    transform: perspective(400px) translateZ(0) rotateY(360deg) scale(0.95);
    animation-timing-function: ease-in;
  }
  100% {
    transform: perspective(400px) translateZ(0) rotateY(360deg) scale(1);
    animation-timing-function: ease-in;
  }
}
.animated.flip {
  backface-visibility: visible;
  animation-name: flip;
}
@keyframes flipInX {
  0% {
    transform: perspective(400px) rotateX(90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotateX(-10deg);
  }
  70% {
    transform: perspective(400px) rotateX(10deg);
  }
  100% {
    transform: perspective(400px) rotateX(0deg);
    opacity: 1;
  }
}
.flipInX {
  backface-visibility: visible !important;
  animation-name: flipInX;
}
@keyframes flipInY {
  0% {
    transform: perspective(400px) rotateY(90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotateY(-10deg);
  }
  70% {
    transform: perspective(400px) rotateY(10deg);
  }
  100% {
    transform: perspective(400px) rotateY(0deg);
    opacity: 1;
  }
}
.flipInY {
  backface-visibility: visible !important;
  animation-name: flipInY;
}
@keyframes flipOutX {
  0% {
    transform: perspective(400px) rotateX(0deg);
    opacity: 1;
  }
  100% {
    transform: perspective(400px) rotateX(90deg);
    opacity: 0;
  }
}
.flipOutX {
  animation-name: flipOutX;
  backface-visibility: visible !important;
}
@keyframes flipOutY {
  0% {
    transform: perspective(400px) rotateY(0deg);
    opacity: 1;
  }
  100% {
    transform: perspective(400px) rotateY(90deg);
    opacity: 0;
  }
}
.flipOutY {
  backface-visibility: visible !important;
  animation-name: flipOutY;
}
@keyframes lightSpeedIn {
  0% {
    transform: translateX(100%) skewX(-30deg);
    opacity: 0;
  }
  60% {
    transform: translateX(-20%) skewX(30deg);
    opacity: 1;
  }
  80% {
    transform: translateX(0%) skewX(-15deg);
    opacity: 1;
  }
  100% {
    transform: translateX(0%) skewX(0deg);
    opacity: 1;
  }
}
.lightSpeedIn {
  animation-name: lightSpeedIn;
  animation-timing-function: ease-out;
}
@keyframes lightSpeedOut {
  0% {
    transform: translateX(0%) skewX(0deg);
    opacity: 1;
  }
  100% {
    transform: translateX(100%) skewX(-30deg);
    opacity: 0;
  }
}
.lightSpeedOut {
  animation-name: lightSpeedOut;
  animation-timing-function: ease-in;
}
@keyframes rotateIn {
  0% {
    transform-origin: center center;
    transform: rotate(-200deg);
    opacity: 0;
  }
  100% {
    transform-origin: center center;
    transform: rotate(0);
    opacity: 1;
  }
}
.rotateIn {
  animation-name: rotateIn;
}
@keyframes rotateInDownLeft {
  0% {
    transform-origin: left bottom;
    transform: rotate(-90deg);
    opacity: 0;
  }
  100% {
    transform-origin: left bottom;
    transform: rotate(0);
    opacity: 1;
  }
}
.rotateInDownLeft {
  animation-name: rotateInDownLeft;
}
@keyframes rotateInDownRight {
  0% {
    transform-origin: right bottom;
    transform: rotate(90deg);
    opacity: 0;
  }
  100% {
    transform-origin: right bottom;
    transform: rotate(0);
    opacity: 1;
  }
}
.rotateInDownRight {
  animation-name: rotateInDownRight;
}
@keyframes rotateInUpLeft {
  0% {
    transform-origin: left bottom;
    transform: rotate(90deg);
    opacity: 0;
  }
  100% {
    transform-origin: left bottom;
    transform: rotate(0);
    opacity: 1;
  }
}
.rotateInUpLeft {
  animation-name: rotateInUpLeft;
}
@keyframes rotateInUpRight {
  0% {
    transform-origin: right bottom;
    transform: rotate(-90deg);
    opacity: 0;
  }
  100% {
    transform-origin: right bottom;
    transform: rotate(0);
    opacity: 1;
  }
}
.rotateInUpRight {
  animation-name: rotateInUpRight;
}
@keyframes rotateOut {
  0% {
    transform-origin: center center;
    transform: rotate(0);
    opacity: 1;
  }
  100% {
    transform-origin: center center;
    transform: rotate(200deg);
    opacity: 0;
  }
}
.rotateOut {
  animation-name: rotateOut;
}
@keyframes rotateOutDownLeft {
  0% {
    transform-origin: left bottom;
    transform: rotate(0);
    opacity: 1;
  }
  100% {
    transform-origin: left bottom;
    transform: rotate(90deg);
    opacity: 0;
  }
}
.rotateOutDownLeft {
  animation-name: rotateOutDownLeft;
}
@keyframes rotateOutDownRight {
  0% {
    transform-origin: right bottom;
    transform: rotate(0);
    opacity: 1;
  }
  100% {
    transform-origin: right bottom;
    transform: rotate(-90deg);
    opacity: 0;
  }
}
.rotateOutDownRight {
  animation-name: rotateOutDownRight;
}
@keyframes rotateOutUpLeft {
  0% {
    transform-origin: left bottom;
    transform: rotate(0);
    opacity: 1;
  }
  100% {
    transform-origin: left bottom;
    transform: rotate(-90deg);
    opacity: 0;
  }
}
.rotateOutUpLeft {
  animation-name: rotateOutUpLeft;
}
@keyframes rotateOutUpRight {
  0% {
    transform-origin: right bottom;
    transform: rotate(0);
    opacity: 1;
  }
  100% {
    transform-origin: right bottom;
    transform: rotate(90deg);
    opacity: 0;
  }
}
.rotateOutUpRight {
  animation-name: rotateOutUpRight;
}
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(150px);
  }
  100% {
    transform: translateY(0);
  }
}
.slideInUp {
  animation-name: slideInUp;
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-2000px);
  }
  100% {
    transform: translateY(0);
  }
}
.slideInDown {
  animation-name: slideInDown;
}
@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-200px);
  }
  100% {
    transform: translateX(0);
  }
}
.slideInLeft {
  animation-name: slideInLeft;
}
@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(200px);
  }
  100% {
    transform: translateX(0);
  }
}
.slideInRight {
  animation-name: slideInRight;
}
@keyframes slideOutLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-2000px);
  }
}
.slideOutLeft {
  animation-name: slideOutLeft;
}
@keyframes slideOutRight {
  0% {
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(2000px);
  }
}
.slideOutRight {
  animation-name: slideOutRight;
}
@keyframes slideOutUp {
  0% {
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-2000px);
  }
}
.slideOutUp {
  animation-name: slideOutUp;
}
@keyframes hinge {
  0% {
    transform: rotate(0);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
  }
  20%, 60% {
    transform: rotate(80deg);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
  }
  40% {
    transform: rotate(60deg);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
  }
  80% {
    transform: rotate(60deg) translateY(0);
    opacity: 1;
    transform-origin: top left;
    animation-timing-function: ease-in-out;
  }
  100% {
    transform: translateY(700px);
    opacity: 0;
  }
}
.hinge {
  animation-name: hinge;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes rollIn {
  0% {
    opacity: 0;
    transform: translateX(-100%) rotate(-120deg);
  }
  100% {
    opacity: 1;
    transform: translateX(0px) rotate(0deg);
  }
}
.rollIn {
  animation-name: rollIn;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes rollOut {
  0% {
    opacity: 1;
    transform: translateX(0px) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateX(100%) rotate(120deg);
  }
}
.rollOut {
  animation-name: rollOut;
}
@keyframes jump {
  0% {
    transform: translate3d(0, 0, 0);
  }
  40% {
    transform: translate3d(0, 50%, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jumpTwo {
  0% {
    transform: translate3d(0, 0, 0);
  }
  40% {
    transform: translate3d(0, 20px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jumpThree {
  0% {
    transform: translate3d(0, 0, 0);
  }
  40% {
    transform: translate3d(0, -20px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jumpFour {
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, -10px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jumpFive {
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, 10px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes prXOne {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-15px);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes prXTwo {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(15px);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes rotated {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rotatedTwo {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
  }
}
@keyframes rotatedHalf {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(90deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes rotatedHalfTwo {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(-90deg);
  }
  100% {
    transform: rotate(0);
  }
}
@keyframes scale-right {
  0% {
    transform: translateX(0%);
  }
  50% {
    transform: translateX(20%);
  }
  100% {
    transform: translateX(0%);
  }
}
@keyframes fade-in {
  0% {
    opacity: 0.2;
  }
  40% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}
@keyframes hvr-ripple-out {
  0% {
    top: -5px;
    right: -5px;
    bottom: -5px;
    left: -5px;
  }
  50% {
    top: -15px;
    right: -15px;
    bottom: -15px;
    left: -15px;
    opacity: 0.8;
  }
  100% {
    top: -5px;
    right: -5px;
    bottom: -5px;
    left: -5px;
    opacity: 1;
  }
}
@keyframes hvr-ripple-out-two {
  0% {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  100% {
    top: -30px;
    right: -30px;
    bottom: -30px;
    left: -30px;
    opacity: 0;
  }
}
@keyframes scale-up-one {
  0% {
    transform: scale(1);
  }
  40% {
    transform: scale(0.5);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes scale-up-two {
  0% {
    transform: scale(0.5);
  }
  40% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(0.5);
  }
}
@keyframes scale-up-three {
  0% {
    transform: scale(1);
  }
  40% {
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
.shop-filter-one .theme-select-menu {
  width: 100%;
  min-width: 145px;
  height: 45px;
  font-size: 17px;
  padding: 0 12px;
  box-shadow: none;
  outline: none;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.product-block-one {
  overflow: hidden;
}
.product-block-one .img-holder {
  position: relative;
  background: #F5F5F5;
  z-index: 5;
  margin-bottom: 22px;
}
.product-block-one .img-holder .cart-icon {
  display: block;
  width: 35px;
  height: 35px;
  line-height: 35px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.09);
  border-radius: 50%;
  background: #fff;
  color: #000;
  text-align: center;
  position: absolute;
  left: 20px;
  top: 20px;
  font-size: 18px;
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.2s ease-in-out 0s;
}
.product-block-one .img-holder .cart-icon:hover {
  color: #19352D;
  background: #CFFF45;
}
.product-block-one .img-holder .cart-button {
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 20px;
  line-height: 48px;
  background: #29594B;
  text-align: center;
  color: #fff;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.4s ease-in-out 0s;
}
.product-block-one .img-holder .cart-button:hover {
  color: #19352D;
  background: #CFFF45;
}
.product-block-one .product-title {
  font-weight: 500;
  color: #19352D;
  font-size: 20px;
  text-transform: capitalize;
}
.product-block-one .rating {
  margin: 0 -3px;
}
.product-block-one .rating li {
  font-size: 15px;
  margin: 0 3px;
  color: #B3B3B3;
}
.product-block-one .rating li .bi-star-fill {
  color: #FFCB65;
}
.product-block-one .price {
  font-weight: 500;
  font-size: 22px;
  color: #0E3E2F;
  margin-top: 10px;
}
.product-block-one:hover .cart-button {
  opacity: 1;
  transform: translateY(0);
}
.product-block-one:hover .product-img {
  transform: scale(0.95);
}
.product-block-one:hover .cart-icon {
  opacity: 1;
  transform: scale(1);
}
.product-block-one:hover .product-title {
  text-decoration: underline;
}

.product-details-one .product-img-tab {
  border: none;
}
.product-details-one .product-img-tab .nav-link {
  width: 100%;
  background: #F6F6F6;
  border: 1px solid transparent;
  border-radius: 0;
  padding: 5px 0;
  height: 88px;
  margin-bottom: 12px;
}
.product-details-one .product-img-tab .nav-link img {
  max-height: 100%;
}
.product-details-one .product-img-tab .nav-link.active {
  background: #fff;
  border-color: #000;
}
.product-details-one .product-img-tab-content {
  background: #F6F6F6;
  padding: 20px;
}
.product-details-one .product-img-tab-content img {
  margin: auto;
}
.product-details-one .product-info .stock-tag {
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  background: #EAEAEA;
  border-radius: 3px;
  line-height: 25px;
  padding: 0 10px;
}
.product-details-one .product-info .product-name {
  text-transform: capitalize;
  font-size: 32px;
  margin: 25px 0 15px;
}
.product-details-one .product-info .rating li {
  font-size: 15px;
  margin-right: 6px;
  color: #B3B3B3;
}
.product-details-one .product-info .rating li .bi-star-fill {
  color: #FFCB65;
}
.product-details-one .product-info .rating li a {
  font-size: 17px;
  color: #000;
  margin-left: 12px;
}
.product-details-one .product-info .rating li a:hover {
  text-decoration: underline;
}
.product-details-one .product-info .price {
  font-size: 26px;
  font-weight: 700;
  color: #29594B;
  padding: 25px 0 5px;
}
.product-details-one .product-info .price del {
  font-size: 0.7em;
  opacity: 0.6;
}
.product-details-one .product-info .availability {
  color: #989CA2;
  font-size: 17px;
}
.product-details-one .product-info .description-text {
  padding: 10px 0 15px;
}
.product-details-one .product-info .product-feature {
  margin-bottom: 20px;
}
.product-details-one .product-info .product-feature li {
  color: #000;
  position: relative;
  padding-right: 30px;
  margin-bottom: 8px;
}
.product-details-one .product-info .product-feature li:before {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  top: 2px;
  right: 0;
  color: #29594B;
}
.product-details-one .product-info .customize-order h6 {
  font-size: 18px;
  margin: 0 0 0 15px;
}
.product-details-one .product-info .customize-order .quantity .button-group {
  border: 1px solid #e3e3e3;
  display: inline-block;
}
.product-details-one .product-info .customize-order .quantity .button-group li {
  line-height: 40px;
  max-height: 40px;
}
.product-details-one .product-info .customize-order .quantity .button-group li button {
  font-size: 25px;
  color: #C9C9C9;
  background: transparent;
  width: 32px;
}
.product-details-one .product-info .customize-order .quantity .button-group li .product-value {
  font-size: 18px;
  font-weight: 500;
  height: 40px;
  color: #000;
  max-width: 45px;
  background: transparent;
  border: none;
  text-align: center;
  padding-left: 5px;
}
.product-details-one .product-review-tab .nav-tabs {
  border-bottom: 1px solid #EBEBEB;
}
.product-details-one .product-review-tab .nav-tabs .nav-link {
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  color: #19352D;
  letter-spacing: 1px;
  padding: 0 0 12px 0;
  position: relative;
  margin: 0 0 0 50px;
  border: none;
  border-radius: 0;
}
.product-details-one .product-review-tab .nav-tabs .nav-link:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  left: 0;
  bottom: -1px;
  z-index: 1;
  background: #000;
  transform: scale(0, 1);
  transition: all 0.3s ease-in-out 0s;
}
.product-details-one .product-review-tab .nav-tabs .nav-link.active:before {
  transform: scale(1);
}
.product-details-one .product-review-tab .nav-tabs .nav-item:last-child .nav-link {
  margin-left: 0;
}
.product-details-one .product-review-tab .tab-content h5 {
  font-weight: 700;
  font-size: 20px;
  margin-bottom: 18px;
}
.product-details-one .product-review-tab .tab-content .product-feature li {
  position: relative;
  padding-right: 30px;
  margin-bottom: 18px;
}
.product-details-one .product-review-tab .tab-content .product-feature li:before {
  content: "\f633";
  position: absolute;
  font-family: "bootstrap-icons";
  top: 2px;
  right: 0;
  color: #29594B;
}
.product-details-one .product-review-tab .user-comment-area .single-comment {
  padding-bottom: 40px;
}
.product-details-one .product-review-tab .user-comment-area .single-comment:last-child {
  padding-bottom: 0;
}
.product-details-one .product-review-tab .user-comment-area .user-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}
.product-details-one .product-review-tab .user-comment-area .user-comment-data {
  padding-left: 30px;
  position: relative;
}
.product-details-one .product-review-tab .user-comment-area .user-comment-data .name {
  font-size: 18px;
  margin-bottom: 4px;
}
.product-details-one .product-review-tab .user-comment-area .user-comment-data .rating li {
  font-size: 14px;
  margin: 0 3px;
  color: #B3B3B3;
}
.product-details-one .product-review-tab .user-comment-area .user-comment-data .rating li .bi-star-fill {
  color: #FFCB65;
}
.product-details-one .product-review-tab .user-comment-area .user-comment-data p {
  padding-top: 8px;
}

.cart-list-form {
  position: relative;
}
.cart-list-form .table {
  margin: 0;
  background: transparent;
}
.cart-list-form .table th {
  font-size: 15px;
  font-weight: 700;
  letter-spacing: 2px;
  border: none;
  border-bottom: 2px solid #000;
  padding: 0 0 30px;
  text-align: center;
  text-transform: uppercase;
  color: #19352D;
  background: transparent;
}
.cart-list-form .table th:first-child {
  text-align: right;
}
.cart-list-form .table tbody td {
  padding: 0 0 70px;
  border: none;
  vertical-align: middle;
  text-align: center;
  background: transparent;
}
.cart-list-form .table tbody tr:first-child td {
  padding-top: 60px;
}
.cart-list-form .table tbody tr:last-child td {
  padding-bottom: 40px;
}
.cart-list-form .table tbody .product-thumbnails {
  width: 85px;
}
.cart-list-form .table tbody .product-thumbnails img {
  max-height: 100%;
  max-width: none;
}
.cart-list-form .table tbody .product-thumbnails .product-img {
  display: block;
  max-width: 85px;
  max-height: 85px;
  overflow: hidden;
}
.cart-list-form .table tbody .product-info {
  padding-right: 30px;
  text-align: right;
}
.cart-list-form .table tbody .product-info .product-name {
  font-weight: 700;
  font-size: 20px;
  color: #19352D;
}
.cart-list-form .table tbody .product-info .serial {
  font-size: 15px;
  color: rgba(31, 31, 31, 0.5);
  padding-bottom: 6px;
}
.cart-list-form .table tbody .product-info ul li {
  display: inline-block;
  font-size: 16px;
  color: #000;
  padding-right: 15px;
}
.cart-list-form .table tbody .price {
  font-weight: 500;
  font-size: 18px;
  color: #19352D;
}
.cart-list-form .table tbody .quantity li {
  display: inline-block;
  line-height: 40px;
  max-height: 40px;
}
.cart-list-form .table tbody .quantity li .btn {
  font-size: 24px;
  padding: 0;
  border: none;
  vertical-align: inherit;
  color: #1d1d1d;
  background: transparent;
}
.cart-list-form .table tbody .quantity li .product-value {
  font-size: 18px;
  font-weight: 500;
  color: #1d1d1d;
  max-width: 55px;
  background: transparent;
  border: none;
  text-align: center;
  padding-left: 12px;
}
.cart-list-form .table tbody .remove-product {
  color: #000;
  font-size: 22px;
}
.cart-list-form .table tbody .remove-product:hover {
  color: #ff2759;
}
.cart-list-form .cart-footer {
  border-top: 2px solid #545454;
  margin-top: 28px;
  padding-top: 40px;
}
.cart-list-form .cart-footer .coupon-form input {
  width: 240px;
  height: 50px;
  box-shadow: none;
  outline: none;
  border: none;
  border-bottom: 2px solid #545454;
  font-size: 18px;
  margin-left: 30px;
  background: transparent;
}
.cart-list-form .cart-footer .cart-total-section {
  text-align: left;
  padding-left: 82px;
}
.cart-list-form .cart-footer .cart-total-section .cart-total-table tr th {
  font-size: 18px;
  font-weight: 500;
  color: rgba(29, 29, 29, 0.5);
  font-weight: normal;
  padding-left: 26px;
  padding-bottom: 16px;
}
.cart-list-form .cart-footer .cart-total-section .cart-total-table tr td {
  font-size: 18px;
  font-weight: 500;
  color: #19352D;
  padding-bottom: 16px;
}

.checkout-toggle-area p {
  margin-bottom: 10px;
}
.checkout-toggle-area p button {
  font-weight: 500;
  letter-spacing: 0px;
  color: #19352D;
  background: transparent;
  display: inline-block;
  text-decoration: underline;
}
.checkout-toggle-area form input {
  width: 100%;
  height: 60px;
  font-size: 18px;
  border: none;
  border-radius: 8px;
  padding: 0 30px;
  margin-bottom: 20px;
}
.checkout-toggle-area form input:focus {
  border-color: #777;
}
.checkout-toggle-area form .lost-passw {
  color: #636067;
  font-size: 0.8em;
  margin: 12px 0 35px;
}
.checkout-toggle-area form .lost-passw:hover {
  text-decoration: underline;
}
.checkout-toggle-area form button {
  line-height: 50px;
}
.checkout-toggle-area form p {
  font-size: 0.9em;
  padding-top: 15px;
}

.checkout-form .main-title {
  font-size: 28px;
  padding-bottom: 55px;
}
.checkout-form .single-input-wrapper {
  display: block;
  width: 100%;
  height: 60px;
  font-size: 18px;
  border: none;
  padding: 0 15px;
  border: none;
  border-radius: 8px;
  margin-bottom: 55px;
}
.checkout-form .theme-select-menu {
  display: block;
  width: 100%;
  height: 60px;
  font-size: 18px;
  border: none;
  padding: 0 15px;
  border: none;
  box-shadow: none;
  outline: none;
  border-radius: 8px;
  margin-bottom: 55px;
}
.checkout-form .theme-select-menu option {
  font-size: 0.85em;
}
.checkout-form .checkbox-list {
  padding-bottom: 44px;
}
.checkout-form .checkbox-list li label {
  position: relative;
  font-weight: 500;
  font-size: 17px;
  line-height: 15px;
  padding-left: 28px;
  color: #19352D;
  cursor: pointer;
  margin: 0 0 24px;
}
.checkout-form .checkbox-list li label:before {
  content: "";
  width: 15px;
  height: 15px;
  line-height: 15px;
  border-radius: 2px;
  border: 1px solid #000;
  font-size: 12px;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
}
.checkout-form .checkbox-list li input[type=checkbox] {
  display: none;
}
.checkout-form .checkbox-list li input[type=checkbox]:checked + label:before {
  content: "\f272";
  font-family: bootstrap-icons !important;
  background: #373737;
  color: #fff;
  border-color: #373737;
}
.checkout-form .other-note-area p {
  font-weight: 500;
  font-size: 16px;
  color: #19352D;
  margin-bottom: 6px;
}
.checkout-form .other-note-area textarea {
  width: 100%;
  border: none;
  padding: 15px;
  resize: none;
  height: 145px;
}
.checkout-form .order-confirm-sheet .order-review {
  background: #fff;
  padding: 50px 40px;
}
.checkout-form .order-confirm-sheet .order-review .product-review {
  width: 100%;
}
.checkout-form .order-confirm-sheet .order-review .product-review tbody {
  color: #19352D;
}
.checkout-form .order-confirm-sheet .order-review .product-review tbody th {
  padding-bottom: 15px;
}
.checkout-form .order-confirm-sheet .order-review .product-review tbody th span {
  font-weight: 500;
  font-size: 18px;
}
.checkout-form .order-confirm-sheet .order-review .product-review tbody td {
  padding-bottom: 15px;
  font-size: 18px;
  font-weight: 500;
  text-align: left;
}
.checkout-form .order-confirm-sheet .order-review .product-review tfoot th {
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 700;
  border-top: 1px solid #e9e9e9;
  padding-top: 15px;
}
.checkout-form .order-confirm-sheet .order-review .product-review tfoot td {
  text-align: left;
  font-weight: 500;
  font-size: 18px;
  border-top: 1px solid #e9e9e9;
  padding-top: 15px;
}
.checkout-form .order-confirm-sheet .order-review .payment-list {
  padding: 30px 0 15px;
  border-bottom: 1px solid #e9e9e9;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li {
  padding: 0 30px 12px 0;
  position: relative;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li p {
  font-size: 16px;
  line-height: 22px;
  margin-bottom: 12px;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li label {
  position: relative;
  font-weight: 500;
  font-size: 18px;
  line-height: 15px;
  color: #19352D;
  cursor: pointer;
  margin: 0 0 13px;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li label:before {
  content: "";
  width: 15px;
  height: 15px;
  line-height: 14px;
  font-weight: 700;
  border-radius: 50%;
  border: 1px solid #d5d5d5;
  font-size: 10px;
  text-align: center;
  position: absolute;
  right: -30px;
  top: 0;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li input[type=radio] {
  position: absolute;
  opacity: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  cursor: pointer;
}
.checkout-form .order-confirm-sheet .order-review .payment-list li input:checked + label:before {
  content: "\f272";
  font-family: bootstrap-icons !important;
  background: #373737;
  color: #fff;
  border-color: #373737;
}
.checkout-form .order-confirm-sheet .policy-text {
  font-size: 16px;
  line-height: 22px;
  color: #979797;
  padding: 25px 0 5px;
}
.checkout-form .order-confirm-sheet .agreement-checkbox label {
  position: relative;
  font-weight: 500;
  font-size: 15px;
  line-height: 22px;
  color: #19352D;
  cursor: pointer;
  padding-right: 33px;
  margin-bottom: 35px;
}
.checkout-form .order-confirm-sheet .agreement-checkbox label:before {
  content: "";
  width: 15px;
  height: 15px;
  line-height: 14px;
  border-radius: 2px;
  border: 1px solid #d5d5d5;
  font-size: 10px;
  font-weight: 700;
  text-align: center;
  position: absolute;
  right: 0;
  top: 3px;
}
.checkout-form .order-confirm-sheet .agreement-checkbox input[type=checkbox] {
  display: none;
}
.checkout-form .order-confirm-sheet .agreement-checkbox input[type=checkbox]:checked + label:before {
  content: "\f272";
  font-family: bootstrap-icons !important;
  background: #373737;
  color: #fff;
  border-color: #373737;
}
.checkout-form .credit-card-form {
  margin-top: 12px;
  display: none;
}
.checkout-form .credit-card-form h6 {
  font-size: 15px;
  margin-bottom: 5px;
}
.checkout-form .credit-card-form input {
  width: 100%;
  height: 40px;
  font-size: 14px;
  border: 1px solid rgba(0, 0, 0, 0.07);
  padding: 0 10px;
  border-radius: 3px;
  margin-bottom: 18px;
}
.checkout-form .credit-card-form span {
  padding: 0 5px;
  margin-bottom: 18px;
}

body.compensate-for-scrollbar {
  overflow: hidden;
}

.fancybox-active {
  height: auto;
}

.fancybox-is-hidden {
  left: -9999px;
  margin: 0;
  position: absolute !important;
  top: -9999px;
  visibility: hidden;
}

.fancybox-container {
  backface-visibility: hidden;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif;
  height: 100%;
  left: 0;
  position: fixed;
  -webkit-tap-highlight-color: transparent;
  top: 0;
  transform: translateZ(0);
  width: 100%;
  z-index: 99992;
}

.fancybox-container * {
  box-sizing: border-box;
}

.fancybox-bg, .fancybox-inner, .fancybox-outer, .fancybox-stage {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.fancybox-outer {
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
}

.fancybox-bg {
  background: #1e1e1e;
  opacity: 0;
  transition-duration: inherit;
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.47, 0, 0.74, 0.71);
}

.fancybox-is-open .fancybox-bg {
  opacity: 0.87;
  transition-timing-function: cubic-bezier(0.22, 0.61, 0.36, 1);
}

.fancybox-caption, .fancybox-infobar, .fancybox-navigation .fancybox-button, .fancybox-toolbar {
  direction: ltr;
  opacity: 0;
  position: absolute;
  transition: opacity 0.25s, visibility 0s linear 0.25s;
  visibility: hidden;
  z-index: 99997;
}

.fancybox-show-caption .fancybox-caption, .fancybox-show-infobar .fancybox-infobar, .fancybox-show-nav .fancybox-navigation .fancybox-button, .fancybox-show-toolbar .fancybox-toolbar {
  opacity: 1;
  transition: opacity 0.25s, visibility 0s;
  visibility: visible;
}

.fancybox-infobar {
  color: #ccc;
  font-size: 13px;
  -webkit-font-smoothing: subpixel-antialiased;
  height: 44px;
  left: 0;
  line-height: 44px;
  min-width: 44px;
  mix-blend-mode: difference;
  padding: 0 10px;
  pointer-events: none;
  text-align: center;
  top: 0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.fancybox-toolbar {
  right: 0;
  top: 0;
}

.fancybox-stage {
  direction: ltr;
  overflow: visible;
  -webkit-transform: translateZ(0);
  z-index: 99994;
}

.fancybox-is-open .fancybox-stage {
  overflow: hidden;
}

.fancybox-slide {
  backface-visibility: hidden;
  display: none;
  height: 100%;
  left: 0;
  outline: none;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  padding: 44px;
  position: absolute;
  text-align: center;
  top: 0;
  transition-property: transform, opacity;
  white-space: normal;
  width: 100%;
  z-index: 99994;
}

.fancybox-slide:before {
  content: "";
  display: inline-block;
  height: 100%;
  margin-right: -0.25em;
  vertical-align: middle;
  width: 0;
}

.fancybox-is-sliding .fancybox-slide, .fancybox-slide--current, .fancybox-slide--next, .fancybox-slide--previous {
  display: block;
}

.fancybox-slide--next {
  z-index: 99995;
}

.fancybox-slide--image {
  overflow: visible;
  padding: 44px 0;
}

.fancybox-slide--image:before {
  display: none;
}

.fancybox-slide--html {
  padding: 6px 6px 0;
}

.fancybox-slide--iframe {
  padding: 44px 44px 0;
}

.fancybox-content {
  background: #fff;
  display: inline-block;
  margin: 0 0 6px;
  max-width: 100%;
  overflow: auto;
  padding: 0;
  padding: 24px;
  position: relative;
  text-align: left;
  vertical-align: middle;
}

.fancybox-slide--image .fancybox-content {
  animation-timing-function: cubic-bezier(0.5, 0, 0.14, 1);
  backface-visibility: hidden;
  background: transparent;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  left: 0;
  margin: 0;
  max-width: none;
  overflow: visible;
  padding: 0;
  position: absolute;
  top: 0;
  transform-origin: top left;
  transition-property: transform, opacity;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  z-index: 99995;
}

.fancybox-can-zoomOut .fancybox-content {
  cursor: zoom-out;
}

.fancybox-can-zoomIn .fancybox-content {
  cursor: zoom-in;
}

.fancybox-can-drag .fancybox-content {
  cursor: grab;
}

.fancybox-is-dragging .fancybox-content {
  cursor: grabbing;
}

.fancybox-container [data-selectable=true] {
  cursor: text;
}

.fancybox-image, .fancybox-spaceball {
  background: transparent;
  border: 0;
  height: 100%;
  left: 0;
  margin: 0;
  max-height: none;
  max-width: none;
  padding: 0;
  position: absolute;
  top: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  width: 100%;
}

.fancybox-spaceball {
  z-index: 1;
}

.fancybox-slide--html .fancybox-content {
  margin-bottom: 6px;
}

.fancybox-slide--iframe .fancybox-content, .fancybox-slide--map .fancybox-content, .fancybox-slide--video .fancybox-content {
  height: 100%;
  margin: 0;
  overflow: visible;
  padding: 0;
  width: 100%;
}

.fancybox-slide--video .fancybox-content {
  background: #000;
}

.fancybox-slide--map .fancybox-content {
  background: #e5e3df;
}

.fancybox-slide--iframe .fancybox-content {
  background: #fff;
  height: calc(100% - 44px);
  margin-bottom: 44px;
}

.fancybox-iframe, .fancybox-video {
  background: transparent;
  border: 0;
  height: 100%;
  margin: 0;
  overflow: hidden;
  padding: 0;
  width: 100%;
}

.fancybox-iframe {
  vertical-align: top;
}

.fancybox-error {
  background: #fff;
  cursor: default;
  max-width: 400px;
  padding: 40px;
  width: 100%;
}

.fancybox-error p {
  color: #444;
  font-size: 16px;
  line-height: 20px;
  margin: 0;
  padding: 0;
}

.fancybox-button {
  background: rgba(30, 30, 30, 0.6);
  border: 0;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  height: 44px;
  margin: 0;
  outline: none;
  padding: 10px;
  transition: color 0.2s;
  vertical-align: top;
  width: 44px;
}

.fancybox-button, .fancybox-button:link, .fancybox-button:visited {
  color: #ccc;
}

.fancybox-button:focus, .fancybox-button:hover {
  color: #fff;
}

.fancybox-button.disabled, .fancybox-button.disabled:hover, .fancybox-button[disabled], .fancybox-button[disabled]:hover {
  color: #888;
  cursor: default;
}

.fancybox-button svg {
  display: block;
  overflow: visible;
  position: relative;
  shape-rendering: geometricPrecision;
}

.fancybox-button svg path {
  fill: transparent;
  stroke: currentColor;
  stroke-linejoin: round;
  stroke-width: 3;
}

.fancybox-button--pause svg path:nth-child(1), .fancybox-button--play svg path:nth-child(2) {
  display: none;
}

.fancybox-button--play svg path, .fancybox-button--share svg path, .fancybox-button--thumbs svg path {
  fill: currentColor;
}

.fancybox-button--share svg path {
  stroke-width: 1;
}

.fancybox-navigation .fancybox-button {
  height: 38px;
  opacity: 0;
  padding: 6px;
  position: absolute;
  top: 50%;
  width: 38px;
}

.fancybox-show-nav .fancybox-navigation .fancybox-button {
  transition: opacity 0.25s, visibility 0s, color 0.25s;
}

.fancybox-navigation .fancybox-button:after {
  content: "";
  left: -25px;
  padding: 50px;
  position: absolute;
  top: -25px;
}

.fancybox-navigation .fancybox-button--arrow_left {
  left: 6px;
}

.fancybox-navigation .fancybox-button--arrow_right {
  right: 6px;
}

.fancybox-close-small {
  background: transparent;
  border: 0;
  border-radius: 0;
  color: #555;
  cursor: pointer;
  height: 44px;
  margin: 0;
  padding: 6px;
  position: absolute;
  right: 0;
  top: 0;
  width: 44px;
  z-index: 10;
}

.fancybox-close-small svg {
  fill: transparent;
  opacity: 0.8;
  stroke: currentColor;
  stroke-width: 1.5;
  transition: stroke 0.1s;
}

.fancybox-close-small:focus {
  outline: none;
}

.fancybox-close-small:hover svg {
  opacity: 1;
}

.fancybox-slide--iframe .fancybox-close-small, .fancybox-slide--image .fancybox-close-small, .fancybox-slide--video .fancybox-close-small {
  color: #ccc;
  padding: 5px;
  right: -12px;
  top: -44px;
}

.fancybox-slide--iframe .fancybox-close-small:hover svg, .fancybox-slide--image .fancybox-close-small:hover svg, .fancybox-slide--video .fancybox-close-small:hover svg {
  background: transparent;
  color: #fff;
}

.fancybox-is-scaling .fancybox-close-small, .fancybox-is-zoomable.fancybox-can-drag .fancybox-close-small {
  display: none;
}

.fancybox-caption {
  bottom: 0;
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  left: 0;
  line-height: 1.5;
  padding: 25px 44px;
  right: 0;
}

.fancybox-caption:before {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAEtCAQAAABjBcL7AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAHRJREFUKM+Vk8EOgDAIQ0vj/3+xBw8qIZZueFnIKC90MCAI8DlrkHGeqqGIU6lVigrBtpCWqeRWoHDNqs0F7VNVBVxmHRlvoVqjaYkdnDIaivH2HqZ5+oZj3JUzWB+cOz4G48Bg+tsJ/tqu4dLC/4Xb+0GcF5BwBC0AA53qAAAAAElFTkSuQmCC);
  background-repeat: repeat-x;
  background-size: contain;
  bottom: 0;
  content: "";
  display: block;
  left: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: -25px;
  z-index: -1;
}

.fancybox-caption:after {
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.3);
  content: "";
  display: block;
  left: 44px;
  position: absolute;
  right: 44px;
  top: 0;
}

.fancybox-caption a, .fancybox-caption a:link, .fancybox-caption a:visited {
  color: #ccc;
  text-decoration: none;
}

.fancybox-caption a:hover {
  color: #fff;
  text-decoration: underline;
}

.fancybox-loading {
  animation: a 0.8s infinite linear;
  background: transparent;
  border: 6px solid hsla(0, 0%, 39%, 0.5);
  border-radius: 100%;
  border-top-color: #fff;
  height: 60px;
  left: 50%;
  margin: -30px 0 0 -30px;
  opacity: 0.6;
  padding: 0;
  position: absolute;
  top: 50%;
  width: 60px;
  z-index: 99999;
}
@keyframes a {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}
.fancybox-animated {
  transition-timing-function: cubic-bezier(0, 0, 0.25, 1);
}

.fancybox-fx-slide.fancybox-slide--previous {
  opacity: 0;
  transform: translate3d(-100%, 0, 0);
}

.fancybox-fx-slide.fancybox-slide--next {
  opacity: 0;
  transform: translate3d(100%, 0, 0);
}

.fancybox-fx-slide.fancybox-slide--current {
  opacity: 1;
  transform: translateZ(0);
}

.fancybox-fx-fade.fancybox-slide--next, .fancybox-fx-fade.fancybox-slide--previous {
  opacity: 0;
  transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
}

.fancybox-fx-fade.fancybox-slide--current {
  opacity: 1;
}

.fancybox-fx-zoom-in-out.fancybox-slide--previous {
  opacity: 0;
  transform: scale3d(1.5, 1.5, 1.5);
}

.fancybox-fx-zoom-in-out.fancybox-slide--next {
  opacity: 0;
  transform: scale3d(0.5, 0.5, 0.5);
}

.fancybox-fx-zoom-in-out.fancybox-slide--current {
  opacity: 1;
  transform: scaleX(1);
}

.fancybox-fx-rotate.fancybox-slide--previous {
  opacity: 0;
  transform: rotate(-1turn);
}

.fancybox-fx-rotate.fancybox-slide--next {
  opacity: 0;
  transform: rotate(1turn);
}

.fancybox-fx-rotate.fancybox-slide--current {
  opacity: 1;
  transform: rotate(0deg);
}

.fancybox-fx-circular.fancybox-slide--previous {
  opacity: 0;
  transform: scale3d(0, 0, 0) translate3d(-100%, 0, 0);
}

.fancybox-fx-circular.fancybox-slide--next {
  opacity: 0;
  transform: scale3d(0, 0, 0) translate3d(100%, 0, 0);
}

.fancybox-fx-circular.fancybox-slide--current {
  opacity: 1;
  transform: scaleX(1) translateZ(0);
}

.fancybox-fx-tube.fancybox-slide--previous {
  transform: translate3d(-100%, 0, 0) scale(0.1) skew(-10deg);
}

.fancybox-fx-tube.fancybox-slide--next {
  transform: translate3d(100%, 0, 0) scale(0.1) skew(10deg);
}

.fancybox-fx-tube.fancybox-slide--current {
  transform: translateZ(0) scale(1);
}

.fancybox-share {
  background: #f4f4f4;
  border-radius: 3px;
  max-width: 90%;
  padding: 30px;
  text-align: center;
}

.fancybox-share h1 {
  color: #222;
  font-size: 35px;
  font-weight: 700;
  margin: 0 0 20px;
}

.fancybox-share p {
  margin: 0;
  padding: 0;
}

.fancybox-share__button {
  border: 0;
  border-radius: 3px;
  display: inline-block;
  font-size: 14px;
  font-weight: 700;
  line-height: 40px;
  margin: 0 5px 10px;
  min-width: 130px;
  padding: 0 15px;
  text-decoration: none;
  transition: all 0.2s;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  white-space: nowrap;
}

.fancybox-share__button:link, .fancybox-share__button:visited {
  color: #fff;
}

.fancybox-share__button:hover {
  text-decoration: none;
}

.fancybox-share__button--fb {
  background: #3b5998;
}

.fancybox-share__button--fb:hover {
  background: #344e86;
}

.fancybox-share__button--pt {
  background: #bd081d;
}

.fancybox-share__button--pt:hover {
  background: #aa0719;
}

.fancybox-share__button--tw {
  background: #1da1f2;
}

.fancybox-share__button--tw:hover {
  background: #0d95e8;
}

.fancybox-share__button svg {
  height: 25px;
  margin-right: 7px;
  position: relative;
  top: -1px;
  vertical-align: middle;
  width: 25px;
}

.fancybox-share__button svg path {
  fill: #fff;
}

.fancybox-share__input {
  background: transparent;
  border: 0;
  border-bottom: 1px solid #d7d7d7;
  border-radius: 0;
  color: #5d5b5b;
  font-size: 14px;
  margin: 10px 0 0;
  outline: none;
  padding: 10px 15px;
  width: 100%;
}

.fancybox-thumbs {
  background: #fff;
  bottom: 0;
  display: none;
  margin: 0;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  padding: 2px 2px 4px;
  position: absolute;
  right: 0;
  -webkit-tap-highlight-color: transparent;
  top: 0;
  width: 212px;
  z-index: 99995;
}

.fancybox-thumbs-x {
  overflow-x: auto;
  overflow-y: hidden;
}

.fancybox-show-thumbs .fancybox-thumbs {
  display: block;
}

.fancybox-show-thumbs .fancybox-inner {
  right: 212px;
}

.fancybox-thumbs > ul {
  font-size: 0;
  height: 100%;
  list-style: none;
  margin: 0;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;
  position: absolute;
  position: relative;
  white-space: nowrap;
  width: 100%;
}

.fancybox-thumbs-x > ul {
  overflow: hidden;
}

.fancybox-thumbs-y > ul::-webkit-scrollbar {
  width: 7px;
}

.fancybox-thumbs-y > ul::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

.fancybox-thumbs-y > ul::-webkit-scrollbar-thumb {
  background: #2a2a2a;
  border-radius: 10px;
}

.fancybox-thumbs > ul > li {
  backface-visibility: hidden;
  cursor: pointer;
  float: left;
  height: 75px;
  margin: 2px;
  max-height: calc(100% - 8px);
  max-width: calc(50% - 4px);
  outline: none;
  overflow: hidden;
  padding: 0;
  position: relative;
  -webkit-tap-highlight-color: transparent;
  width: 100px;
}

.fancybox-thumbs-loading {
  background: rgba(0, 0, 0, 0.1);
}

.fancybox-thumbs > ul > li {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.fancybox-thumbs > ul > li:before {
  border: 4px solid #4ea7f9;
  bottom: 0;
  content: "";
  left: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 99991;
}

.fancybox-thumbs .fancybox-thumbs-active:before {
  opacity: 1;
}

@media (max-width: 800px) {
  .fancybox-thumbs {
    width: 110px;
  }
  .fancybox-show-thumbs .fancybox-inner {
    right: 110px;
  }
  .fancybox-thumbs > ul > li {
    max-width: calc(100% - 10px);
  }
}
/* Slider */
.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  margin: 0 -15px;
}

.slick-dots {
  list-style-type: none;
  display: flex;
}

.slick-dots li {
  display: inline-block;
}

.slick-dots li button {
  text-indent: -50000px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 5px;
  background: #E4E4E4;
  transition: all 0.3s ease-in-out;
}

.slick-dots .slick-active button {
  background: #0C3A30;
}

.slick-list {
  position: relative;
  display: block;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.slick-list:focus {
  outline: none !important;
}

.slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
  transform: translate3d(0, 0, 0);
}

.slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.slick-track:before,
.slick-track:after {
  display: table;
  content: "";
}

.slick-track:after {
  clear: both;
}

.slick-loading .slick-track {
  visibility: hidden;
}

.slick-slide {
  display: none;
  float: left;
  margin: 0 15px;
  height: 100%;
  min-height: 1px;
}

[dir=rtl] .slick-slide {
  float: right;
}

.slick-slide img {
  display: block;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block;
}

.slick-loading .slick-slide {
  visibility: hidden;
}

.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
  display: none;
}/*# sourceMappingURL=style.css.map */