.shop-filter-one {
   .theme-select-menu {
    width: 100%;
    min-width: 145px;
    height: 45px;
    font-size: 17px;
    padding: 0 12px;
    box-shadow: none;
    outline: none;
    border-radius: 5px;
    border: 1px solid rgba($color: #000000, $alpha: 0.1);
   } 
}

// Product Section One
.product-block-one {
    overflow: hidden;
    .img-holder {
        position: relative;
        background: #F5F5F5;
        z-index: 5;
        margin-bottom: 22px;
        .cart-icon {
            display: block;
            width: 35px;
            height: 35px;
            line-height: 35px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.09);
            border-radius: 50%;
            background: #fff;
            color: #000;
            text-align: center;
            position: absolute;
            left: 20px;
            top: 20px;
            font-size: 18px;
            opacity: 0;
            transform: scale(0.5);
            @include transition(0.2s);
            &:hover {
                color: $heading; 
                background: $color-one;
            }
        }
        .cart-button {
            font-size: 14px;
            font-weight: 700;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            position: absolute;
            left: 20px;
            right: 20px;
            bottom: 20px;
            line-height: 48px;
            background: $color-two;
            text-align: center;
            color: #fff;
            opacity: 0;
            transform: translateY(10px);
            @include transition(0.4s);
            &:hover {
                color: $heading;
                background: $color-one;
            }
          }
    }
    .product-title {
        font-weight: 500;
        color: $heading;
        font-size: 20px;
        text-transform: capitalize;
    }
    .rating {
        margin: 0 -3px;
        li {
            font-size: 15px; 
            margin: 0 3px;
            color: #B3B3B3;
            .bi-star-fill {color: #FFCB65;}
        }
    }
    .price {
        font-weight: 500;
        font-size: 22px; 
        color: $color-five; 
        margin-top: 10px;
    }
    &:hover {
        .cart-button {
            opacity: 1; 
            transform: translateY(0);
        }
        .product-img {
            transform: scale(0.95);
        }
        .cart-icon {
            opacity: 1; 
            transform: scale(1);
        }
        .product-title {
            text-decoration: underline;
        }
    }
}
// Product Details One
.product-details-one {
    .product-img-tab {
        border: none;
        .nav-link {
            width: 100%;
            background: #F6F6F6;
            border: 1px solid transparent;
            border-radius: 0;
            padding: 5px 0;
            height: 88px;
            margin-bottom: 12px;
            img {
                max-height: 100%;
            }
            &.active {
                background: #fff;
                border-color: #000;
            }
        }
    }
    .product-img-tab-content {
        background: #F6F6F6;
        padding: 20px;
        img {
            margin: auto;
        }
    }
    .product-info {
        .stock-tag {
            display: inline-block;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            background: #EAEAEA;
            border-radius: 3px;
            line-height: 25px;
            padding: 0 10px;
        }
        .product-name {
            text-transform: capitalize;
            font-size: 32px;
            margin: 25px 0 15px;
        }
        .rating li {
            font-size: 15px; 
            margin-right: 6px;
            color: #B3B3B3;
            .bi-star-fill {color: #FFCB65;}
            a {
                font-size: 17px;
                color: #000;
                margin-left: 12px;
                &:hover {
                    text-decoration: underline;
                }
            }
        }
        .price {
            font-size: 26px;
            font-weight: 700;
            color: $color-two;
            padding: 25px 0 5px;
            del {
                font-size: 0.7em;
                opacity: 0.6;
            }
        }
        .availability {
            color: #989CA2; 
            font-size: 17px;
        }
        .description-text {
            padding: 10px 0 15px;
        }
        .product-feature {
            margin-bottom: 20px;
            li {
                color: #000;
                position: relative;
                padding-right: 30px;
                margin-bottom: 8px;
                &:before {
                    content: '\F633';
                    position: absolute;
                    font-family: $bootstrapFont;
                    top:2px;
                    right: 0;
                    color: $color-two;
                }
            }
        }
        .customize-order {
            h6 {
                font-size: 18px;
                margin: 0 0 0 15px;
            }
            .quantity .button-group {
                border: 1px solid #e3e3e3;
                display: inline-block;
                li {
                    line-height: 40px;
                    max-height: 40px;
                    button {
                        font-size: 25px;
                        color: #C9C9C9;
                        background: transparent;
                        width: 32px;
                    }
                    .product-value {
                        font-size: 18px;
                        font-weight: 500;
                        height: 40px;
                        color: #000;
                        max-width: 45px;
                        background: transparent;
                        border: none;
                        text-align: center;
                        padding-left: 5px;
                    }
                }
            }
        }
    }
    .product-review-tab {
        .nav-tabs {
            border-bottom: 1px solid #EBEBEB;
            .nav-link {
                font-size: 16px;
                font-weight: 700;
                text-transform: uppercase;
                color: $heading;
                letter-spacing: 1px;
                padding: 0 0 12px 0;
                position: relative;
                margin: 0 0 0 50px;
                border: none;
                border-radius: 0;
                &:before {
                    content: '';
                    position: absolute;
                    width: 100%;
                    height: 2px;
                    left: 0;
                    bottom: -1px;
                    z-index: 1;
                    background: #000;
                    transform: scale(0 , 1);
                    @include transition(0.3s);
                }
                &.active:before {transform: scale(1);}
            }
            .nav-item:last-child .nav-link {margin-left: 0;}
        }
        .tab-content {
            h5 {
                font-weight: 700;
                font-size: 20px;
                margin-bottom: 18px;
            }
            .product-feature li {
                position: relative;
                padding-right: 30px;
                margin-bottom: 18px;
                &:before {
                    content: '\F633';
                    position: absolute;
                    font-family: $bootstrapFont;
                    top:2px;
                    right: 0;
                    color: $color-two;
                }
            }
        }
        .user-comment-area {
            .single-comment {
                padding-bottom: 40px;
                &:last-child {padding-bottom: 0;}
            }
            .user-img {
                width: 60px;
                height: 60px;
                border-radius: 50%;
            }
            .user-comment-data {
                padding-left: 30px;
                position: relative;
                .name {
                    font-size: 18px;
                    margin-bottom: 4px;
                }
                .rating li {
                    font-size: 14px; 
                    margin: 0 3px;
                    color: #B3B3B3;
                    .bi-star-fill {color: #FFCB65;}
                }
                p {
                    padding-top: 8px; 
                }
            }
        }
    }
}
// Cart Page
.cart-list-form {
    position: relative;
    .table {
        margin: 0; 
        background: transparent;
        th {
            font-size: 15px;
            font-weight: 700;
            letter-spacing: 2px;
            border: none;
            border-bottom: 2px solid #000;
            padding: 0 0 30px;
            text-align: center;
            text-transform: uppercase;
            color: $heading;
            background: transparent;
            &:first-child {text-align: right;}
        }
        tbody {
            td {
                padding: 0 0 70px; 
                border: none; 
                vertical-align: middle;
                text-align: center;
                background: transparent;
            }
            tr:first-child td {padding-top: 60px;}
            tr:last-child td {padding-bottom: 40px;}
            .product-thumbnails {
                width: 85px;
                img {
                    max-height: 100%; 
                    max-width: none;
                }
                .product-img {
                    display: block; 
                    max-width: 85px; 
                    max-height: 85px; 
                    overflow: hidden;
                }
            }
            .product-info {
                padding-right: 30px;
                text-align: right;
                .product-name {
                    font-weight: 700;
                    font-size: 20px;
                    color: $heading;
                }
                .serial {
                    font-size: 15px;
                    color: rgba(31,31,31,0.5);
                    padding-bottom: 6px;
                }
                ul li {
                    display: inline-block;
                    font-size: 16px;
                    color: #000;
                    padding-right: 15px;
                }
            }
            .price {
                font-weight: 500;
                font-size: 18px;
                color: $heading;
            }
            .quantity {
                li {
                    display: inline-block;
                    line-height: 40px;
                    max-height: 40px;
                    .btn {
                        font-size: 24px;
                        padding: 0;
                        border: none;
                        vertical-align: inherit;
                        color: #1d1d1d;
                        background: transparent;
                    }
                    .product-value {
                        font-size: 18px;
                        font-weight: 500;
                        color: #1d1d1d;
                        max-width: 55px;
                        background: transparent;
                        border: none;
                        text-align: center;
                        padding-left: 12px;
                    }
                }
            }
            .remove-product {
                color: #000; 
                font-size: 22px;
                &:hover {
                    color: #ff2759;
                }
            }
        }
    }
    .cart-footer {
        border-top: 2px solid #545454;
        margin-top: 28px;
        padding-top: 40px;
        .coupon-form input {
            width: 240px;
            height: 50px;
            box-shadow: none;
            outline: none;
            border:none;
            border-bottom: 2px solid #545454;
            font-size: 18px;
            margin-left: 30px;
            background: transparent;
        }
        .cart-total-section {
            text-align: left;
            padding-left: 82px;
            .cart-total-table tr {
                th {
                    font-size: 18px;
                    font-weight: 500;
                    color: rgba(29,29,29,0.5);
                    font-weight: normal;
                    padding-left: 26px;
                    padding-bottom: 16px;
                }
                td {
                    font-size:18px;
                    font-weight: 500;
                    color: $heading;
                    padding-bottom: 16px;
                }
            }
        }
    }
}
// Checkout
.checkout-toggle-area {
	p {
		margin-bottom: 10px;
		button {
			font-weight: 500;
			letter-spacing: 0px;
			color: $heading;
			background: transparent;
			display: inline-block;
			text-decoration: underline;
		}
	}
	form {
		input {
			width: 100%;
			height: 60px;
			font-size: 18px;
			border: none;
			border-radius: 8px;
			padding: 0 30px;
			margin-bottom: 20px;
			&:focus {
				border-color: #777;
			}
		}
		.lost-passw {
			color: #636067;
			font-size: 0.8em;
			margin: 12px 0 35px;
			&:hover {
				text-decoration: underline;
			}
		}
		button {
			line-height: 50px;
		}
		p {
            font-size: 0.9em;
			padding-top: 15px;
		}
	}
}
.checkout-form {
	.main-title {
		font-size: 28px;
		padding-bottom: 55px;
	}
	.single-input-wrapper {
		display: block;
		width: 100%;
		height: 60px;
		font-size: 18px;
		border: none;
		padding: 0 15px;
		border: none;
        border-radius: 8px;
		margin-bottom: 55px;
	}
	.theme-select-menu {
		display: block;
		width: 100%;
		height: 60px;
		font-size: 18px;
		border: none;
		padding: 0 15px;
		border: none;
        box-shadow: none;
        outline: none;
        border-radius: 8px;
		margin-bottom: 55px;
        
        option {
            font-size: 0.85em;
        }
	}
	.checkbox-list {
		padding-bottom: 44px;
		li {
			label {
				position: relative;
				font-weight: 500;
				font-size: 17px;
				line-height: 15px;
				padding-left: 28px;
				color: $heading;
				cursor: pointer;
				margin: 0 0 24px;
				&:before {
					content: '';
					width: 15px;
					height: 15px;
					line-height: 15px;
					border-radius: 2px;
					border: 1px solid #000;
					font-size: 12px;
					text-align: center;
					position: absolute;
					left: 0;
					top: 0;
				}
			}
			input[type="checkbox"] {
				display: none;
			}
			input[type="checkbox"]:checked {
				& + label {
					&:before {
						content: "\f272";
						font-family: bootstrap-icons !important;
						background: #373737;
						color: #fff;
						border-color: #373737;
					}
				}
			}
		}
	}
	.other-note-area {
		p {
            font-weight: 500;
			font-size: 16px;
			color: $heading;
			margin-bottom: 6px;
		}
		textarea {
			width: 100%;
			border: none;
			padding: 15px;
			resize: none;
			height: 145px;
		}
	}
	.order-confirm-sheet {
		.order-review {
			background: #fff;
			padding: 50px 40px;
			.product-review {
				width: 100%;
				tbody {
                    color: $heading;
					th {
                        padding-bottom: 15px;
						span {
							font-weight: 500;
							font-size: 18px;
						}
					}
					td {
                        padding-bottom: 15px;
						font-size: 18px;
                        font-weight: 500;
						text-align: left;
					}
				}
				tfoot {
					th {
						font-size: 16px;
						text-transform: uppercase;
						font-weight: 700;
                        border-top: 1px solid #e9e9e9;
	                    padding-top: 15px;
					}
					td {
						text-align: left;
                        font-weight: 500;
						font-size: 18px;
                        border-top: 1px solid #e9e9e9;
	                    padding-top: 15px;
					}
				}
			}
			.payment-list {
				padding: 30px 0 15px;
				border-bottom: 1px solid #e9e9e9;
				li {
					padding: 0 30px 12px 0;
					position: relative;
					p {
						font-size: 16px;
						line-height: 22px;
						margin-bottom: 12px;
					}
					label {
						position: relative;
						font-weight: 500;
						font-size: 18px;
						line-height: 15px;
						color: $heading;
						cursor: pointer;
						margin: 0 0 13px;
						&:before {
							content: '';
							width: 15px;
							height: 15px;
							line-height: 14px;
							font-weight: 700;
							border-radius: 50%;
							border: 1px solid #d5d5d5;
							font-size: 10px;
							text-align: center;
							position: absolute;
							right: -30px;
							top: 0;
						}
					}
					input[type="radio"] {
						position: absolute;
						opacity: 0;
						z-index: 1;
						width: 100%;
						height: 100%;
						cursor: pointer;
					}
					input {
						&:checked {
							& + label {
								&:before {
									content: "\f272";
									font-family: bootstrap-icons !important;
									background: #373737;
									color: #fff;
									border-color: #373737;
								}
							}
						}
					}
				}
			}
		}
		.policy-text {
			font-size: 16px;
			line-height: 22px;
			color: #979797;
			padding: 25px 0 5px;
		}
		.agreement-checkbox {
			label {
				position: relative;
                font-weight: 500;
				font-size: 15px;
				line-height: 22px;
				color: $heading;
				cursor: pointer;
				padding-right: 33px;
				margin-bottom: 35px;
				&:before {
					content: '';
					width: 15px;
					height: 15px;
					line-height: 14px;
					border-radius: 2px;
					border: 1px solid #d5d5d5;
					font-size: 10px;
					font-weight: 700;
					text-align: center;
					position: absolute;
					right: 0;
					top: 3px;
				}
			}
			input[type="checkbox"] {
				display: none;
			}
			input[type="checkbox"]:checked {
				& + label {
					&:before {
						content: "\f272";
						font-family: bootstrap-icons !important;
						background: #373737;
						color: #fff;
						border-color: #373737;
					}
				}
			}
		}
	}
	.credit-card-form {
		margin-top: 12px;
		display: none;
		h6 {
			font-size: 15px;
			margin-bottom: 5px;
		}
		input {
			width: 100%;
			height: 40px;
			font-size: 14px;
			border: 1px solid rgba(0,0,0,0.07);
			padding: 0 10px;
			border-radius: 3px;
			margin-bottom: 18px;
		}
		span {
			padding: 0 5px;
			margin-bottom: 18px;
		}
	}
}
