// Service Details
.service-details {
    .details-meta {
        h2 {
            font-size: 64px;
            line-height: 1.1875em;
            font-weight: 700;
            margin-bottom: 42px;
        }
        p {
            margin-bottom: 35px;
        }
        h3 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 25px;
        }
        .line-wrapper {
            border-top: 1px dashed #e5e5e5;
            border-bottom: 1px dashed #e5e5e5;
        }
        .quote-wrapper {
            padding: 50px 80px 60px;
            border-radius: 30px;
            z-index: 1;
            .icon {
                margin-top: 7px;
            }
            blockquote {
                font-size: 48px;
                line-height: 1.354em;
                font-weight: 500;
                color: $heading;
                margin-bottom: 30px;
            }
            .shape_01 {
                bottom: 0;
                left: 1%;
                width: 37.2%;
            }
        }
        .list-item {
            li {
                font-size: 22px;
                font-weight: 500;
                color: $text-dark;
                padding-right: 45px;
                margin-bottom: 24px;
                position: relative;
                &:before {
                    content: '';
                    position: absolute;
                    width: 26px;
                    height: 26px;
                    border-radius: 50%;
                    background: #FFF2AC;
                    right: 0;
                    top:3px;
                }
                &:after {
                    content: '\F633';
                    position: absolute;
                    font-family: $bootstrapFont;
                    font-size: 18px;
                    top:3px;
                    right: 5px;
                    color: #000;
                }
            }
        }
    }
    .service-nav-item {
        background: $light-bg-one;
        border-radius: 20px;
        overflow: hidden;
        padding: 0 25px;
        a {
           font-weight: 500;
           color: rgba($color: #000000, $alpha: 0.4);
           padding: 24px 0;
           line-height: 20px;
           border-bottom: 1px dashed rgba($color: #0A4020, $alpha: 0.2);
           @include transition(0.3s);
           img {
            width: 20px;
            margin-left: 22px;
            opacity: 0.3;
            @include transition(0.3s);
           }
           &.active, &:hover {
            color: #0A4020;
            img {
                opacity: 1;
            }
           }
        }
        li:last-child a {
            border: none;
        }
    }
    .contact-banner {
        padding: 35px 3% 50px;
        background: #FFE86B;
        border-radius: 20px;
        h3 {
            font-size: 32px;
            line-height: 1.25em;
            color: #000;
        }
        a {
            line-height: 36px;
            border: 2px solid #000;
            border-radius: 30px;
            color: #000;
            padding: 0 30px;
            &:hover {
                background: $color-three;
                border-color: $color-three;
                color: #fff;
            }
        }
    }
}